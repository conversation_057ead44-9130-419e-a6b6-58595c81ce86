# AutoMem 🧠

<div align="center">

**智能记忆增强MCP服务器 - 让AI拥有持久记忆**

[![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Tests](https://img.shields.io/badge/tests-passing-brightgreen.svg)](tests/)
[![Coverage](https://img.shields.io/badge/coverage-85%25-yellow.svg)](htmlcov/)
[![MCP Compatible](https://img.shields.io/badge/MCP-compatible-purple.svg)](https://modelcontextprotocol.io)

[快速开始](#-快速开始) • [文档](docs/) • [示例](#-示例) • [贡献](#-贡献) • [社区](#-社区)

</div>

AutoMem是一个基于模型上下文协议(MCP)的本地智能记忆服务器，通过自主存储检索功能显著增强AI助手的记忆能力。

## 🌟 核心特性

### 🧠 智能记忆管理
- **自动存储判断**: 智能识别值得保存的信息，无需手动干预
- **智能分类标签**: 自动为记忆内容生成分类和标签
- **上下文关联**: 基于会话主题自动检索相关记忆
- **时间轴管理**: 近期记忆优先，历史记录智能归档

### 🔍 高级检索能力
- **语义搜索**: 基于内容含义而非关键词的智能搜索
- **交叉引用**: 自动发现和链接相关记忆
- **上下文感知**: 根据当前对话主题推荐相关记忆
- **多维度检索**: 支持时间、主题、重要性等多维度查询

### 🤖 自主运行
- **无感知集成**: 在后台自动工作，不影响用户体验
- **智能更新**: 自动合并和更新相关记忆
- **清理机制**: 智能清理过时和冗余信息
- **配置灵活**: 丰富的配置选项适应不同使用场景

### 🔒 本地优先
- **完全本地**: 所有数据存储在本地，保护隐私
- **离线运行**: 无需网络连接即可正常工作
- **轻量级**: 最小化资源占用，适合个人设备
- **跨平台**: 支持Windows、macOS、Linux

## 🏗️ 架构设计

```
AutoMem MCP Server
├── 🧠 Core Engine (核心引擎)
│   ├── Memory Storage (记忆存储)
│   ├── Vector Database (向量数据库)
│   ├── Metadata Manager (元数据管理)
│   └── Context Analyzer (上下文分析)
├── 🤖 Intelligence Layer (智能层)
│   ├── Auto Classifier (自动分类器)
│   ├── Tag Generator (标签生成器)
│   ├── Relevance Scorer (相关性评分)
│   └── Decision Engine (决策引擎)
├── 🔍 Retrieval System (检索系统)
│   ├── Semantic Search (语义搜索)
│   ├── Timeline Manager (时间轴管理)
│   ├── Cross Reference (交叉引用)
│   └── Context Retrieval (上下文检索)
├── 🔌 MCP Interface (MCP接口)
│   ├── Tools (工具接口)
│   ├── Resources (资源接口)
│   └── Prompts (提示符接口)
└── ⚙️ Management Layer (管理层)
    ├── Configuration (配置管理)
    ├── Logging (日志系统)
    └── Cleanup (清理机制)
```

## 🚀 快速开始

### 安装

```bash
# 克隆项目
git clone https://github.com/your-org/automem-mcp.git
cd automem-mcp

# 安装依赖
pip install -e .

# 或使用uv (推荐)
uv sync
```

### 基本使用

```bash
# 启动AutoMem MCP服务器
automem serve

# 开发模式 (带调试信息)
automem serve --dev

# 指定配置文件
automem serve --config config.yaml
```

### 集成到Claude Desktop

在Claude Desktop配置文件中添加：

```json
{
  "mcpServers": {
    "automem": {
      "command": "automem",
      "args": ["serve", "--stdio"],
      "env": {
        "AUTOMEM_CONFIG": "/path/to/config.yaml"
      }
    }
  }
}
```

### Docker 部署

```bash
# 使用 Docker Compose
docker-compose up -d

# 或使用 Docker
docker run -d -p 8000:8000 -v $(pwd)/data:/data automem:latest
```

## 💡 示例

### 存储记忆

```python
# 通过 MCP 工具调用
{
  "method": "tools/call",
  "params": {
    "name": "store_memory",
    "arguments": {
      "content": "Python是一种高级编程语言，广泛用于Web开发、数据科学和AI。",
      "memory_type": "fact",
      "importance": "high",
      "tags": ["Python", "编程", "技术"],
      "auto_process": true
    }
  }
}
```

### 搜索记忆

```python
# 语义搜索
{
  "method": "tools/call",
  "params": {
    "name": "search_memories",
    "arguments": {
      "query": "机器学习算法",
      "limit": 5,
      "similarity_threshold": 0.7,
      "memory_types": ["fact", "procedure"],
      "importance_levels": ["high", "medium"]
    }
  }
}
```

### 获取上下文

```python
# 获取相关上下文
{
  "method": "resources/read",
  "params": {
    "uri": "memory://context/深度学习"
  }
}

# 获取时间线记忆
{
  "method": "resources/read",
  "params": {
    "uri": "memory://timeline/2024-01-15"
  }
}
```

### 使用提示符

```python
# 回忆上下文提示符
{
  "method": "prompts/get",
  "params": {
    "name": "recall_context",
    "arguments": {
      "topic": "机器学习",
      "depth": "deep",
      "focus": "technical"
    }
  }
}
```

## 📖 使用指南

### MCP工具接口

AutoMem提供以下MCP工具：

- `store_memory`: 手动存储重要信息
- `search_memories`: 搜索相关记忆
- `get_context`: 获取当前对话的相关上下文
- `manage_tags`: 管理记忆标签
- `cleanup_memories`: 清理过时记忆

### 资源接口

- `memory://recent`: 最近的记忆
- `memory://context/{topic}`: 特定主题的上下文
- `memory://timeline/{date}`: 特定日期的记忆
- `memory://tags/{tag}`: 特定标签的记忆

### 提示符模板

- `recall_context`: 回忆相关上下文的提示符
- `summarize_memories`: 总结记忆的提示符
- `find_connections`: 发现记忆连接的提示符

## ⚙️ 配置

AutoMem支持丰富的配置选项：

```yaml
# config.yaml
storage:
  data_dir: "./data"
  max_memories: 10000
  cleanup_interval: "24h"

intelligence:
  embedding_model: "all-MiniLM-L6-v2"
  classification_threshold: 0.7
  auto_tag_enabled: true

retrieval:
  max_results: 10
  similarity_threshold: 0.6
  time_decay_factor: 0.1

logging:
  level: "INFO"
  file: "./logs/automem.log"
```

## 🧪 测试

```bash
# 运行所有测试
./scripts/run_tests.sh

# 运行特定类型的测试
./scripts/run_tests.sh unit          # 单元测试
./scripts/run_tests.sh integration  # 集成测试
./scripts/run_tests.sh e2e          # 端到端测试

# 生成覆盖率报告
./scripts/run_tests.sh coverage

# 并行运行快速测试
./scripts/run_tests.sh --parallel fast
```

## 🚀 部署

### 生产环境部署

```bash
# 使用 systemd 服务
sudo cp scripts/automem.service /etc/systemd/system/
sudo systemctl enable automem
sudo systemctl start automem

# 使用 Docker
docker-compose -f docker/docker-compose.prod.yml up -d

# 使用 Kubernetes
kubectl apply -f k8s/
```

### 监控和告警

- **Prometheus**: 指标收集和监控
- **Grafana**: 可视化仪表板
- **日志聚合**: 结构化日志记录

```bash
# 启动监控栈
docker-compose -f docker/docker-compose.yml --profile monitoring up -d

# 访问 Grafana 仪表板
open http://localhost:3000
```

## 🧪 开发

### 环境设置

```bash
# 克隆仓库
git clone https://github.com/your-org/automem.git
cd automem

# 安装开发依赖
pip install -r requirements.txt
pip install -e .

# 设置pre-commit钩子
pre-commit install

# 运行测试
pytest

# 代码格式化
black src/ tests/
isort src/ tests/

# 类型检查
mypy src/
```

### 项目结构

```
src/automem/
├── __init__.py
├── cli.py                 # 命令行接口
├── server.py             # MCP服务器主入口
├── config/               # 配置管理
├── core/                 # 核心引擎
├── intelligence/         # 智能层
├── retrieval/           # 检索系统
├── mcp/                 # MCP接口实现
└── utils/               # 工具函数
```

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

我们欢迎所有形式的贡献！

### 如何贡献

1. **Fork** 本仓库
2. **创建** 特性分支 (`git checkout -b feature/amazing-feature`)
3. **提交** 更改 (`git commit -m 'Add amazing feature'`)
4. **推送** 到分支 (`git push origin feature/amazing-feature`)
5. **创建** Pull Request

### 贡献指南

- 遵循 [代码规范](docs/contributing.md#代码规范)
- 编写测试用例
- 更新相关文档
- 确保所有测试通过

### 开发者资源

- [📖 开发者文档](docs/development.md)
- [🏗️ 架构设计](docs/architecture.md)
- [🔌 插件开发](docs/plugin-development.md)
- [🧪 测试指南](docs/testing.md)

## 📊 项目状态

- ✅ 核心功能完成
- ✅ MCP 协议支持
- ✅ 基础测试覆盖
- ✅ 文档完善
- 🚧 高级功能开发中
- 🚧 性能优化进行中

## 🌍 社区

- [💬 讨论区](https://github.com/your-org/automem/discussions)
- [🐛 问题反馈](https://github.com/your-org/automem/issues)
- [📧 邮件列表](mailto:<EMAIL>)
- [🐦 Twitter](https://twitter.com/automem_ai)

## 📚 文档

- [📖 完整文档](docs/README.md)
- [🚀 快速开始](docs/quickstart.md)
- [⚙️ 配置指南](docs/configuration.md)
- [🔧 API 参考](docs/api-reference.md)
- [🏗️ 架构设计](docs/architecture.md)
- [🔌 插件开发](docs/plugin-development.md)

## 🙏 致谢

感谢以下开源项目和贡献者：

- [MCP](https://modelcontextprotocol.io) - Model Context Protocol
- [ChromaDB](https://www.trychroma.com) - 向量数据库
- [Sentence Transformers](https://www.sbert.net) - 语义嵌入
- [FastAPI](https://fastapi.tiangolo.com) - 现代 Web 框架
- 所有贡献者和社区成员

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 📞 支持

- 📧 邮箱: <EMAIL>
- 💬 讨论: [GitHub Discussions](https://github.com/your-org/automem/discussions)
- 🐛 问题报告: [GitHub Issues](https://github.com/your-org/automem/issues)

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个 ⭐️**

**AutoMem** - 让AI助手拥有真正的记忆力 🧠✨

Made with ❤️ by the AutoMem Team

</div>
