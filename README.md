# AutoMem - 智能记忆增强MCP服务器

AutoMem是一个基于模型上下文协议(MCP)的本地智能记忆服务器，通过自主存储检索功能显著增强AI助手的记忆能力。

## 🌟 核心特性

### 🧠 智能记忆管理
- **自动存储判断**: 智能识别值得保存的信息，无需手动干预
- **智能分类标签**: 自动为记忆内容生成分类和标签
- **上下文关联**: 基于会话主题自动检索相关记忆
- **时间轴管理**: 近期记忆优先，历史记录智能归档

### 🔍 高级检索能力
- **语义搜索**: 基于内容含义而非关键词的智能搜索
- **交叉引用**: 自动发现和链接相关记忆
- **上下文感知**: 根据当前对话主题推荐相关记忆
- **多维度检索**: 支持时间、主题、重要性等多维度查询

### 🤖 自主运行
- **无感知集成**: 在后台自动工作，不影响用户体验
- **智能更新**: 自动合并和更新相关记忆
- **清理机制**: 智能清理过时和冗余信息
- **配置灵活**: 丰富的配置选项适应不同使用场景

### 🔒 本地优先
- **完全本地**: 所有数据存储在本地，保护隐私
- **离线运行**: 无需网络连接即可正常工作
- **轻量级**: 最小化资源占用，适合个人设备
- **跨平台**: 支持Windows、macOS、Linux

## 🏗️ 架构设计

```
AutoMem MCP Server
├── 🧠 Core Engine (核心引擎)
│   ├── Memory Storage (记忆存储)
│   ├── Vector Database (向量数据库)
│   ├── Metadata Manager (元数据管理)
│   └── Context Analyzer (上下文分析)
├── 🤖 Intelligence Layer (智能层)
│   ├── Auto Classifier (自动分类器)
│   ├── Tag Generator (标签生成器)
│   ├── Relevance Scorer (相关性评分)
│   └── Decision Engine (决策引擎)
├── 🔍 Retrieval System (检索系统)
│   ├── Semantic Search (语义搜索)
│   ├── Timeline Manager (时间轴管理)
│   ├── Cross Reference (交叉引用)
│   └── Context Retrieval (上下文检索)
├── 🔌 MCP Interface (MCP接口)
│   ├── Tools (工具接口)
│   ├── Resources (资源接口)
│   └── Prompts (提示符接口)
└── ⚙️ Management Layer (管理层)
    ├── Configuration (配置管理)
    ├── Logging (日志系统)
    └── Cleanup (清理机制)
```

## 🚀 快速开始

### 安装

```bash
# 克隆项目
git clone https://github.com/your-org/automem-mcp.git
cd automem-mcp

# 安装依赖
pip install -e .

# 或使用uv (推荐)
uv sync
```

### 基本使用

```bash
# 启动AutoMem MCP服务器
automem serve

# 开发模式 (带调试信息)
automem serve --dev

# 指定配置文件
automem serve --config config.yaml
```

### 集成到Claude Desktop

在Claude Desktop配置文件中添加：

```json
{
  "mcpServers": {
    "automem": {
      "command": "automem",
      "args": ["serve", "--stdio"]
    }
  }
}
```

## 📖 使用指南

### MCP工具接口

AutoMem提供以下MCP工具：

- `store_memory`: 手动存储重要信息
- `search_memories`: 搜索相关记忆
- `get_context`: 获取当前对话的相关上下文
- `manage_tags`: 管理记忆标签
- `cleanup_memories`: 清理过时记忆

### 资源接口

- `memory://recent`: 最近的记忆
- `memory://context/{topic}`: 特定主题的上下文
- `memory://timeline/{date}`: 特定日期的记忆
- `memory://tags/{tag}`: 特定标签的记忆

### 提示符模板

- `recall_context`: 回忆相关上下文的提示符
- `summarize_memories`: 总结记忆的提示符
- `find_connections`: 发现记忆连接的提示符

## ⚙️ 配置

AutoMem支持丰富的配置选项：

```yaml
# config.yaml
storage:
  data_dir: "./data"
  max_memories: 10000
  cleanup_interval: "24h"

intelligence:
  embedding_model: "all-MiniLM-L6-v2"
  classification_threshold: 0.7
  auto_tag_enabled: true

retrieval:
  max_results: 10
  similarity_threshold: 0.6
  time_decay_factor: 0.1

logging:
  level: "INFO"
  file: "./logs/automem.log"
```

## 🧪 开发

### 环境设置

```bash
# 安装开发依赖
pip install -e ".[dev]"

# 设置pre-commit钩子
pre-commit install

# 运行测试
pytest

# 代码格式化
black src/ tests/
isort src/ tests/

# 类型检查
mypy src/
```

### 项目结构

```
src/automem/
├── __init__.py
├── cli.py                 # 命令行接口
├── server.py             # MCP服务器主入口
├── config/               # 配置管理
├── core/                 # 核心引擎
├── intelligence/         # 智能层
├── retrieval/           # 检索系统
├── mcp/                 # MCP接口实现
└── utils/               # 工具函数
```

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

## 📞 支持

- 📧 邮箱: <EMAIL>
- 💬 讨论: [GitHub Discussions](https://github.com/your-org/automem-mcp/discussions)
- 🐛 问题报告: [GitHub Issues](https://github.com/your-org/automem-mcp/issues)

---

**AutoMem** - 让AI助手拥有真正的记忆力 🧠✨
