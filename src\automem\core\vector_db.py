"""
向量数据库实现

使用ChromaDB提供语义搜索功能。
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID
import numpy as np

try:
    import chromadb
    from chromadb.config import Settings
    from sentence_transformers import SentenceTransformer
except ImportError as e:
    raise ImportError(
        "向量数据库依赖未安装。请运行: pip install chromadb sentence-transformers"
    ) from e

from ..config.settings import IntelligenceConfig, StorageConfig
from ..utils.logging import LoggerMixin
from .memory import Memory


class VectorDatabase(LoggerMixin):
    """向量数据库"""
    
    def __init__(self, storage_config: StorageConfig, intelligence_config: IntelligenceConfig):
        self.storage_config = storage_config
        self.intelligence_config = intelligence_config
        
        # ChromaDB设置
        self.chroma_path = storage_config.data_dir / "chroma_db"
        self.collection_name = "memories"
        
        # 初始化组件
        self.client: Optional[chromadb.Client] = None
        self.collection: Optional[chromadb.Collection] = None
        self.embedding_model: Optional[SentenceTransformer] = None
    
    async def initialize(self) -> None:
        """初始化向量数据库"""
        # 确保目录存在
        self.chroma_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化ChromaDB客户端
        self.client = chromadb.PersistentClient(
            path=str(self.chroma_path),
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True,
            )
        )
        
        # 获取或创建集合
        try:
            self.collection = self.client.get_collection(name=self.collection_name)
            self.logger.info(f"加载现有向量集合: {self.collection_name}")
        except ValueError:
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "AutoMem记忆向量存储"}
            )
            self.logger.info(f"创建新向量集合: {self.collection_name}")
        
        # 初始化嵌入模型
        await self._initialize_embedding_model()
        
        self.logger.info("向量数据库初始化完成")
    
    async def _initialize_embedding_model(self) -> None:
        """初始化嵌入模型"""
        try:
            # 在线程池中加载模型以避免阻塞
            loop = asyncio.get_event_loop()
            self.embedding_model = await loop.run_in_executor(
                None,
                lambda: SentenceTransformer(self.intelligence_config.embedding_model)
            )
            self.logger.info(f"嵌入模型加载完成: {self.intelligence_config.embedding_model}")
        except Exception as e:
            self.logger.error(f"嵌入模型加载失败: {e}")
            raise
    
    async def add_memory(self, memory: Memory) -> None:
        """添加记忆到向量数据库"""
        if not self.collection or not self.embedding_model:
            raise RuntimeError("向量数据库未初始化")
        
        # 生成嵌入向量
        embedding = await self._generate_embedding(memory.content)
        
        # 准备元数据
        metadata = {
            "memory_type": memory.memory_type.value,
            "importance": memory.importance.value,
            "created_at": memory.created_at.isoformat(),
            "session_id": memory.session_id or "",
            "conversation_id": memory.conversation_id or "",
            "tags": list(memory.tags),
            "categories": list(memory.categories),
        }
        
        # 添加到集合
        self.collection.add(
            ids=[str(memory.id)],
            embeddings=[embedding.tolist()],
            documents=[memory.content],
            metadatas=[metadata]
        )
        
        # 更新记忆对象的嵌入向量
        memory.embedding = embedding.tolist()
        
        self.logger.debug(f"向量数据库添加记忆: {memory.id}")
    
    async def update_memory(self, memory: Memory) -> None:
        """更新记忆向量"""
        if not self.collection or not self.embedding_model:
            raise RuntimeError("向量数据库未初始化")
        
        # 检查记忆是否存在
        try:
            existing = self.collection.get(ids=[str(memory.id)])
            if not existing['ids']:
                # 记忆不存在，添加新的
                await self.add_memory(memory)
                return
        except Exception:
            # 记忆不存在，添加新的
            await self.add_memory(memory)
            return
        
        # 生成新的嵌入向量
        embedding = await self._generate_embedding(memory.content)
        
        # 准备元数据
        metadata = {
            "memory_type": memory.memory_type.value,
            "importance": memory.importance.value,
            "created_at": memory.created_at.isoformat(),
            "updated_at": memory.updated_at.isoformat(),
            "session_id": memory.session_id or "",
            "conversation_id": memory.conversation_id or "",
            "tags": list(memory.tags),
            "categories": list(memory.categories),
        }
        
        # 更新集合
        self.collection.update(
            ids=[str(memory.id)],
            embeddings=[embedding.tolist()],
            documents=[memory.content],
            metadatas=[metadata]
        )
        
        # 更新记忆对象的嵌入向量
        memory.embedding = embedding.tolist()
        
        self.logger.debug(f"向量数据库更新记忆: {memory.id}")
    
    async def delete_memory(self, memory_id: UUID) -> None:
        """从向量数据库删除记忆"""
        if not self.collection:
            raise RuntimeError("向量数据库未初始化")
        
        try:
            self.collection.delete(ids=[str(memory_id)])
            self.logger.debug(f"向量数据库删除记忆: {memory_id}")
        except Exception as e:
            self.logger.warning(f"删除向量记忆失败: {e}")
    
    async def search_similar(
        self,
        query: str,
        limit: int = 10,
        similarity_threshold: float = 0.6,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[Tuple[UUID, float]]:
        """搜索相似记忆"""
        if not self.collection or not self.embedding_model:
            raise RuntimeError("向量数据库未初始化")
        
        # 生成查询向量
        query_embedding = await self._generate_embedding(query)
        
        # 构建过滤条件
        where_clause = {}
        if filters:
            for key, value in filters.items():
                if isinstance(value, list):
                    # 处理列表类型的过滤条件
                    where_clause[key] = {"$in": value}
                else:
                    where_clause[key] = value
        
        # 执行搜索
        results = self.collection.query(
            query_embeddings=[query_embedding.tolist()],
            n_results=limit,
            where=where_clause if where_clause else None,
        )
        
        # 处理结果
        similar_memories = []
        if results['ids'] and results['distances']:
            for memory_id, distance in zip(results['ids'][0], results['distances'][0]):
                # 将距离转换为相似度分数 (1 - distance)
                similarity = 1.0 - distance
                
                if similarity >= similarity_threshold:
                    similar_memories.append((UUID(memory_id), similarity))
        
        # 按相似度排序
        similar_memories.sort(key=lambda x: x[1], reverse=True)
        
        self.logger.debug(f"向量搜索找到 {len(similar_memories)} 个相似记忆")
        return similar_memories
    
    async def get_memory_embedding(self, memory_id: UUID) -> Optional[List[float]]:
        """获取记忆的嵌入向量"""
        if not self.collection:
            raise RuntimeError("向量数据库未初始化")
        
        try:
            result = self.collection.get(
                ids=[str(memory_id)],
                include=['embeddings']
            )
            
            if result['embeddings'] and result['embeddings'][0]:
                return result['embeddings'][0]
        except Exception as e:
            self.logger.warning(f"获取记忆嵌入向量失败: {e}")
        
        return None
    
    async def _generate_embedding(self, text: str) -> np.ndarray:
        """生成文本嵌入向量"""
        if not self.embedding_model:
            raise RuntimeError("嵌入模型未初始化")
        
        # 在线程池中执行以避免阻塞
        loop = asyncio.get_event_loop()
        embedding = await loop.run_in_executor(
            None,
            self.embedding_model.encode,
            text
        )
        
        return embedding
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        if not self.collection:
            return {"error": "向量数据库未初始化"}
        
        try:
            count = self.collection.count()
            return {
                "total_vectors": count,
                "collection_name": self.collection_name,
                "embedding_model": self.intelligence_config.embedding_model,
            }
        except Exception as e:
            return {"error": str(e)}
    
    async def cleanup(self) -> None:
        """清理资源"""
        # ChromaDB会自动持久化，无需特殊清理
        self.logger.info("向量数据库资源清理完成")
