#!/bin/bash

# AutoMem 恢复脚本
# 从备份恢复数据和配置

set -e

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DATA_DIR="${PROJECT_DIR}/data"
BACKUP_DIR="${PROJECT_DIR}/backups"

# 默认配置
BACKUP_FILE=""
RESTORE_TYPE="full"
FORCE=false
BACKUP_DATA=true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助
show_help() {
    cat << EOF
AutoMem 恢复脚本

用法: $0 [选项] <备份文件>

选项:
  -t, --type TYPE          恢复类型 (full|data|config) [默认: full]
  -f, --force              强制恢复，不进行确认
  --no-backup              不备份现有数据
  -h, --help               显示帮助信息

恢复类型:
  full     - 完整恢复 (数据 + 配置)
  data     - 仅恢复数据
  config   - 仅恢复配置

示例:
  $0 backup.tar.gz                    # 完整恢复
  $0 -t data backup.tar.gz           # 仅恢复数据
  $0 -f backup.tar.gz                # 强制恢复，不确认

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                RESTORE_TYPE="$2"
                shift 2
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            --no-backup)
                BACKUP_DATA=false
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "$BACKUP_FILE" ]]; then
                    BACKUP_FILE="$1"
                else
                    log_error "只能指定一个备份文件"
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# 验证参数
validate_args() {
    if [[ -z "$BACKUP_FILE" ]]; then
        log_error "请指定备份文件"
        show_help
        exit 1
    fi
    
    if [[ ! -f "$BACKUP_FILE" ]]; then
        log_error "备份文件不存在: $BACKUP_FILE"
        exit 1
    fi
    
    if [[ ! "$RESTORE_TYPE" =~ ^(full|data|config)$ ]]; then
        log_error "无效的恢复类型: $RESTORE_TYPE"
        exit 1
    fi
}

# 检查备份文件
check_backup_file() {
    log_info "检查备份文件..."
    
    # 检查文件类型
    if [[ "$BACKUP_FILE" == *.tar.gz || "$BACKUP_FILE" == *.tgz ]]; then
        # 验证压缩文件
        if ! tar -tzf "$BACKUP_FILE" >/dev/null 2>&1; then
            log_error "备份文件损坏或格式不正确"
            exit 1
        fi
        log_success "压缩备份文件验证通过"
    elif [[ -d "$BACKUP_FILE" ]]; then
        log_success "目录备份验证通过"
    else
        log_error "不支持的备份文件格式"
        exit 1
    fi
}

# 显示备份信息
show_backup_info() {
    log_info "备份文件信息:"
    
    local temp_dir=""
    local info_file=""
    
    if [[ "$BACKUP_FILE" == *.tar.gz || "$BACKUP_FILE" == *.tgz ]]; then
        # 解压到临时目录查看信息
        temp_dir=$(mktemp -d)
        tar -xzf "$BACKUP_FILE" -C "$temp_dir"
        
        # 查找备份信息文件
        info_file=$(find "$temp_dir" -name "backup_info.json" | head -1)
    else
        info_file="$BACKUP_FILE/backup_info.json"
    fi
    
    if [[ -f "$info_file" ]]; then
        echo "  文件: $BACKUP_FILE"
        echo "  类型: $(jq -r '.backup_type' "$info_file" 2>/dev/null || echo "未知")"
        echo "  时间: $(jq -r '.timestamp' "$info_file" 2>/dev/null || echo "未知")"
        echo "  主机: $(jq -r '.hostname' "$info_file" 2>/dev/null || echo "未知")"
        echo "  用户: $(jq -r '.user' "$info_file" 2>/dev/null || echo "未知")"
    else
        echo "  文件: $BACKUP_FILE"
        echo "  信息: 无备份元数据"
    fi
    
    # 清理临时目录
    if [[ -n "$temp_dir" ]]; then
        rm -rf "$temp_dir"
    fi
}

# 确认恢复
confirm_restore() {
    if [[ "$FORCE" == true ]]; then
        return
    fi
    
    echo
    log_warning "恢复操作将覆盖现有数据！"
    show_backup_info
    echo
    
    read -p "确定要继续恢复吗？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "恢复操作已取消"
        exit 0
    fi
}

# 备份现有数据
backup_existing_data() {
    if [[ "$BACKUP_DATA" != true ]]; then
        return
    fi
    
    log_info "备份现有数据..."
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_name="automem_pre_restore_${timestamp}"
    local backup_path="${BACKUP_DIR}/${backup_name}"
    
    mkdir -p "$backup_path"
    
    # 备份数据目录
    if [[ -d "$DATA_DIR" && "$RESTORE_TYPE" != "config" ]]; then
        cp -r "$DATA_DIR" "$backup_path/"
        log_success "现有数据已备份到: $backup_path"
    fi
    
    # 备份配置文件
    if [[ "$RESTORE_TYPE" != "data" ]]; then
        for config in "${PROJECT_DIR}"/*.yaml "${PROJECT_DIR}"/*.yml "${PROJECT_DIR}"/*.json; do
            if [[ -f "$config" ]]; then
                cp "$config" "$backup_path/"
            fi
        done
    fi
}

# 解压备份文件
extract_backup() {
    log_info "解压备份文件..."
    
    TEMP_RESTORE_DIR=$(mktemp -d)
    
    if [[ "$BACKUP_FILE" == *.tar.gz || "$BACKUP_FILE" == *.tgz ]]; then
        tar -xzf "$BACKUP_FILE" -C "$TEMP_RESTORE_DIR"
        
        # 查找实际的备份目录
        BACKUP_CONTENT_DIR=$(find "$TEMP_RESTORE_DIR" -maxdepth 1 -type d -name "automem_*" | head -1)
        
        if [[ -z "$BACKUP_CONTENT_DIR" ]]; then
            # 如果没有找到标准目录，使用临时目录
            BACKUP_CONTENT_DIR="$TEMP_RESTORE_DIR"
        fi
    else
        # 直接使用目录
        BACKUP_CONTENT_DIR="$BACKUP_FILE"
    fi
    
    log_success "备份文件解压完成"
}

# 恢复数据
restore_data() {
    if [[ "$RESTORE_TYPE" == "config" ]]; then
        return
    fi
    
    log_info "恢复数据文件..."
    
    local backup_data_dir="$BACKUP_CONTENT_DIR/data"
    
    if [[ -d "$backup_data_dir" ]]; then
        # 删除现有数据目录
        if [[ -d "$DATA_DIR" ]]; then
            rm -rf "$DATA_DIR"
        fi
        
        # 恢复数据
        cp -r "$backup_data_dir" "$DATA_DIR"
        log_success "数据恢复完成"
    else
        log_warning "备份中没有找到数据目录"
    fi
}

# 恢复配置
restore_config() {
    if [[ "$RESTORE_TYPE" == "data" ]]; then
        return
    fi
    
    log_info "恢复配置文件..."
    
    local restored_count=0
    
    # 恢复配置文件
    for config in "$BACKUP_CONTENT_DIR"/*.yaml "$BACKUP_CONTENT_DIR"/*.yml "$BACKUP_CONTENT_DIR"/*.json; do
        if [[ -f "$config" ]]; then
            local filename=$(basename "$config")
            
            # 跳过备份元数据文件
            if [[ "$filename" == "backup_info.json" || "$filename" == "file_list.txt" ]]; then
                continue
            fi
            
            cp "$config" "$PROJECT_DIR/"
            ((restored_count++))
            log_info "恢复配置文件: $filename"
        fi
    done
    
    if [[ $restored_count -gt 0 ]]; then
        log_success "配置恢复完成，共恢复 $restored_count 个文件"
    else
        log_warning "备份中没有找到配置文件"
    fi
}

# 清理临时文件
cleanup() {
    if [[ -n "$TEMP_RESTORE_DIR" && -d "$TEMP_RESTORE_DIR" ]]; then
        rm -rf "$TEMP_RESTORE_DIR"
        log_info "清理临时文件完成"
    fi
}

# 验证恢复
verify_restore() {
    log_info "验证恢复结果..."
    
    local issues=0
    
    # 检查数据目录
    if [[ "$RESTORE_TYPE" != "config" ]]; then
        if [[ ! -d "$DATA_DIR" ]]; then
            log_error "数据目录恢复失败"
            ((issues++))
        else
            log_success "数据目录恢复验证通过"
        fi
    fi
    
    # 检查配置文件
    if [[ "$RESTORE_TYPE" != "data" ]]; then
        if [[ -f "$PROJECT_DIR/config.yaml" ]]; then
            log_success "配置文件恢复验证通过"
        else
            log_warning "主配置文件未找到"
        fi
    fi
    
    if [[ $issues -eq 0 ]]; then
        log_success "恢复验证通过"
    else
        log_error "恢复验证发现 $issues 个问题"
        exit 1
    fi
}

# 主函数
main() {
    echo "🔄 AutoMem 恢复工具"
    echo "==================="
    echo
    
    parse_args "$@"
    validate_args
    check_backup_file
    confirm_restore
    backup_existing_data
    extract_backup
    restore_data
    restore_config
    verify_restore
    cleanup
    
    log_success "恢复完成！"
    echo
    log_info "建议重启AutoMem服务以应用恢复的配置"
}

# 错误处理
trap 'cleanup; log_error "恢复过程中发生错误"; exit 1' ERR

# 运行主函数
main "$@"
