"""
上下文分析器

分析对话上下文，提取关键信息用于记忆存储和检索。
"""

import re
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Set, Any, Optional, Tuple
from uuid import UUID

try:
    import jieba
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize, sent_tokenize
except ImportError as e:
    # 可选依赖，如果没有安装则使用简化版本
    jieba = None
    nltk = None
    stopwords = None
    word_tokenize = None
    sent_tokenize = None

from ..config.settings import IntelligenceConfig
from ..utils.logging import LoggerMixin
from .memory import Memory


class ContextAnalyzer(LoggerMixin):
    """上下文分析器"""
    
    def __init__(self, config: IntelligenceConfig):
        self.config = config
        self.stop_words: Set[str] = set()
        self._initialize_nlp()
    
    def _initialize_nlp(self) -> None:
        """初始化NLP组件"""
        try:
            # 初始化中文分词
            if jieba and self.config.language_detection:
                jieba.initialize()
            
            # 初始化英文停用词
            if nltk and stopwords:
                try:
                    self.stop_words = set(stopwords.words('english'))
                except LookupError:
                    # 如果没有下载停用词数据，使用默认集合
                    self.stop_words = {
                        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
                        'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
                        'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
                        'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
                    }
            
            # 添加中文停用词
            chinese_stop_words = {
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
                '自己', '这', '那', '什么', '可以', '这个', '那个', '他', '她', '它'
            }
            self.stop_words.update(chinese_stop_words)
            
            self.logger.info("NLP组件初始化完成")
        except Exception as e:
            self.logger.warning(f"NLP组件初始化失败: {e}")
    
    async def analyze_content(self, content: str) -> Dict[str, Any]:
        """分析内容并提取上下文信息"""
        analysis = {
            "language": await self._detect_language(content),
            "keywords": await self._extract_keywords(content),
            "entities": await self._extract_entities(content),
            "sentiment": await self._analyze_sentiment(content),
            "topics": await self._extract_topics(content),
            "summary": await self._generate_summary(content),
            "metadata": {
                "length": len(content),
                "word_count": len(content.split()),
                "sentence_count": len(self._split_sentences(content)),
                "analyzed_at": datetime.now(timezone.utc).isoformat(),
            }
        }
        
        return analysis
    
    async def _detect_language(self, content: str) -> str:
        """检测文本语言"""
        if not self.config.language_detection:
            return "unknown"
        
        # 简单的语言检测逻辑
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', content))
        english_chars = len(re.findall(r'[a-zA-Z]', content))
        
        total_chars = chinese_chars + english_chars
        if total_chars == 0:
            return "unknown"
        
        chinese_ratio = chinese_chars / total_chars
        
        if chinese_ratio > 0.3:
            return "zh"
        elif english_chars > chinese_chars:
            return "en"
        else:
            return "mixed"
    
    async def _extract_keywords(self, content: str, max_keywords: int = 10) -> List[str]:
        """提取关键词"""
        # 检测语言
        language = await self._detect_language(content)
        
        keywords = []
        
        if language in ["zh", "mixed"] and jieba:
            # 中文关键词提取
            words = jieba.cut(content)
            word_freq = {}
            
            for word in words:
                word = word.strip()
                if len(word) > 1 and word not in self.stop_words:
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # 按频率排序
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            keywords.extend([word for word, freq in sorted_words[:max_keywords]])
        
        if language in ["en", "mixed"]:
            # 英文关键词提取
            if word_tokenize:
                words = word_tokenize(content.lower())
            else:
                words = content.lower().split()
            
            word_freq = {}
            for word in words:
                if (len(word) > 2 and 
                    word.isalpha() and 
                    word not in self.stop_words):
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # 按频率排序
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            keywords.extend([word for word, freq in sorted_words[:max_keywords]])
        
        # 去重并限制数量
        unique_keywords = []
        seen = set()
        for keyword in keywords:
            if keyword not in seen:
                unique_keywords.append(keyword)
                seen.add(keyword)
                if len(unique_keywords) >= max_keywords:
                    break
        
        return unique_keywords
    
    async def _extract_entities(self, content: str) -> List[Dict[str, str]]:
        """提取命名实体"""
        entities = []
        
        # 简单的实体识别模式
        patterns = {
            "email": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            "url": r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
            "phone": r'\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b',
            "date": r'\b\d{4}[-/]\d{1,2}[-/]\d{1,2}\b|\b\d{1,2}[-/]\d{1,2}[-/]\d{4}\b',
            "time": r'\b\d{1,2}:\d{2}(?::\d{2})?\s?(?:AM|PM|am|pm)?\b',
        }
        
        for entity_type, pattern in patterns.items():
            matches = re.finditer(pattern, content)
            for match in matches:
                entities.append({
                    "type": entity_type,
                    "text": match.group(),
                    "start": match.start(),
                    "end": match.end(),
                })
        
        return entities
    
    async def _analyze_sentiment(self, content: str) -> Dict[str, Any]:
        """分析情感倾向"""
        # 简单的情感分析
        positive_words = {
            "好", "棒", "优秀", "成功", "高兴", "开心", "满意", "喜欢", "爱",
            "good", "great", "excellent", "success", "happy", "love", "like", "amazing"
        }
        
        negative_words = {
            "坏", "差", "失败", "难过", "生气", "讨厌", "恨", "糟糕", "问题",
            "bad", "terrible", "fail", "sad", "angry", "hate", "problem", "issue"
        }
        
        content_lower = content.lower()
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)
        
        total_sentiment_words = positive_count + negative_count
        
        if total_sentiment_words == 0:
            sentiment = "neutral"
            confidence = 0.5
        elif positive_count > negative_count:
            sentiment = "positive"
            confidence = positive_count / total_sentiment_words
        elif negative_count > positive_count:
            sentiment = "negative"
            confidence = negative_count / total_sentiment_words
        else:
            sentiment = "neutral"
            confidence = 0.5
        
        return {
            "sentiment": sentiment,
            "confidence": confidence,
            "positive_words": positive_count,
            "negative_words": negative_count,
        }
    
    async def _extract_topics(self, content: str) -> List[str]:
        """提取主题"""
        # 基于关键词的简单主题提取
        keywords = await self._extract_keywords(content, max_keywords=20)
        
        # 主题映射
        topic_keywords = {
            "工作": ["工作", "项目", "任务", "会议", "同事", "老板", "公司", "work", "project", "task", "meeting"],
            "学习": ["学习", "课程", "考试", "书", "知识", "技能", "study", "course", "exam", "book", "knowledge"],
            "技术": ["代码", "编程", "软件", "系统", "算法", "数据", "code", "programming", "software", "system", "algorithm"],
            "生活": ["家", "朋友", "家人", "吃饭", "购物", "旅行", "home", "friend", "family", "food", "shopping", "travel"],
            "健康": ["健康", "运动", "医生", "药", "锻炼", "health", "exercise", "doctor", "medicine", "fitness"],
            "娱乐": ["电影", "音乐", "游戏", "书", "小说", "movie", "music", "game", "book", "novel"],
        }
        
        topics = []
        for topic, topic_words in topic_keywords.items():
            score = sum(1 for keyword in keywords if keyword in topic_words)
            if score > 0:
                topics.append(topic)
        
        return topics
    
    async def _generate_summary(self, content: str, max_length: int = 100) -> str:
        """生成内容摘要"""
        if len(content) <= max_length:
            return content
        
        # 简单的摘要生成：取前几句话
        sentences = self._split_sentences(content)
        
        summary = ""
        for sentence in sentences:
            if len(summary + sentence) <= max_length:
                summary += sentence + " "
            else:
                break
        
        return summary.strip() or content[:max_length] + "..."
    
    def _split_sentences(self, content: str) -> List[str]:
        """分割句子"""
        if sent_tokenize:
            return sent_tokenize(content)
        else:
            # 简单的句子分割
            sentences = re.split(r'[.!?。！？]', content)
            return [s.strip() for s in sentences if s.strip()]
    
    async def extract_conversation_context(
        self, 
        messages: List[str], 
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """提取对话上下文"""
        if not messages:
            return {}
        
        # 合并所有消息
        full_content = " ".join(messages)
        
        # 分析整体内容
        analysis = await self.analyze_content(full_content)
        
        # 添加对话特定信息
        context = {
            **analysis,
            "conversation": {
                "message_count": len(messages),
                "session_id": session_id,
                "total_length": len(full_content),
                "avg_message_length": len(full_content) / len(messages),
                "last_message": messages[-1][:100] if messages else "",
            }
        }
        
        return context
