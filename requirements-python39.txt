# AutoMem Python 3.9 兼容依赖包
# 专门为 Python 3.9 环境设计

# 基础依赖
pydantic>=1.10.0,<2.0.0  # Python 3.9 兼容版本
typer>=0.7.0,<0.10.0
rich>=12.0.0,<14.0.0
click>=8.0.0

# 异步和网络
aiohttp>=3.8.0
aiofiles>=0.8.0

# 数据库
aiosqlite>=0.17.0
sqlalchemy>=1.4.0,<2.0.0

# 向量数据库和嵌入
chromadb>=0.3.0,<0.5.0
sentence-transformers>=2.2.0
numpy>=1.21.0,<1.25.0
scikit-learn>=1.1.0,<1.4.0

# 自然语言处理
jieba>=0.42.0
langdetect>=1.0.9

# 日志和配置
structlog>=22.0.0
python-dateutil>=2.8.0
PyYAML>=6.0
toml>=0.10.2
python-dotenv>=0.19.0

# Windows 特定
pywin32>=306; sys_platform == "win32"
colorama>=0.4.6; sys_platform == "win32"

# GUI 依赖 (已安装)
# customtkinter>=5.2.0
# psutil>=5.9.0
# Pillow>=10.0.0
# requests>=2.31.0

# 开发工具 (可选)
# pytest>=7.0.0
# black>=22.0.0
# isort>=5.10.0
