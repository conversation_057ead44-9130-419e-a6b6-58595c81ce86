"""
自动分类器

基于内容自动分类记忆，支持多种分类策略。
"""

import re
import asyncio
from typing import Dict, List, Set, Any, Optional, Tuple
from collections import defaultdict, Counter

from ..config.settings import IntelligenceConfig
from ..utils.logging import LoggerMixin
from ..core.memory import Memory, MemoryType


class AutoClassifier(LoggerMixin):
    """自动分类器"""
    
    def __init__(self, config: IntelligenceConfig):
        self.config = config
        
        # 预定义分类规则
        self.classification_rules = self._initialize_classification_rules()
        
        # 学习到的分类模式
        self.learned_patterns: Dict[str, List[str]] = defaultdict(list)
        
        # 分类统计
        self.classification_stats: Dict[str, int] = defaultdict(int)
    
    def _initialize_classification_rules(self) -> Dict[str, Dict[str, Any]]:
        """初始化分类规则"""
        return {
            "工作": {
                "keywords": [
                    "工作", "项目", "任务", "会议", "同事", "老板", "公司", "客户", "业务",
                    "work", "project", "task", "meeting", "colleague", "boss", "company", "client", "business"
                ],
                "patterns": [
                    r"项目\w*",
                    r"会议\w*",
                    r"\w*工作\w*",
                    r"project\s+\w+",
                    r"meeting\s+\w+",
                ],
                "memory_types": [MemoryType.PROCEDURE, MemoryType.FACT],
                "weight": 1.0,
            },
            "学习": {
                "keywords": [
                    "学习", "课程", "教程", "知识", "技能", "书", "文档", "笔记", "总结",
                    "study", "course", "tutorial", "knowledge", "skill", "book", "document", "note", "summary"
                ],
                "patterns": [
                    r"学习\w*",
                    r"教程\w*",
                    r"\w*课程\w*",
                    r"study\s+\w+",
                    r"learn\s+\w+",
                ],
                "memory_types": [MemoryType.SEMANTIC, MemoryType.FACT],
                "weight": 1.0,
            },
            "技术": {
                "keywords": [
                    "代码", "编程", "开发", "软件", "系统", "算法", "数据库", "API", "框架",
                    "code", "programming", "development", "software", "system", "algorithm", "database", "api", "framework"
                ],
                "patterns": [
                    r"代码\w*",
                    r"编程\w*",
                    r"\w*开发\w*",
                    r"code\s+\w+",
                    r"programming\s+\w+",
                ],
                "memory_types": [MemoryType.PROCEDURE, MemoryType.SEMANTIC],
                "weight": 1.2,
            },
            "个人": {
                "keywords": [
                    "个人", "生活", "家庭", "朋友", "爱好", "健康", "情感", "想法", "计划",
                    "personal", "life", "family", "friend", "hobby", "health", "emotion", "thought", "plan"
                ],
                "patterns": [
                    r"个人\w*",
                    r"生活\w*",
                    r"\w*计划\w*",
                    r"personal\s+\w+",
                    r"life\s+\w+",
                ],
                "memory_types": [MemoryType.EPISODIC, MemoryType.CONVERSATION],
                "weight": 0.8,
            },
            "问题": {
                "keywords": [
                    "问题", "错误", "bug", "故障", "异常", "失败", "困难", "挑战",
                    "problem", "error", "bug", "issue", "exception", "failure", "difficulty", "challenge"
                ],
                "patterns": [
                    r"问题\w*",
                    r"错误\w*",
                    r"\w*bug\w*",
                    r"error\s+\w+",
                    r"problem\s+\w+",
                ],
                "memory_types": [MemoryType.PROCEDURE, MemoryType.FACT],
                "weight": 1.1,
            },
            "决策": {
                "keywords": [
                    "决定", "选择", "决策", "方案", "建议", "推荐", "评估", "分析",
                    "decision", "choice", "option", "solution", "recommendation", "evaluation", "analysis"
                ],
                "patterns": [
                    r"决定\w*",
                    r"选择\w*",
                    r"\w*方案\w*",
                    r"decision\s+\w+",
                    r"choice\s+\w+",
                ],
                "memory_types": [MemoryType.EPISODIC, MemoryType.PROCEDURE],
                "weight": 1.0,
            },
            "信息": {
                "keywords": [
                    "信息", "数据", "资料", "文档", "报告", "统计", "事实", "细节",
                    "information", "data", "material", "document", "report", "statistics", "fact", "detail"
                ],
                "patterns": [
                    r"信息\w*",
                    r"数据\w*",
                    r"\w*报告\w*",
                    r"information\s+\w+",
                    r"data\s+\w+",
                ],
                "memory_types": [MemoryType.FACT, MemoryType.SEMANTIC],
                "weight": 0.9,
            },
        }
    
    async def classify_memory(self, memory: Memory) -> List[str]:
        """对记忆进行自动分类"""
        content = memory.content.lower()
        summary = (memory.summary or "").lower()
        combined_text = f"{content} {summary}".strip()
        
        # 计算每个分类的得分
        category_scores = {}
        
        for category, rules in self.classification_rules.items():
            score = await self._calculate_category_score(
                combined_text, 
                rules, 
                memory.memory_type
            )
            
            if score >= self.config.classification_threshold:
                category_scores[category] = score
        
        # 按得分排序
        sorted_categories = sorted(
            category_scores.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        # 返回符合阈值的分类
        categories = [cat for cat, score in sorted_categories]
        
        # 更新统计信息
        for category in categories:
            self.classification_stats[category] += 1
        
        # 学习新模式
        await self._learn_patterns(combined_text, categories)
        
        self.logger.debug(f"记忆分类结果: {categories}")
        return categories
    
    async def _calculate_category_score(
        self, 
        text: str, 
        rules: Dict[str, Any], 
        memory_type: MemoryType
    ) -> float:
        """计算分类得分"""
        score = 0.0
        
        # 关键词匹配得分
        keyword_score = 0.0
        keywords = rules.get("keywords", [])
        for keyword in keywords:
            if keyword.lower() in text:
                keyword_score += 1.0
        
        if keywords:
            keyword_score = keyword_score / len(keywords)
        
        # 模式匹配得分
        pattern_score = 0.0
        patterns = rules.get("patterns", [])
        for pattern in patterns:
            if re.search(pattern, text, re.IGNORECASE):
                pattern_score += 1.0
        
        if patterns:
            pattern_score = pattern_score / len(patterns)
        
        # 记忆类型匹配得分
        type_score = 0.0
        expected_types = rules.get("memory_types", [])
        if memory_type in expected_types:
            type_score = 1.0
        elif expected_types:
            type_score = 0.5  # 部分匹配
        
        # 综合得分
        base_score = (keyword_score * 0.5 + pattern_score * 0.3 + type_score * 0.2)
        
        # 应用权重
        weight = rules.get("weight", 1.0)
        final_score = base_score * weight
        
        return min(final_score, 1.0)
    
    async def _learn_patterns(self, text: str, categories: List[str]) -> None:
        """学习新的分类模式"""
        if not categories:
            return
        
        # 提取文本中的关键短语
        phrases = self._extract_phrases(text)
        
        for category in categories:
            # 将新短语添加到学习模式中
            for phrase in phrases:
                if phrase not in self.learned_patterns[category]:
                    self.learned_patterns[category].append(phrase)
                    
                    # 限制学习模式的数量
                    if len(self.learned_patterns[category]) > 100:
                        self.learned_patterns[category] = self.learned_patterns[category][-100:]
    
    def _extract_phrases(self, text: str, max_phrases: int = 10) -> List[str]:
        """提取关键短语"""
        # 简单的短语提取：2-3个词的组合
        words = text.split()
        phrases = []
        
        # 提取2词短语
        for i in range(len(words) - 1):
            phrase = f"{words[i]} {words[i+1]}"
            if len(phrase) > 3 and phrase not in phrases:
                phrases.append(phrase)
        
        # 提取3词短语
        for i in range(len(words) - 2):
            phrase = f"{words[i]} {words[i+1]} {words[i+2]}"
            if len(phrase) > 5 and phrase not in phrases:
                phrases.append(phrase)
        
        return phrases[:max_phrases]
    
    async def suggest_categories(self, content: str, limit: int = 3) -> List[Tuple[str, float]]:
        """为内容建议分类"""
        content_lower = content.lower()
        
        category_scores = {}
        for category, rules in self.classification_rules.items():
            score = await self._calculate_category_score(
                content_lower, 
                rules, 
                MemoryType.CONVERSATION  # 默认类型
            )
            category_scores[category] = score
        
        # 按得分排序
        sorted_suggestions = sorted(
            category_scores.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        # 返回前N个建议
        return sorted_suggestions[:limit]
    
    async def update_classification_rules(
        self, 
        category: str, 
        keywords: Optional[List[str]] = None,
        patterns: Optional[List[str]] = None,
        weight: Optional[float] = None
    ) -> None:
        """更新分类规则"""
        if category not in self.classification_rules:
            self.classification_rules[category] = {
                "keywords": [],
                "patterns": [],
                "memory_types": [MemoryType.CONVERSATION],
                "weight": 1.0,
            }
        
        rules = self.classification_rules[category]
        
        if keywords:
            rules["keywords"].extend(keywords)
            # 去重
            rules["keywords"] = list(set(rules["keywords"]))
        
        if patterns:
            rules["patterns"].extend(patterns)
            rules["patterns"] = list(set(rules["patterns"]))
        
        if weight is not None:
            rules["weight"] = weight
        
        self.logger.info(f"更新分类规则: {category}")
    
    async def get_classification_stats(self) -> Dict[str, Any]:
        """获取分类统计信息"""
        total_classifications = sum(self.classification_stats.values())
        
        stats = {
            "total_classifications": total_classifications,
            "category_distribution": dict(self.classification_stats),
            "category_percentages": {},
            "learned_patterns_count": {
                cat: len(patterns) 
                for cat, patterns in self.learned_patterns.items()
            },
            "available_categories": list(self.classification_rules.keys()),
        }
        
        # 计算百分比
        if total_classifications > 0:
            for category, count in self.classification_stats.items():
                stats["category_percentages"][category] = (count / total_classifications) * 100
        
        return stats
    
    async def reset_learned_patterns(self) -> None:
        """重置学习到的模式"""
        self.learned_patterns.clear()
        self.classification_stats.clear()
        self.logger.info("已重置学习模式和统计信息")
