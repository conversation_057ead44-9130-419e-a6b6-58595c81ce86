# AutoMem 使用指南

本指南将详细介绍如何使用 AutoMem 智能记忆管理系统，从基础操作到高级功能。

## 🎯 快速上手

### 启动服务

```bash
# 方法一：直接启动（开发/测试）
./automem serve --stdio

# 方法二：后台服务启动（生产）
sudo systemctl start automem

# 方法三：Docker 启动
docker-compose up -d
```

### 验证服务

```bash
# 检查服务状态
./automem status

# 查看系统信息
./automem version --detailed

# 运行健康检查
curl http://localhost:8000/health
```

## 🔌 MCP 客户端集成

### Claude Desktop 集成

1. **配置 Claude Desktop**

   编辑 Claude Desktop 配置文件：
   
   **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

   ```json
   {
     "mcpServers": {
       "automem": {
         "command": "/path/to/automem/automem",
         "args": ["serve", "--stdio"],
         "env": {
           "AUTOMEM_CONFIG": "/path/to/config.yaml",
           "AUTOMEM_DATA_DIR": "/path/to/data"
         }
       }
     }
   }
   ```

2. **重启 Claude Desktop**

3. **验证连接**
   
   在 Claude 中输入：
   ```
   请帮我存储一个记忆：今天学习了 AutoMem 的使用方法
   ```

### 其他 MCP 客户端

AutoMem 兼容所有标准 MCP 客户端，包括：
- Claude Desktop
- Continue.dev
- 自定义 MCP 客户端

## 📝 基本操作

### 1. 存储记忆

#### 自动处理存储（推荐）

```json
{
  "method": "tools/call",
  "params": {
    "name": "store_memory",
    "arguments": {
      "content": "Python 是一种高级编程语言，广泛用于 Web 开发、数据科学和人工智能。它具有简洁的语法和强大的库生态系统。",
      "auto_process": true
    }
  }
}
```

系统会自动：
- 判断是否值得存储
- 分析记忆类型和重要性
- 生成相关标签
- 分类到合适的类别

#### 手动指定参数

```json
{
  "method": "tools/call",
  "params": {
    "name": "store_memory",
    "arguments": {
      "content": "今天团队会议讨论了新项目的技术架构，决定使用微服务架构。",
      "memory_type": "episodic",
      "importance": "high",
      "tags": ["会议", "架构", "微服务"],
      "categories": ["工作", "技术"],
      "session_id": "meeting_20240115",
      "auto_process": false
    }
  }
}
```

### 2. 搜索记忆

#### 基础搜索

```json
{
  "method": "tools/call",
  "params": {
    "name": "search_memories",
    "arguments": {
      "query": "Python 编程语言",
      "limit": 5
    }
  }
}
```

#### 高级搜索

```json
{
  "method": "tools/call",
  "params": {
    "name": "search_memories",
    "arguments": {
      "query": "机器学习算法",
      "limit": 10,
      "memory_types": ["fact", "procedure"],
      "importance_levels": ["high", "critical"],
      "tags": ["AI", "算法"],
      "categories": ["技术"],
      "similarity_threshold": 0.7,
      "include_context": true
    }
  }
}
```

### 3. 获取上下文

#### 主题上下文

```json
{
  "method": "tools/call",
  "params": {
    "name": "get_context",
    "arguments": {
      "topic": "深度学习",
      "session_id": "current_session",
      "time_window_hours": 24,
      "limit": 5,
      "include_related": true
    }
  }
}
```

#### 会话上下文

```json
{
  "method": "tools/call",
  "params": {
    "name": "get_context",
    "arguments": {
      "session_id": "project_discussion",
      "conversation_id": "conv_001",
      "limit": 10
    }
  }
}
```

## 📚 资源访问

### 最近记忆

```json
{
  "method": "resources/read",
  "params": {
    "uri": "memory://recent"
  }
}
```

### 主题相关记忆

```json
{
  "method": "resources/read",
  "params": {
    "uri": "memory://context/人工智能"
  }
}
```

### 时间线记忆

```json
{
  "method": "resources/read",
  "params": {
    "uri": "memory://timeline/2024-01-15"
  }
}
```

### 标签记忆

```json
{
  "method": "resources/read",
  "params": {
    "uri": "memory://tags/Python"
  }
}
```

### 系统摘要

```json
{
  "method": "resources/read",
  "params": {
    "uri": "memory://summary"
  }
}
```

## 🎨 智能提示符

### 回忆上下文

```json
{
  "method": "prompts/get",
  "params": {
    "name": "recall_context",
    "arguments": {
      "topic": "机器学习",
      "depth": "deep",
      "time_range": "recent",
      "focus": "technical"
    }
  }
}
```

### 总结记忆

```json
{
  "method": "prompts/get",
  "params": {
    "name": "summarize_memories",
    "arguments": {
      "time_range": "week",
      "focus": "work",
      "memory_types": ["episodic", "procedure"],
      "importance_filter": "high"
    }
  }
}
```

### 发现连接

```json
{
  "method": "prompts/get",
  "params": {
    "name": "find_connections",
    "arguments": {
      "concept": "深度学习",
      "connection_types": ["semantic", "temporal", "causal"]
    }
  }
}
```

### 决策支持

```json
{
  "method": "prompts/get",
  "params": {
    "name": "decision_support",
    "arguments": {
      "decision_context": "选择合适的机器学习框架用于新项目",
      "decision_type": "technical"
    }
  }
}
```

## 🔧 管理操作

### 标签管理

#### 添加标签

```json
{
  "method": "tools/call",
  "params": {
    "name": "manage_tags",
    "arguments": {
      "action": "add",
      "memory_id": "memory-uuid-here",
      "tags": ["新标签1", "新标签2"]
    }
  }
}
```

#### 建议标签

```json
{
  "method": "tools/call",
  "params": {
    "name": "manage_tags",
    "arguments": {
      "action": "suggest",
      "memory_id": "memory-uuid-here"
    }
  }
}
```

#### 列出所有标签

```json
{
  "method": "tools/call",
  "params": {
    "name": "manage_tags",
    "arguments": {
      "action": "list"
    }
  }
}
```

### 系统维护

#### 清理预览

```json
{
  "method": "tools/call",
  "params": {
    "name": "cleanup_memories",
    "arguments": {
      "days": 30,
      "dry_run": true,
      "min_importance": "minimal",
      "max_access_count": 2
    }
  }
}
```

#### 执行清理

```json
{
  "method": "tools/call",
  "params": {
    "name": "cleanup_memories",
    "arguments": {
      "days": 30,
      "dry_run": false
    }
  }
}
```

#### 获取统计信息

```json
{
  "method": "tools/call",
  "params": {
    "name": "get_memory_stats"
  }
}
```

## 💡 使用技巧

### 1. 有效的记忆存储

**好的记忆内容**：
- 具体且有意义的信息
- 包含上下文和背景
- 有明确的主题或目标

```
✅ 好例子：
"在今天的项目会议中，团队决定使用 React + TypeScript 技术栈开发前端，主要考虑因素包括团队熟悉度、生态系统成熟度和长期维护性。预计开发周期为3个月。"

❌ 差例子：
"开会了"
```

### 2. 智能搜索技巧

**使用语义搜索**：
- 使用自然语言描述需求
- 包含相关的关键词和概念
- 调整相似度阈值获得更好结果

```
✅ 好的搜索：
"如何优化深度学习模型的训练速度"

❌ 简单搜索：
"优化"
```

### 3. 标签和分类策略

**建立一致的标签体系**：
- 使用层次化的标签结构
- 保持标签的一致性和规范性
- 定期清理和合并相似标签

```
✅ 好的标签：
["Python", "机器学习", "TensorFlow", "图像识别"]

❌ 混乱标签：
["python", "Python编程", "py", "蟒蛇"]
```

### 4. 会话管理

**使用会话ID组织相关记忆**：
- 为相关的对话使用相同的session_id
- 使用有意义的会话标识符
- 定期回顾和总结会话内容

## 📊 监控和分析

### 查看系统状态

```bash
# 详细状态信息
./automem status --detailed

# 性能监控
./automem stats

# 健康检查
./automem test
```

### 分析记忆使用

```bash
# 记忆统计
./automem stats --memories

# 标签分析
./automem stats --tags

# 使用趋势
./automem stats --trends
```

## 🔄 备份和恢复

### 创建备份

```bash
# 完整备份
./scripts/backup.sh --type full

# 仅数据备份
./scripts/backup.sh --type data

# 压缩备份
./scripts/backup.sh --compress
```

### 恢复数据

```bash
# 从备份恢复
./scripts/restore.sh backup_file.tar.gz

# 预览恢复内容
./scripts/restore.sh --preview backup_file.tar.gz
```

## ❓ 常见问题

### Q: 如何提高搜索准确性？

A: 
1. 使用更具体的查询词
2. 调整similarity_threshold参数
3. 使用标签和分类过滤
4. 定期清理和优化数据

### Q: 记忆存储失败怎么办？

A:
1. 检查内容是否为空或过短
2. 验证参数格式是否正确
3. 查看系统日志获取详细错误信息
4. 检查存储空间是否充足

### Q: 如何优化系统性能？

A:
1. 定期清理旧记忆
2. 优化配置参数
3. 增加系统资源
4. 使用缓存和索引优化

## 📞 获取帮助

- 📖 查看 [完整文档](README.md)
- 🐛 报告问题：[GitHub Issues](https://github.com/your-org/automem/issues)
- 💬 社区讨论：[GitHub Discussions](https://github.com/your-org/automem/discussions)
- 📧 技术支持：<EMAIL>

---

**恭喜！** 您现在已经掌握了 AutoMem 的基本使用方法。开始构建您的智能记忆系统吧！🧠✨
