# AutoMem Windows 安装指南

本指南专门为 Windows 用户提供详细的安装和配置说明，无需 Docker 依赖。

## 🎯 系统要求

### 最低要求
- **操作系统**: Windows 10 或 Windows Server 2019 及以上
- **Python**: Python 3.8 或更高版本
- **内存**: 至少 4GB RAM
- **存储**: 至少 2GB 可用空间
- **网络**: 互联网连接（用于下载依赖）

### 推荐配置
- **操作系统**: Windows 11
- **Python**: Python 3.11
- **内存**: 8GB RAM 或更多
- **存储**: 10GB 可用空间
- **CPU**: 4核心或更多

## 🚀 快速安装

### 方法一：一键安装（推荐）

1. **下载快速开始脚本**
   ```cmd
   # 下载并运行
   curl -o quick_start.bat https://raw.githubusercontent.com/your-org/automem/main/quick_start.bat
   quick_start.bat
   ```

2. **或者直接运行**
   ```cmd
   # 如果已有项目文件
   quick_start.bat
   ```

### 方法二：PowerShell 自动部署

```powershell
# 以管理员身份运行 PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 运行自动部署脚本
.\scripts\auto_deploy.ps1
```

## 🔧 手动安装步骤

### 1. 安装 Python

1. **下载 Python**
   - 访问 [Python 官网](https://www.python.org/downloads/)
   - 下载 Python 3.8+ 版本

2. **安装 Python**
   - 运行下载的安装程序
   - ✅ **重要**: 勾选 "Add Python to PATH"
   - 选择 "Install Now"

3. **验证安装**
   ```cmd
   python --version
   pip --version
   ```

### 2. 下载 AutoMem

```cmd
# 方法一：使用 git
git clone https://github.com/your-org/automem.git
cd automem

# 方法二：下载 ZIP 文件
# 从 GitHub 下载 ZIP 文件并解压
```

### 3. 创建虚拟环境

```cmd
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate

# 升级 pip
python -m pip install --upgrade pip
```

### 4. 安装依赖

```cmd
# 安装所有依赖
pip install -r requirements.txt
```

### 5. 初始化配置

```cmd
# 复制配置文件
copy config.example.yaml config.yaml

# 创建数据目录
mkdir data
mkdir data\logs
```

### 6. 测试安装

```cmd
# 运行测试
python -m pytest tests/ -v

# 检查配置
python -m automem.cli config --validate
```

## 🎮 启动和使用

### 启动 MCP 服务器

```cmd
# 方法一：直接启动
python -m automem.cli serve --stdio

# 方法二：使用批处理文件
start_automem.bat

# 方法三：指定配置文件
python -m automem.cli serve --config config.yaml --stdio
```

### 配置 Claude Desktop

1. **找到配置文件位置**
   ```
   %APPDATA%\Claude\claude_desktop_config.json
   ```

2. **编辑配置文件**
   ```json
   {
     "mcpServers": {
       "automem": {
         "command": "C:\\path\\to\\automem\\venv\\Scripts\\python.exe",
         "args": ["-m", "automem.cli", "serve", "--stdio"],
         "env": {
           "AUTOMEM_CONFIG": "C:\\path\\to\\automem\\config.yaml"
         }
       }
     }
   }
   ```

3. **重启 Claude Desktop**

## 🔧 高级配置

### 环境变量设置

```cmd
# 设置环境变量
set AUTOMEM_DATA_DIR=C:\AutoMem\data
set AUTOMEM_LOG_LEVEL=INFO
set AUTOMEM_CONFIG_FILE=C:\AutoMem\config.yaml
```

### Windows 服务安装

1. **创建服务**
   ```powershell
   # 以管理员身份运行
   .\install_service.ps1
   ```

2. **管理服务**
   ```cmd
   # 启动服务
   sc start AutoMem
   
   # 停止服务
   sc stop AutoMem
   
   # 查看服务状态
   sc query AutoMem
   ```

### 防火墙配置

```cmd
# 允许 AutoMem 通过防火墙
netsh advfirewall firewall add rule name="AutoMem" dir=in action=allow protocol=TCP localport=8000
```

## 📊 性能优化

### 1. 内存优化

在 `config.yaml` 中调整：
```yaml
storage:
  max_memories: 50000  # 减少最大记忆数量
  
intelligence:
  embedding_model: "all-MiniLM-L6-v2"  # 使用轻量级模型
```

### 2. 存储优化

```yaml
storage:
  sqlite_pragma:
    journal_mode: "WAL"
    synchronous: "NORMAL"
    cache_size: 10000
```

### 3. 日志优化

```yaml
logging:
  level: "WARNING"  # 减少日志输出
  enable_file: true
  max_file_size: "50MB"
```

## 🔍 故障排除

### 常见问题

1. **Python 未找到**
   ```
   错误: 'python' 不是内部或外部命令
   解决: 重新安装 Python 并确保勾选 "Add to PATH"
   ```

2. **依赖安装失败**
   ```cmd
   # 使用国内镜像
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

3. **权限错误**
   ```cmd
   # 以管理员身份运行命令提示符
   # 或使用 --user 参数
   pip install --user -r requirements.txt
   ```

4. **端口被占用**
   ```cmd
   # 查看端口占用
   netstat -ano | findstr :8000
   
   # 杀死占用进程
   taskkill /PID <进程ID> /F
   ```

### 日志查看

```cmd
# 查看应用日志
type data\logs\automem.log

# 实时监控日志
powershell Get-Content data\logs\automem.log -Wait
```

### 性能监控

```cmd
# 查看进程资源使用
tasklist /fi "imagename eq python.exe" /fo table

# 查看内存使用
wmic process where name="python.exe" get ProcessId,PageFileUsage,WorkingSetSize
```

## 🔄 更新和维护

### 更新 AutoMem

```cmd
# 停止服务
taskkill /f /im python.exe

# 拉取最新代码
git pull origin main

# 更新依赖
venv\Scripts\activate
pip install -r requirements.txt --upgrade

# 重启服务
start_automem.bat
```

### 备份数据

```cmd
# 手动备份
xcopy data backup\data_%date% /E /I

# 使用内置备份工具
python -m automem.cli backup --output backup\automem_backup.zip
```

### 清理维护

```cmd
# 清理日志
del data\logs\*.log

# 清理缓存
rmdir /s venv\Lib\site-packages\__pycache__

# 重建虚拟环境
rmdir /s venv
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

## 📞 获取帮助

### 命令行帮助

```cmd
# 查看所有命令
python -m automem.cli --help

# 查看特定命令帮助
python -m automem.cli serve --help
```

### 在线资源

- **文档**: [docs/README.md](README.md)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/automem/issues)
- **社区讨论**: [GitHub Discussions](https://github.com/your-org/automem/discussions)

### 技术支持

- **邮箱**: <EMAIL>
- **QQ群**: 123456789
- **微信群**: 扫描二维码加入

## 🎉 完成安装

恭喜！您已成功在 Windows 上安装了 AutoMem。

**下一步**:
1. 配置 Claude Desktop 或其他 MCP 客户端
2. 阅读 [使用指南](usage.md)
3. 开始使用智能记忆功能

**测试安装**:
```cmd
# 在 Claude 中输入
请帮我存储一个记忆：今天成功在Windows上安装了AutoMem
```

享受您的智能记忆助手！🧠✨
