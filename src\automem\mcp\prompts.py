"""
AutoMem MCP提示符接口

提供记忆相关的智能提示符。
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone

from ..utils.logging import LoggerMixin
from ..intelligence.autonomous import AutonomousMemorySystem


class AutoMemPrompts(LoggerMixin):
    """AutoMem MCP提示符"""
    
    def __init__(self, autonomous_system: AutonomousMemorySystem):
        self.autonomous_system = autonomous_system
    
    async def recall_context_prompt(
        self, 
        topic: str, 
        depth: str = "medium",
        time_range: str = "recent",
        focus: str = "general"
    ) -> str:
        """生成回忆相关上下文的提示符
        
        Args:
            topic: 主题关键词
            depth: 回忆深度 (shallow, medium, deep)
            time_range: 时间范围 (recent, today, week, month, all)
            focus: 关注焦点 (general, technical, personal, work)
        
        Returns:
            结构化的提示符文本
        """
        try:
            # 获取相关记忆
            from ..retrieval.context_retriever import ContextRetriever, ConversationContext
            
            context_retriever = ContextRetriever(
                self.autonomous_system.retrieval_config,
                self.autonomous_system.storage,
                None
            )
            
            context = ConversationContext(topics=[topic])
            
            # 根据时间范围调整检索
            time_window_hours = {
                "recent": 24,
                "today": 24,
                "week": 168,
                "month": 720,
                "all": 8760,  # 一年
            }.get(time_range, 24)
            
            limit = {
                "shallow": 3,
                "medium": 7,
                "deep": 15,
            }.get(depth, 7)
            
            contextual_memories = await context_retriever.retrieve_context(
                query=topic,
                context=context,
                limit=limit,
                time_window_hours=time_window_hours
            )
            
            # 构建提示符
            depth_instructions = {
                "shallow": "简要回忆",
                "medium": "详细回忆",
                "deep": "深度分析和回忆",
            }
            
            focus_instructions = {
                "general": "全面的信息",
                "technical": "技术细节和实现方案",
                "personal": "个人经验和感受",
                "work": "工作相关的决策和流程",
            }
            
            instruction = depth_instructions.get(depth, "详细回忆")
            focus_desc = focus_instructions.get(focus, "相关信息")
            
            prompt = f"""请{instruction}与主题 "{topic}" 相关的{focus_desc}。

基于以下记忆内容：

"""
            
            if contextual_memories:
                for i, contextual_memory in enumerate(contextual_memories, 1):
                    memory = contextual_memory.memory
                    prompt += f"{i}. **{memory.created_at.strftime('%Y-%m-%d %H:%M')}** - {memory.content}\n"
                    if memory.tags:
                        prompt += f"   标签: {', '.join(memory.tags)}\n"
                    prompt += f"   相关性: {contextual_memory.context_score:.2f} ({', '.join(contextual_memory.context_reasons)})\n\n"
            else:
                prompt += "暂无直接相关的记忆内容。\n\n"
            
            prompt += f"""
请基于上述记忆内容，{instruction}以下方面：

1. **核心概念和定义** - 与"{topic}"相关的关键概念
2. **历史背景** - 之前的讨论、决策或经验
3. **重要细节** - 具体的数据、方法或实现
4. **关联关系** - 与其他主题或概念的联系
5. **实践应用** - 具体的使用场景或案例
6. **经验教训** - 从中获得的洞察或启发

请以结构化的方式组织这些信息，突出最相关和最重要的内容。
"""
            
            return prompt
            
        except Exception as e:
            self.logger.error(f"生成回忆上下文提示符失败: {e}")
            return f"生成提示符时发生错误: {e}"
    
    async def summarize_memories_prompt(
        self,
        time_range: str = "recent",
        focus: str = "general",
        memory_types: Optional[List[str]] = None,
        importance_filter: str = "medium"
    ) -> List[Dict[str, str]]:
        """生成总结记忆的提示符
        
        Args:
            time_range: 时间范围 (recent, today, week, month)
            focus: 关注焦点 (general, work, learning, decisions)
            memory_types: 记忆类型过滤
            importance_filter: 重要性过滤 (all, high, critical)
        
        Returns:
            消息列表格式的提示符
        """
        try:
            # 获取相关记忆
            from ..core.memory import MemoryQuery, MemoryType, MemoryImportance
            
            # 构建查询
            query_kwargs = {"limit": 50}
            
            # 时间过滤
            if time_range == "today":
                hours = 24
            elif time_range == "week":
                hours = 168
            elif time_range == "month":
                hours = 720
            else:  # recent
                hours = 48
            
            from datetime import timedelta
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            query_kwargs["created_after"] = cutoff_time
            
            # 类型过滤
            if memory_types:
                try:
                    query_kwargs["memory_types"] = [MemoryType(mt) for mt in memory_types]
                except ValueError:
                    pass
            
            # 重要性过滤
            if importance_filter == "high":
                query_kwargs["importance_levels"] = [MemoryImportance.HIGH, MemoryImportance.CRITICAL]
            elif importance_filter == "critical":
                query_kwargs["importance_levels"] = [MemoryImportance.CRITICAL]
            
            query = MemoryQuery(**query_kwargs)
            memories = await self.autonomous_system.storage.search_memories(query)
            
            # 时间描述
            time_descriptions = {
                "recent": "最近的",
                "today": "今天的",
                "week": "本周的",
                "month": "本月的",
            }
            
            time_desc = time_descriptions.get(time_range, "相关的")
            
            # 构建消息
            messages = []
            
            # 系统消息
            system_message = f"请总结{time_desc}记忆，重点关注{focus}方面的内容。"
            messages.append({"role": "user", "content": system_message})
            
            # 记忆内容
            if memories:
                memory_content = f"以下是{time_desc}记忆内容（共{len(memories)}条）：\n\n"
                
                for i, memory in enumerate(memories, 1):
                    memory_content += f"{i}. **{memory.created_at.strftime('%m-%d %H:%M')}** "
                    memory_content += f"[{memory.memory_type.value}] "
                    memory_content += f"({memory.importance.value}) "
                    memory_content += f"{memory.content}\n"
                    
                    if memory.tags:
                        memory_content += f"   标签: {', '.join(list(memory.tags)[:3])}\n"
                    
                    memory_content += "\n"
                
                messages.append({"role": "user", "content": memory_content})
            else:
                messages.append({"role": "user", "content": f"在指定时间范围内暂无记忆内容。"})
            
            # 总结指令
            summary_instruction = """请按以下结构总结这些记忆：

1. **概览** - 总体情况和主要活动
2. **重要事件** - 关键的决策、发现或成果
3. **学习收获** - 新的知识、技能或洞察
4. **待办事项** - 需要跟进或完成的任务
5. **模式识别** - 发现的趋势或重复出现的主题

请按重要性排序，并提供简洁但全面的总结。"""
            
            messages.append({"role": "user", "content": summary_instruction})
            
            return messages
            
        except Exception as e:
            self.logger.error(f"生成总结记忆提示符失败: {e}")
            return [{"role": "user", "content": f"生成提示符时发生错误: {e}"}]
    
    async def find_connections_prompt(self, concept: str, connection_types: List[str] = None) -> str:
        """生成发现记忆连接的提示符
        
        Args:
            concept: 核心概念
            connection_types: 连接类型 (semantic, temporal, causal, thematic)
        
        Returns:
            连接分析提示符
        """
        try:
            # 搜索相关记忆
            from ..retrieval.semantic_search import SemanticSearchEngine
            
            search_engine = SemanticSearchEngine(
                self.autonomous_system.retrieval_config,
                self.autonomous_system.vector_db,
                self.autonomous_system.storage
            )
            
            search_results = await search_engine.search(
                query=concept,
                limit=15,
                similarity_threshold=0.4
            )
            
            prompt = f"""请分析概念 "{concept}" 与已有记忆之间的潜在连接。

相关记忆内容：

"""
            
            if search_results:
                for i, result in enumerate(search_results, 1):
                    memory = result.memory
                    prompt += f"{i}. **{memory.created_at.strftime('%Y-%m-%d')}** - {memory.content}\n"
                    prompt += f"   相似度: {result.similarity_score:.2f}, 匹配原因: {', '.join(result.match_reasons)}\n"
                    if memory.tags:
                        prompt += f"   标签: {', '.join(list(memory.tags)[:3])}\n"
                    prompt += "\n"
            else:
                prompt += "暂无直接相关的记忆。\n\n"
            
            connection_types = connection_types or ["semantic", "temporal", "causal", "thematic"]
            
            prompt += f"""
请从以下维度分析连接：

"""
            
            if "semantic" in connection_types:
                prompt += """1. **语义关联** - 概念、定义或含义上的相似性
   - 直接关联：明确提到该概念的记忆
   - 间接关联：相关主题或上下文的记忆
   
"""
            
            if "temporal" in connection_types:
                prompt += """2. **时间关联** - 时间上的相关性
   - 同期记忆：同一时间段的相关记忆
   - 发展轨迹：概念演变的时间线
   
"""
            
            if "causal" in connection_types:
                prompt += """3. **因果关系** - 原因和结果的关联
   - 前因：导致该概念出现的原因
   - 后果：该概念产生的影响或结果
   
"""
            
            if "thematic" in connection_types:
                prompt += """4. **主题关联** - 主题或领域的相关性
   - 模式识别：类似情况或决策的记忆
   - 领域交叉：不同领域间的连接
   
"""
            
            prompt += """
请识别这些连接的强度和重要性，并解释连接的原因。对于每个发现的连接，请提供：
- 连接类型和强度（强/中/弱）
- 连接的具体原因和证据
- 这种连接的潜在价值或意义
"""
            
            return prompt
            
        except Exception as e:
            self.logger.error(f"生成连接分析提示符失败: {e}")
            return f"生成提示符时发生错误: {e}"
    
    async def decision_support_prompt(
        self,
        decision_context: str,
        decision_type: str = "general"
    ) -> str:
        """生成决策支持提示符
        
        Args:
            decision_context: 决策上下文描述
            decision_type: 决策类型 (technical, business, personal)
        
        Returns:
            决策支持提示符
        """
        try:
            # 搜索相关的决策记忆
            from ..core.memory import MemoryQuery, MemoryType
            
            # 搜索程序性记忆和情节性记忆
            query = MemoryQuery(
                content=decision_context,
                memory_types=[MemoryType.PROCEDURE, MemoryType.EPISODIC],
                limit=10
            )
            
            decision_memories = await self.autonomous_system.storage.search_memories(query)
            
            prompt = f"""基于历史记忆为以下决策提供支持：

**决策上下文**: {decision_context}
**决策类型**: {decision_type}

相关历史经验：

"""
            
            if decision_memories:
                for i, memory in enumerate(decision_memories, 1):
                    prompt += f"{i}. **{memory.created_at.strftime('%Y-%m-%d')}** - {memory.content}\n"
                    prompt += f"   类型: {memory.memory_type.value}, 重要性: {memory.importance.value}\n"
                    if memory.tags:
                        prompt += f"   标签: {', '.join(list(memory.tags)[:3])}\n"
                    prompt += "\n"
            else:
                prompt += "暂无直接相关的历史决策记录。\n\n"
            
            prompt += f"""
请基于上述历史经验，为当前决策提供以下分析：

1. **相似情况分析** - 识别类似的历史决策情况
2. **成功经验** - 之前成功决策的关键因素
3. **失败教训** - 需要避免的错误或陷阱
4. **风险评估** - 潜在的风险和缓解策略
5. **决策建议** - 基于历史经验的具体建议
6. **关键考虑因素** - 需要特别注意的要点

请提供结构化的分析，帮助做出更明智的决策。
"""
            
            return prompt
            
        except Exception as e:
            self.logger.error(f"生成决策支持提示符失败: {e}")
            return f"生成提示符时发生错误: {e}"
