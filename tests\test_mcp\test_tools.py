"""
测试MCP工具接口
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from uuid import uuid4

from src.automem.mcp.tools import AutoMemTools
from src.automem.core.memory import Memory, MemoryType, MemoryImportance


class TestAutoMemTools:
    """测试AutoMem MCP工具"""
    
    @pytest.fixture
    def mock_autonomous_system(self):
        """模拟自主系统"""
        system = Mock()
        system.process_new_memory = AsyncMock()
        system.storage = Mock()
        system.vector_db = Mock()
        system.tagger = Mock()
        system.priority_manager = Mock()
        system.lifecycle_manager = Mock()
        system.classifier = Mock()
        return system
    
    @pytest.fixture
    def tools(self, mock_autonomous_system):
        """创建工具实例"""
        return AutoMemTools(mock_autonomous_system)
    
    @pytest.mark.asyncio
    async def test_store_memory_auto_process(self, tools, mock_autonomous_system):
        """测试自动处理存储记忆"""
        content = "这是一个测试记忆内容"
        
        # 模拟自主系统返回成功结果
        mock_memory = Memory(
            content=content,
            memory_type=MemoryType.CONVERSATION,
            importance=MemoryImportance.MEDIUM
        )
        
        mock_autonomous_system.process_new_memory.return_value = (
            True,  # success
            mock_memory,  # memory
            [Mock(action_type="store", confidence=0.8, reasoning="自动存储")]  # actions
        )
        
        # 调用工具
        result = await tools.store_memory(
            content=content,
            memory_type="conversation",
            importance="medium",
            auto_process=True
        )
        
        # 验证结果
        assert result["success"] is True
        assert result["memory_id"] == str(mock_memory.id)
        assert result["content"] == content
        assert result["memory_type"] == "conversation"
        assert result["importance"] == "medium"
        assert "auto_actions" in result
        assert len(result["auto_actions"]) == 1
        
        # 验证调用
        mock_autonomous_system.process_new_memory.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_store_memory_manual(self, tools, mock_autonomous_system):
        """测试手动存储记忆"""
        content = "手动存储的记忆内容"
        
        # 模拟存储和向量数据库
        mock_memory = Memory(
            content=content,
            memory_type=MemoryType.FACT,
            importance=MemoryImportance.HIGH
        )
        
        mock_autonomous_system.storage.store_memory = AsyncMock(return_value=mock_memory)
        mock_autonomous_system.vector_db.add_memory = AsyncMock()
        
        # 调用工具
        result = await tools.store_memory(
            content=content,
            memory_type="fact",
            importance="high",
            tags=["测试", "手动"],
            categories=["技术"],
            auto_process=False
        )
        
        # 验证结果
        assert result["success"] is True
        assert result["memory_id"] == str(mock_memory.id)
        assert result["content"] == content
        assert result["memory_type"] == "fact"
        assert result["importance"] == "high"
        assert "message" in result
        assert "手动存储" in result["message"]
        
        # 验证调用
        mock_autonomous_system.storage.store_memory.assert_called_once()
        mock_autonomous_system.vector_db.add_memory.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_store_memory_auto_reject(self, tools, mock_autonomous_system):
        """测试自动处理拒绝存储"""
        content = "嗯"
        
        # 模拟自主系统拒绝存储
        mock_autonomous_system.process_new_memory.return_value = (
            False,  # success
            None,  # memory
            [Mock(action_type="ignore", reasoning="内容过于简短")]  # actions
        )
        
        # 调用工具
        result = await tools.store_memory(
            content=content,
            auto_process=True
        )
        
        # 验证结果
        assert result["success"] is False
        assert "error" in result
        assert "不存储" in result["error"]
        assert "actions" in result
    
    @pytest.mark.asyncio
    async def test_search_memories(self, tools, mock_autonomous_system):
        """测试搜索记忆"""
        query = "Python编程"
        
        # 模拟搜索引擎和结果
        mock_search_engine = Mock()
        mock_search_results = [
            Mock(
                memory=Mock(
                    id=uuid4(),
                    content="Python是一种编程语言",
                    summary="Python语言介绍",
                    memory_type=MemoryType.FACT,
                    importance=MemoryImportance.HIGH,
                    tags={"Python", "编程"},
                    categories={"技术"},
                    created_at=Mock(),
                    updated_at=Mock(),
                    access_count=5
                ),
                similarity_score=0.85,
                relevance_score=0.90,
                combined_score=0.875,
                match_reasons=["内容匹配", "标签匹配"]
            )
        ]
        
        with patch('src.automem.mcp.tools.SemanticSearchEngine') as mock_engine_class:
            mock_engine_class.return_value = mock_search_engine
            mock_search_engine.search = AsyncMock(return_value=mock_search_results)
            
            # 调用工具
            result = await tools.search_memories(
                query=query,
                limit=10,
                memory_types=["fact"],
                tags=["Python"],
                similarity_threshold=0.6
            )
            
            # 验证结果
            assert result["success"] is True
            assert result["query"] == query
            assert len(result["results"]) == 1
            assert result["total_found"] == 1
            
            # 验证结果内容
            memory_result = result["results"][0]
            assert "memory_id" in memory_result
            assert memory_result["content"] == "Python是一种编程语言"
            assert memory_result["similarity_score"] == 0.85
            assert memory_result["relevance_score"] == 0.90
    
    @pytest.mark.asyncio
    async def test_get_context(self, tools, mock_autonomous_system):
        """测试获取上下文"""
        topic = "机器学习"
        
        # 模拟上下文检索器
        mock_context_retriever = Mock()
        mock_contextual_memories = [
            Mock(
                memory=Mock(
                    id=uuid4(),
                    content="机器学习是人工智能的一个分支",
                    summary="机器学习定义",
                    memory_type=MemoryType.SEMANTIC,
                    importance=MemoryImportance.HIGH,
                    tags={"机器学习", "AI"},
                    created_at=Mock()
                ),
                context_score=0.92,
                context_reasons=["主题匹配", "语义相关"],
                relationship_type="semantic"
            )
        ]
        
        with patch('src.automem.mcp.tools.ContextRetriever') as mock_retriever_class:
            mock_retriever_class.return_value = mock_context_retriever
            mock_context_retriever.retrieve_context = AsyncMock(return_value=mock_contextual_memories)
            
            # 调用工具
            result = await tools.get_context(
                topic=topic,
                session_id="test_session",
                time_window_hours=24,
                limit=5
            )
            
            # 验证结果
            assert result["success"] is True
            assert result["topic"] == topic
            assert result["session_id"] == "test_session"
            assert len(result["context_memories"]) == 1
            
            # 验证上下文记忆
            context_memory = result["context_memories"][0]
            assert "memory_id" in context_memory
            assert context_memory["content"] == "机器学习是人工智能的一个分支"
            assert context_memory["context_score"] == 0.92
            assert context_memory["relationship_type"] == "semantic"
    
    @pytest.mark.asyncio
    async def test_manage_tags_add(self, tools, mock_autonomous_system):
        """测试添加标签"""
        memory_id = str(uuid4())
        
        # 模拟记忆对象
        mock_memory = Mock()
        mock_memory.tags = {"原有标签"}
        mock_memory.add_tag = Mock()
        
        mock_autonomous_system.storage.get_memory = AsyncMock(return_value=mock_memory)
        mock_autonomous_system.storage.update_memory = AsyncMock(return_value=mock_memory)
        mock_autonomous_system.vector_db.update_memory = AsyncMock()
        
        # 调用工具
        result = await tools.manage_tags(
            action="add",
            memory_id=memory_id,
            tags=["新标签1", "新标签2"]
        )
        
        # 验证结果
        assert result["success"] is True
        assert result["action"] == "add"
        assert result["memory_id"] == memory_id
        assert result["added_tags"] == ["新标签1", "新标签2"]
        
        # 验证调用
        mock_memory.add_tag.assert_any_call("新标签1")
        mock_memory.add_tag.assert_any_call("新标签2")
        mock_autonomous_system.storage.update_memory.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_manage_tags_suggest(self, tools, mock_autonomous_system):
        """测试标签建议"""
        memory_id = str(uuid4())
        
        # 模拟记忆对象
        mock_memory = Mock()
        mock_memory.tags = {"现有标签"}
        
        mock_autonomous_system.storage.get_memory = AsyncMock(return_value=mock_memory)
        mock_autonomous_system.tagger.generate_tags = AsyncMock(return_value={"建议标签1", "建议标签2"})
        mock_autonomous_system.tagger.suggest_related_tags = AsyncMock(return_value=["相关标签1"])
        
        # 调用工具
        result = await tools.manage_tags(
            action="suggest",
            memory_id=memory_id
        )
        
        # 验证结果
        assert result["success"] is True
        assert result["action"] == "suggest"
        assert result["memory_id"] == memory_id
        assert "suggested_tags" in result
        assert "related_tags" in result
        assert len(result["suggested_tags"]) == 2
        assert len(result["related_tags"]) == 1
    
    @pytest.mark.asyncio
    async def test_cleanup_memories_dry_run(self, tools, mock_autonomous_system):
        """测试清理记忆预览"""
        # 模拟归档候选
        mock_candidates = [
            Mock(
                id=uuid4(),
                content="旧记忆内容",
                age_in_days=Mock(return_value=35),
                access_count=1,
                importance=MemoryImportance.MINIMAL,
                created_at=Mock()
            )
        ]
        
        mock_autonomous_system.priority_manager.get_archive_candidates = AsyncMock(return_value=mock_candidates)
        
        # 调用工具
        result = await tools.cleanup_memories(
            days=30,
            dry_run=True,
            min_importance="minimal",
            max_access_count=2
        )
        
        # 验证结果
        assert result["success"] is True
        assert result["dry_run"] is True
        assert result["days"] == 30
        assert result["total_candidates"] == 1
        assert result["filtered_candidates"] == 1
        assert "candidates_preview" in result
        assert len(result["candidates_preview"]) == 1
    
    @pytest.mark.asyncio
    async def test_get_memory_stats(self, tools, mock_autonomous_system):
        """测试获取记忆统计"""
        # 模拟各种统计数据
        mock_autonomous_system.storage.get_memory_stats = AsyncMock(return_value={
            "total_memories": 1000,
            "by_type": {"conversation": 600, "fact": 400},
            "recent_week": 50
        })
        
        mock_autonomous_system.priority_manager.get_priority_statistics = AsyncMock(return_value={
            "high_priority": 100,
            "medium_priority": 500,
            "low_priority": 400
        })
        
        mock_autonomous_system.lifecycle_manager.get_lifecycle_statistics = AsyncMock(return_value={
            "active": 200,
            "recent": 300,
            "archived": 500
        })
        
        mock_autonomous_system.tagger.get_tag_stats = AsyncMock(return_value={
            "total_tags": 500,
            "avg_tags_per_memory": 2.5
        })
        
        mock_autonomous_system.classifier.get_classification_stats = AsyncMock(return_value={
            "total_categories": 50,
            "avg_categories_per_memory": 1.2
        })
        
        mock_autonomous_system.get_autonomous_insights = AsyncMock(return_value={
            "total_actions": 1500,
            "success_rate": 95.0
        })
        
        # 调用工具
        result = await tools.get_memory_stats()
        
        # 验证结果
        assert result["success"] is True
        assert "basic_stats" in result
        assert "priority_stats" in result
        assert "lifecycle_stats" in result
        assert "tag_stats" in result
        assert "classification_stats" in result
        assert "autonomous_insights" in result
        assert "generated_at" in result
        
        # 验证基础统计
        assert result["basic_stats"]["total_memories"] == 1000
        assert result["autonomous_insights"]["success_rate"] == 95.0
    
    @pytest.mark.asyncio
    async def test_error_handling(self, tools, mock_autonomous_system):
        """测试错误处理"""
        # 模拟存储错误
        mock_autonomous_system.process_new_memory.side_effect = Exception("存储错误")
        
        # 调用工具
        result = await tools.store_memory("测试内容")
        
        # 验证错误处理
        assert result["success"] is False
        assert "error" in result
        assert "存储错误" in result["error"]
