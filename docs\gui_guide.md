# AutoMem GUI 管理器使用指南

AutoMem GUI 管理器是一个现代化的 Windows 图形界面工具，让您轻松管理和监控 AutoMem 智能记忆系统。

## 🚀 快速开始

### 启动 GUI 管理器

```cmd
# 方法一：直接启动（推荐）
python start_gui.py

# 方法二：手动启动
python automem_manager.py
```

### 首次使用

1. **自动依赖检查**：首次启动时会自动检查并安装 GUI 依赖
2. **配置初始化**：如果没有配置文件，会自动创建默认配置
3. **环境验证**：自动检查 Python 环境和 AutoMem 模块

## 📱 界面介绍

### 主界面布局

GUI 管理器采用现代化的深色主题，包含以下主要区域：

- **标题栏**：显示应用名称和版本
- **选项卡区域**：包含五个主要功能选项卡
- **状态栏**：显示当前操作状态

### 选项卡功能

#### 1. 🎛️ 仪表板

**服务状态区域**
- **状态指示器**：实时显示服务运行状态
  - 🟢 绿色圆点：服务正在运行
  - 🔴 红色圆点：服务未运行
- **控制按钮**：
  - `启动服务`：启动 AutoMem MCP 服务器
  - `停止服务`：停止正在运行的服务
  - `重启服务`：重启服务（先停止再启动）

**快速设置**
- `打开配置文件`：使用默认编辑器打开配置文件
- `打开数据目录`：在文件资源管理器中打开数据目录
- `查看日志`：快速切换到日志选项卡

**系统信息**
- 实时显示系统状态信息：
  - 系统时间
  - Python 版本
  - 工作目录
  - 配置文件路径
  - 内存使用情况
  - CPU 使用率
  - 磁盘空间
  - 数据目录大小

#### 2. ⚙️ 配置

**配置文件管理**
- **文件选择**：
  - 路径输入框：显示当前配置文件路径
  - `浏览`：选择其他配置文件
  - `重新加载`：重新加载配置文件内容

**配置编辑器**
- **语法高亮**：YAML 格式的配置编辑器
- **实时编辑**：直接在界面中编辑配置
- **操作按钮**：
  - `保存配置`：保存当前编辑的配置
  - `重置配置`：恢复到默认配置
  - `验证配置`：检查配置文件格式是否正确

**配置项说明**
```yaml
# 服务器基本配置
server_name: "AutoMem-Windows"  # 服务器名称
debug: false                    # 调试模式

# 存储配置
storage:
  data_dir: "data"             # 数据目录
  max_memories: 50000          # 最大记忆数量
  backup_enabled: true         # 启用自动备份

# 智能处理配置
intelligence:
  embedding_model: "all-MiniLM-L6-v2"  # 嵌入模型
  auto_classification: true             # 自动分类
  auto_tag_enabled: true               # 自动标签

# 日志配置
logging:
  level: "INFO"                        # 日志级别
  enable_file: true                    # 启用文件日志
  file_path: "data/logs/automem.log"   # 日志文件路径
```

#### 3. 📋 日志

**日志控制**
- `刷新日志`：重新加载日志文件内容
- `清空日志`：清空当前日志文件
- `导出日志`：将日志导出到指定文件
- **日志级别选择**：选择显示的日志级别（DEBUG、INFO、WARNING、ERROR）

**日志显示**
- **实时更新**：服务运行时实时显示新的日志条目
- **自动滚动**：新日志自动滚动到底部
- **搜索功能**：可以搜索特定的日志内容
- **时间戳**：每条日志都带有精确的时间戳

**日志级别说明**
- **DEBUG**：详细的调试信息
- **INFO**：一般信息，如服务启动、配置加载等
- **WARNING**：警告信息，不影响正常运行
- **ERROR**：错误信息，可能影响功能

#### 4. 🔧 工具

**数据管理**
- `备份数据`：
  - 将整个数据目录打包为 ZIP 文件
  - 自动包含时间戳的文件名
  - 支持自定义备份位置
- `恢复数据`：
  - 从备份文件恢复数据
  - 会覆盖现有数据（需确认）
- `清理数据`：
  - 清理指定天数前的旧记忆
  - 支持预览模式（dry-run）
  - 可配置清理规则

**系统工具**
- `检查更新`：
  - 检查 GitHub 上的最新版本
  - 自动比较版本号
  - 提供下载链接
- `重建索引`：
  - 重建向量数据库索引
  - 提高搜索性能
  - 适用于数据损坏修复
- `系统诊断`：
  - 全面检查系统状态
  - 验证依赖包安装
  - 检查配置文件完整性
  - 验证数据目录结构

**Claude Desktop 集成**
- `生成配置`：
  - 自动生成 Claude Desktop 配置
  - 包含正确的路径和参数
  - 支持复制到剪贴板
- `打开配置文件`：
  - 直接打开 Claude Desktop 配置文件
  - 自动定位到正确路径
- `测试连接`：
  - 测试与 Claude Desktop 的连接
  - 验证 MCP 协议通信

**工具输出区域**
- 显示所有工具操作的输出信息
- 包含成功、警告和错误消息
- 支持滚动查看历史输出

#### 5. ℹ️ 关于

**应用信息**
- 应用名称和版本号
- 详细的功能描述
- 主要特性列表

**快速链接**
- `GitHub 仓库`：打开项目 GitHub 页面
- `使用文档`：打开在线文档
- `问题反馈`：打开 GitHub Issues 页面

**版权信息**
- 版权声明
- 开源许可证信息

## 🎯 使用技巧

### 服务管理

1. **启动服务前检查**：
   - 确保配置文件存在且格式正确
   - 检查数据目录权限
   - 验证 Python 环境

2. **服务状态监控**：
   - 观察状态指示器颜色变化
   - 查看系统信息中的资源使用情况
   - 定期检查日志输出

3. **故障排除**：
   - 查看日志选项卡的错误信息
   - 使用系统诊断工具检查问题
   - 检查配置文件格式

### 配置管理

1. **配置备份**：
   - 修改配置前先备份
   - 使用版本控制管理配置变更
   - 测试配置后再应用到生产环境

2. **配置验证**：
   - 保存前使用验证功能
   - 注意 YAML 格式的缩进
   - 检查路径是否正确

3. **性能调优**：
   - 根据系统资源调整 `max_memories`
   - 选择合适的 `embedding_model`
   - 配置合理的日志级别

### 数据管理

1. **定期备份**：
   - 设置定期备份计划
   - 保留多个备份版本
   - 测试备份文件的完整性

2. **数据清理**：
   - 定期清理旧数据释放空间
   - 使用预览模式确认清理范围
   - 清理前先备份重要数据

3. **监控存储**：
   - 关注数据目录大小增长
   - 监控磁盘空间使用情况
   - 及时处理存储告警

## 🔧 高级功能

### 自定义主题

GUI 管理器支持自定义主题：

```python
# 在 automem_manager.py 中修改
ctk.set_appearance_mode("light")  # 浅色主题
ctk.set_default_color_theme("green")  # 绿色主题
```

### 快捷键

- `Ctrl+S`：保存配置（在配置选项卡中）
- `Ctrl+R`：刷新日志（在日志选项卡中）
- `F5`：刷新当前选项卡内容
- `Ctrl+Q`：退出应用

### 命令行参数

```cmd
# 指定配置文件启动
python start_gui.py --config custom_config.yaml

# 指定数据目录
python start_gui.py --data-dir custom_data

# 调试模式
python start_gui.py --debug
```

## 🚨 故障排除

### 常见问题

1. **GUI 启动失败**
   ```
   解决方案：
   - 检查 Python 版本（需要 3.8+）
   - 安装 GUI 依赖：pip install -r requirements-gui.txt
   - 检查 tkinter 是否可用
   ```

2. **服务启动失败**
   ```
   解决方案：
   - 检查配置文件格式
   - 验证 AutoMem 模块安装
   - 查看日志选项卡的错误信息
   ```

3. **配置保存失败**
   ```
   解决方案：
   - 检查文件权限
   - 验证 YAML 格式
   - 确保磁盘空间充足
   ```

### 日志分析

查看日志选项卡中的关键信息：

- **启动日志**：服务启动过程和初始化信息
- **错误日志**：红色显示的错误信息
- **警告日志**：黄色显示的警告信息
- **性能日志**：资源使用和性能指标

### 获取帮助

- **系统诊断**：使用工具选项卡的系统诊断功能
- **在线文档**：点击关于选项卡的文档链接
- **社区支持**：通过 GitHub Issues 报告问题

---

**AutoMem GUI 管理器** - 让 AutoMem 管理变得简单直观！🎯
