# 贡献指南

感谢您对 AutoMem 项目的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告 bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ✨ 开发新功能
- 🧪 编写测试
- 📖 翻译文档

## 🚀 快速开始

### 开发环境设置

1. **Fork 仓库**
   ```bash
   # 在 GitHub 上 fork 仓库，然后克隆到本地
   git clone https://github.com/your-username/automem.git
   cd automem
   ```

2. **设置开发环境**
   ```bash
   # 创建虚拟环境
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   # 或 venv\Scripts\activate  # Windows
   
   # 安装开发依赖
   pip install -r requirements.txt
   pip install -e .
   
   # 安装 pre-commit 钩子
   pre-commit install
   ```

3. **验证安装**
   ```bash
   # 运行测试
   pytest
   
   # 检查代码格式
   black --check src/ tests/
   isort --check-only src/ tests/
   
   # 类型检查
   mypy src/
   ```

## 📋 贡献流程

### 1. 创建 Issue

在开始编码之前，请先创建一个 Issue 来讨论您的想法：

- **Bug 报告**：使用 Bug 报告模板
- **功能请求**：使用功能请求模板
- **文档改进**：描述需要改进的部分

### 2. 创建分支

```bash
# 创建并切换到新分支
git checkout -b feature/your-feature-name
# 或
git checkout -b fix/your-bug-fix
```

### 3. 开发和测试

- 编写代码时遵循项目的编码规范
- 为新功能编写测试
- 确保所有测试通过
- 更新相关文档

### 4. 提交代码

```bash
# 添加文件
git add .

# 提交（遵循提交信息规范）
git commit -m "feat: add new memory classification algorithm"

# 推送到您的 fork
git push origin feature/your-feature-name
```

### 5. 创建 Pull Request

- 在 GitHub 上创建 Pull Request
- 填写 PR 模板
- 等待代码审查
- 根据反馈进行修改

## 📝 编码规范

### Python 代码风格

我们使用以下工具来保持代码质量：

- **Black**：代码格式化
- **isort**：导入排序
- **flake8**：代码检查
- **mypy**：类型检查

```bash
# 格式化代码
black src/ tests/
isort src/ tests/

# 检查代码
flake8 src/ tests/
mypy src/
```

### 代码规范要点

1. **命名规范**
   - 类名：`PascalCase`
   - 函数和变量：`snake_case`
   - 常量：`UPPER_SNAKE_CASE`
   - 私有成员：以 `_` 开头

2. **文档字符串**
   ```python
   def function_name(param1: str, param2: int) -> bool:
       """
       函数的简短描述。
       
       Args:
           param1: 参数1的描述
           param2: 参数2的描述
       
       Returns:
           返回值的描述
       
       Raises:
           ValueError: 异常情况的描述
       """
   ```

3. **类型注解**
   - 所有公共函数都应该有类型注解
   - 使用 `typing` 模块的类型
   - 复杂类型使用 `TypeVar` 或 `Generic`

4. **错误处理**
   - 使用具体的异常类型
   - 提供有意义的错误信息
   - 记录重要的异常

### 测试规范

1. **测试文件结构**
   ```
   tests/
   ├── test_core/
   │   ├── test_memory.py
   │   └── test_storage.py
   ├── test_intelligence/
   │   └── test_classifier.py
   └── test_integration/
       └── test_end_to_end.py
   ```

2. **测试命名**
   - 测试类：`TestClassName`
   - 测试方法：`test_method_name_scenario`

3. **测试标记**
   ```python
   @pytest.mark.unit
   def test_memory_creation():
       pass
   
   @pytest.mark.integration
   async def test_full_workflow():
       pass
   ```

## 🐛 Bug 报告

好的 Bug 报告应该包含：

1. **环境信息**
   - 操作系统和版本
   - Python 版本
   - AutoMem 版本
   - 相关依赖版本

2. **重现步骤**
   - 详细的操作步骤
   - 最小化的重现代码
   - 预期行为 vs 实际行为

3. **错误信息**
   - 完整的错误堆栈
   - 相关日志信息
   - 截图（如果适用）

## 💡 功能请求

提出功能请求时，请包含：

1. **问题描述**
   - 当前的限制或问题
   - 为什么需要这个功能

2. **解决方案**
   - 建议的实现方式
   - 可能的替代方案
   - 对现有功能的影响

3. **用例**
   - 具体的使用场景
   - 预期的用户体验

## 📚 文档贡献

### 文档类型

1. **用户文档**
   - 安装指南
   - 使用教程
   - 配置说明
   - 故障排除

2. **开发者文档**
   - API 参考
   - 架构设计
   - 贡献指南
   - 开发环境设置

3. **示例和教程**
   - 代码示例
   - 最佳实践
   - 集成指南

### 文档规范

- 使用 Markdown 格式
- 包含代码示例
- 提供清晰的截图
- 保持内容更新

## 🔍 代码审查

### 审查清单

- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 没有破坏现有功能
- [ ] 性能影响可接受
- [ ] 安全考虑充分

### 审查流程

1. 自动检查通过
2. 至少一个维护者审查
3. 所有讨论得到解决
4. 测试覆盖率满足要求

## 🏷️ 提交信息规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 类型

- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式（不影响功能）
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 示例

```
feat(core): add memory importance scoring algorithm

Implement a new algorithm to automatically score memory importance
based on content analysis and user interaction patterns.

Closes #123
```

## 🎯 发布流程

### 版本管理

- 遵循 [语义化版本](https://semver.org/)
- 主版本：不兼容的 API 变更
- 次版本：向后兼容的新功能
- 修订版本：向后兼容的 bug 修复

### 发布检查清单

- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] CHANGELOG 已更新
- [ ] 版本号已更新
- [ ] 标签已创建

## 🤝 社区准则

### 行为准则

我们致力于为每个人提供友好、安全和欢迎的环境：

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

### 沟通渠道

- **GitHub Issues**: Bug 报告和功能请求
- **GitHub Discussions**: 一般讨论和问答
- **Pull Requests**: 代码审查和讨论

## 📞 获取帮助

如果您在贡献过程中遇到问题：

1. 查看现有的 Issues 和 Discussions
2. 阅读项目文档
3. 在 Discussions 中提问
4. 联系维护者

感谢您的贡献！🙏
