# AutoMem Windows 专用依赖包列表
# 针对 Windows 环境优化，去除 Docker 和 Linux 特定依赖

# 核心框架
mcp[cli]>=1.12.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# CLI 和界面
typer>=0.9.0
rich>=13.0.0
click>=8.0.0

# 异步和网络
aiohttp>=3.8.0
aiofiles>=23.0.0

# 数据库
aiosqlite>=0.19.0
sqlalchemy>=2.0.0

# 向量数据库和嵌入
chromadb>=0.4.0
sentence-transformers>=2.2.0
numpy>=1.24.0
scikit-learn>=1.3.0

# 自然语言处理
jieba>=0.42.0
langdetect>=1.0.9

# 日志和配置
structlog>=23.0.0
python-dateutil>=2.8.0
PyYAML>=6.0
toml>=0.10.2
python-dotenv>=1.0.0

# Windows 特定优化
pywin32>=306; sys_platform == "win32"
colorama>=0.4.6; sys_platform == "win32"

# 测试依赖 (可选)
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 代码质量工具 (可选)
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0

# 性能监控 (可选)
psutil>=5.9.0
memory-profiler>=0.61.0

# 注意：以下依赖在 Windows 上可能需要额外配置，已移除：
# - docker (Docker 相关)
# - kubernetes (K8s 相关)
# - prometheus-client (如需监控可单独安装)
# - uvicorn (如需 HTTP 服务可单独安装)
# - gunicorn (Linux 专用)

# 如果需要 HTTP 服务器功能，可以安装：
# uvicorn>=0.23.0
# fastapi>=0.104.0

# 如果需要监控功能，可以安装：
# prometheus-client>=0.17.0

# 如果需要加密功能，可以安装：
# cryptography>=41.0.0
# bcrypt>=4.0.0
