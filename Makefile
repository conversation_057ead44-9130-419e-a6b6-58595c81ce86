# AutoMem 开发工具

.PHONY: help install dev test lint format clean run config

# 默认目标
help:
	@echo "AutoMem 开发命令:"
	@echo "  install     - 安装项目依赖"
	@echo "  dev         - 安装开发依赖"
	@echo "  test        - 运行测试"
	@echo "  lint        - 代码检查"
	@echo "  format      - 代码格式化"
	@echo "  clean       - 清理临时文件"
	@echo "  run         - 运行服务器"
	@echo "  config      - 生成配置文件"

# 安装依赖
install:
	pip install -e .

# 安装开发依赖
dev:
	pip install -e ".[dev]"
	pre-commit install

# 运行测试
test:
	pytest tests/ -v --cov=automem --cov-report=term-missing

# 代码检查
lint:
	flake8 src/ tests/
	mypy src/automem/

# 代码格式化
format:
	black src/ tests/
	isort src/ tests/

# 清理临时文件
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/ dist/ .coverage htmlcov/ .pytest_cache/ .mypy_cache/

# 运行服务器 (stdio模式)
run:
	python -m automem.cli serve --stdio --dev

# 运行服务器 (HTTP模式)
run-http:
	python -m automem.cli serve --host localhost --port 8000 --dev

# 生成配置文件
config:
	python -m automem.cli config --init --output config.yaml

# 显示配置
show-config:
	python -m automem.cli config --show

# 检查服务器状态
status:
	python -m automem.cli status

# 显示版本
version:
	python -m automem.cli version

# 构建分发包
build:
	python -m build

# 安装pre-commit钩子
pre-commit:
	pre-commit install
	pre-commit run --all-files

# 更新依赖
update:
	pip install --upgrade pip
	pip install --upgrade -e ".[dev]"

# 运行性能测试
benchmark:
	@echo "性能测试功能开发中..."

# 生成文档
docs:
	@echo "文档生成功能开发中..."

# Docker相关命令
docker-build:
	docker build -t automem:latest .

docker-run:
	docker run -it --rm -p 8000:8000 automem:latest

# 数据库迁移 (未来功能)
migrate:
	@echo "数据库迁移功能开发中..."

# 备份数据
backup:
	@echo "数据备份功能开发中..."
