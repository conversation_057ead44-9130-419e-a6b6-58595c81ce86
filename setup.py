#!/usr/bin/env python3
"""
AutoMem 安装脚本
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取 README 文件
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding="utf-8")

# 读取 requirements.txt
def read_requirements(filename):
    """读取依赖文件"""
    requirements = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 移除注释
                    if '#' in line:
                        line = line[:line.index('#')].strip()
                    if line:
                        requirements.append(line)
    except FileNotFoundError:
        pass
    return requirements

# 基础依赖
install_requires = [
    "mcp[cli]>=1.12.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "typer>=0.9.0",
    "rich>=13.0.0",
    "aiohttp>=3.8.0",
    "aiofiles>=23.0.0",
    "aiosqlite>=0.19.0",
    "chromadb>=0.4.0",
    "sentence-transformers>=2.2.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "jieba>=0.42.0",
    "structlog>=23.0.0",
    "python-dateutil>=2.8.0",
    "PyYAML>=6.0",
]

# 可选依赖
extras_require = {
    "dev": [
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.1.0",
        "pytest-mock>=3.11.0",
        "black>=23.0.0",
        "isort>=5.12.0",
        "flake8>=6.0.0",
        "mypy>=1.5.0",
        "pre-commit>=3.4.0",
    ],
    "docs": [
        "mkdocs>=1.5.0",
        "mkdocs-material>=9.2.0",
        "mkdocs-mermaid2-plugin>=1.1.0",
    ],
    "monitoring": [
        "prometheus-client>=0.17.0",
        "psutil>=5.9.0",
    ],
    "security": [
        "cryptography>=41.0.0",
        "bcrypt>=4.0.0",
    ],
    "performance": [
        "memory-profiler>=0.61.0",
        "line-profiler>=4.1.0",
    ],
    "ml": [
        "torch>=2.0.0",
        "transformers>=4.30.0",
        "datasets>=2.14.0",
    ],
}

# 所有可选依赖
extras_require["all"] = list(set(
    dep for deps in extras_require.values() for dep in deps
))

setup(
    name="automem",
    version="0.1.0",
    author="AutoMem Team",
    author_email="<EMAIL>",
    description="智能记忆增强MCP服务器 - 让AI拥有持久记忆",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/automem",
    project_urls={
        "Bug Reports": "https://github.com/your-org/automem/issues",
        "Source": "https://github.com/your-org/automem",
        "Documentation": "https://automem.readthedocs.io/",
        "Changelog": "https://github.com/your-org/automem/blob/main/CHANGELOG.md",
    },
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: System :: Distributed Computing",
        "Typing :: Typed",
    ],
    python_requires=">=3.8",
    install_requires=install_requires,
    extras_require=extras_require,
    entry_points={
        "console_scripts": [
            "automem=automem.cli:app",
        ],
    },
    include_package_data=True,
    package_data={
        "automem": [
            "config/*.yaml",
            "config/*.yml",
            "templates/*.j2",
            "static/*",
        ],
    },
    zip_safe=False,
    keywords=[
        "mcp",
        "memory",
        "ai",
        "artificial-intelligence",
        "machine-learning",
        "nlp",
        "semantic-search",
        "vector-database",
        "context-management",
        "intelligent-assistant",
    ],
    platforms=["any"],
    license="MIT",
    test_suite="tests",
    tests_require=[
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.1.0",
    ],
)
