"""
AutoMem MCP资源接口

提供记忆数据的MCP资源。
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone, timedelta
import json

from ..utils.logging import LoggerMixin
from ..intelligence.autonomous import AutonomousMemorySystem


class AutoMemResources(LoggerMixin):
    """AutoMem MCP资源"""
    
    def __init__(self, autonomous_system: AutonomousMemorySystem):
        self.autonomous_system = autonomous_system
    
    async def get_recent_memories(self, hours: int = 24, limit: int = 10) -> str:
        """获取最近的记忆
        
        Args:
            hours: 时间范围（小时）
            limit: 结果数量限制
        
        Returns:
            格式化的记忆列表
        """
        try:
            recent_memories = await self.autonomous_system.storage.get_recent_memories(
                hours=hours, limit=limit
            )
            
            if not recent_memories:
                return f"# 最近 {hours} 小时内的记忆\n\n暂无记忆。"
            
            content = f"# 最近 {hours} 小时内的记忆\n\n"
            content += f"共找到 {len(recent_memories)} 个记忆：\n\n"
            
            for i, memory in enumerate(recent_memories, 1):
                age_hours = (datetime.now(timezone.utc) - memory.created_at).total_seconds() / 3600
                
                content += f"## {i}. 记忆 {memory.id}\n\n"
                content += f"**内容**: {memory.content}\n\n"
                content += f"**类型**: {memory.memory_type.value}\n"
                content += f"**重要性**: {memory.importance.value}\n"
                content += f"**创建时间**: {memory.created_at.strftime('%Y-%m-%d %H:%M:%S')} ({age_hours:.1f}小时前)\n"
                content += f"**访问次数**: {memory.access_count}\n"
                
                if memory.tags:
                    content += f"**标签**: {', '.join(memory.tags)}\n"
                
                if memory.categories:
                    content += f"**分类**: {', '.join(memory.categories)}\n"
                
                content += "\n---\n\n"
            
            return content
            
        except Exception as e:
            self.logger.error(f"获取最近记忆失败: {e}")
            return f"# 错误\n\n获取最近记忆时发生错误: {e}"
    
    async def get_context_memories(self, topic: str, limit: int = 5) -> str:
        """获取特定主题的上下文记忆
        
        Args:
            topic: 主题关键词
            limit: 结果数量限制
        
        Returns:
            格式化的上下文记忆
        """
        try:
            from ..retrieval.context_retriever import ContextRetriever, ConversationContext
            
            # 构建上下文检索器
            context_retriever = ContextRetriever(
                self.autonomous_system.retrieval_config,
                self.autonomous_system.storage,
                None
            )
            
            # 构建对话上下文
            context = ConversationContext(topics=[topic])
            
            # 检索上下文记忆
            contextual_memories = await context_retriever.retrieve_context(
                query=topic,
                context=context,
                limit=limit
            )
            
            if not contextual_memories:
                return f"# 主题 '{topic}' 的相关记忆\n\n暂无相关记忆。"
            
            content = f"# 主题 '{topic}' 的相关记忆\n\n"
            content += f"共找到 {len(contextual_memories)} 个相关记忆：\n\n"
            
            for i, contextual_memory in enumerate(contextual_memories, 1):
                memory = contextual_memory.memory
                
                content += f"## {i}. 记忆 {memory.id}\n\n"
                content += f"**内容**: {memory.content}\n\n"
                content += f"**相关性得分**: {contextual_memory.context_score:.2f}\n"
                content += f"**关系类型**: {contextual_memory.relationship_type}\n"
                content += f"**匹配原因**: {', '.join(contextual_memory.context_reasons)}\n"
                content += f"**重要性**: {memory.importance.value}\n"
                content += f"**创建时间**: {memory.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                
                if memory.tags:
                    content += f"**标签**: {', '.join(memory.tags)}\n"
                
                content += "\n---\n\n"
            
            return content
            
        except Exception as e:
            self.logger.error(f"获取上下文记忆失败: {e}")
            return f"# 错误\n\n获取主题 '{topic}' 的记忆时发生错误: {e}"
    
    async def get_timeline_memories(self, date: str, format: str = "daily") -> str:
        """获取特定日期的记忆
        
        Args:
            date: 日期字符串 (YYYY-MM-DD)
            format: 格式类型 (daily, weekly, monthly)
        
        Returns:
            格式化的时间线记忆
        """
        try:
            from ..retrieval.timeline import TimelineManager
            
            timeline_manager = TimelineManager(
                self.autonomous_system.storage_config,
                self.autonomous_system.storage
            )
            
            # 解析日期
            try:
                target_date = datetime.strptime(date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            except ValueError:
                return f"# 错误\n\n无效的日期格式: {date}，请使用 YYYY-MM-DD 格式。"
            
            if format == "daily":
                start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = start_date + timedelta(days=1)
                title = f"日期 {date} 的记忆"
            elif format == "weekly":
                # 获取该周的记忆
                days_since_monday = target_date.weekday()
                start_date = target_date - timedelta(days=days_since_monday)
                start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = start_date + timedelta(days=7)
                title = f"周 {start_date.strftime('%Y-%m-%d')} 到 {(end_date - timedelta(days=1)).strftime('%Y-%m-%d')} 的记忆"
            elif format == "monthly":
                # 获取该月的记忆
                start_date = target_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                if start_date.month == 12:
                    end_date = start_date.replace(year=start_date.year + 1, month=1)
                else:
                    end_date = start_date.replace(month=start_date.month + 1)
                title = f"月份 {target_date.strftime('%Y-%m')} 的记忆"
            else:
                return f"# 错误\n\n不支持的格式类型: {format}"
            
            # 获取时间范围内的记忆
            memories = await timeline_manager.get_memories_by_date_range(
                start_date, end_date, limit=100
            )
            
            if not memories:
                return f"# {title}\n\n该时间段内暂无记忆。"
            
            content = f"# {title}\n\n"
            content += f"共找到 {len(memories)} 个记忆：\n\n"
            
            # 按日期分组
            memories_by_date = {}
            for memory in memories:
                date_key = memory.created_at.strftime("%Y-%m-%d")
                if date_key not in memories_by_date:
                    memories_by_date[date_key] = []
                memories_by_date[date_key].append(memory)
            
            # 按日期排序显示
            for date_key in sorted(memories_by_date.keys(), reverse=True):
                day_memories = memories_by_date[date_key]
                content += f"## {date_key} ({len(day_memories)} 个记忆)\n\n"
                
                for memory in day_memories:
                    time_str = memory.created_at.strftime("%H:%M")
                    content += f"- **{time_str}** - {memory.content[:100]}"
                    if len(memory.content) > 100:
                        content += "..."
                    content += f" *({memory.importance.value})*\n"
                
                content += "\n"
            
            return content
            
        except Exception as e:
            self.logger.error(f"获取时间线记忆失败: {e}")
            return f"# 错误\n\n获取日期 '{date}' 的记忆时发生错误: {e}"
    
    async def get_tagged_memories(self, tag: str, limit: int = 20) -> str:
        """获取特定标签的记忆
        
        Args:
            tag: 标签名称
            limit: 结果数量限制
        
        Returns:
            格式化的标签记忆
        """
        try:
            from ..core.memory import MemoryQuery
            
            # 搜索带有指定标签的记忆
            query = MemoryQuery(
                tags=[tag],
                sort_by="created_at",
                sort_order="desc",
                limit=limit
            )
            
            memories = await self.autonomous_system.storage.search_memories(query)
            
            if not memories:
                return f"# 标签 '{tag}' 的记忆\n\n暂无带有此标签的记忆。"
            
            content = f"# 标签 '{tag}' 的记忆\n\n"
            content += f"共找到 {len(memories)} 个记忆：\n\n"
            
            for i, memory in enumerate(memories, 1):
                content += f"## {i}. 记忆 {memory.id}\n\n"
                content += f"**内容**: {memory.content}\n\n"
                content += f"**类型**: {memory.memory_type.value}\n"
                content += f"**重要性**: {memory.importance.value}\n"
                content += f"**创建时间**: {memory.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                content += f"**访问次数**: {memory.access_count}\n"
                
                # 显示所有标签，高亮当前标签
                if memory.tags:
                    tag_list = []
                    for t in memory.tags:
                        if t == tag:
                            tag_list.append(f"**{t}**")
                        else:
                            tag_list.append(t)
                    content += f"**标签**: {', '.join(tag_list)}\n"
                
                if memory.categories:
                    content += f"**分类**: {', '.join(memory.categories)}\n"
                
                content += "\n---\n\n"
            
            return content
            
        except Exception as e:
            self.logger.error(f"获取标签记忆失败: {e}")
            return f"# 错误\n\n获取标签 '{tag}' 的记忆时发生错误: {e}"
    
    async def get_memory_summary(self) -> str:
        """获取记忆系统摘要
        
        Returns:
            格式化的系统摘要
        """
        try:
            # 获取统计信息
            basic_stats = await self.autonomous_system.storage.get_memory_stats()
            priority_stats = await self.autonomous_system.priority_manager.get_priority_statistics()
            lifecycle_stats = await self.autonomous_system.lifecycle_manager.get_lifecycle_statistics()
            autonomous_insights = await self.autonomous_system.get_autonomous_insights()
            
            content = "# AutoMem 记忆系统摘要\n\n"
            content += f"**生成时间**: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}\n\n"
            
            # 基础统计
            content += "## 基础统计\n\n"
            content += f"- **总记忆数**: {basic_stats.get('total_memories', 0)}\n"
            content += f"- **本周新增**: {basic_stats.get('recent_week', 0)}\n"
            
            if basic_stats.get('by_type'):
                content += "- **按类型分布**:\n"
                for mem_type, count in basic_stats['by_type'].items():
                    content += f"  - {mem_type}: {count}\n"
            
            if basic_stats.get('by_importance'):
                content += "- **按重要性分布**:\n"
                for importance, count in basic_stats['by_importance'].items():
                    content += f"  - {importance}: {count}\n"
            
            # 自主系统状态
            content += "\n## 自主系统状态\n\n"
            content += f"- **总执行行动**: {autonomous_insights.get('total_actions', 0)}\n"
            content += f"- **成功率**: {autonomous_insights.get('success_rate', 0):.1f}%\n"
            
            if autonomous_insights.get('recent_actions'):
                content += "- **最近行动**:\n"
                for action in autonomous_insights['recent_actions'][:5]:
                    content += f"  - {action['action_type']}: {action['reasoning'][:50]}...\n"
            
            # 生命周期分布
            if lifecycle_stats.get('current_distribution'):
                content += "\n## 生命周期分布\n\n"
                for stage, info in lifecycle_stats['current_distribution'].items():
                    content += f"- **{stage}**: {info.get('count', 0)} ({info.get('percentage', 0):.1f}%)\n"
            
            return content
            
        except Exception as e:
            self.logger.error(f"获取系统摘要失败: {e}")
            return f"# 错误\n\n获取系统摘要时发生错误: {e}"
