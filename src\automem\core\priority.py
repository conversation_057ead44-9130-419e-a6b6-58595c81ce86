"""
记忆优先级管理器

管理记忆的优先级，实现近期记忆优先和智能归档。
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Set, Any, Optional, Tuple
from enum import Enum
from uuid import UUID
from collections import defaultdict

from ..config.settings import StorageConfig
from ..utils.logging import LoggerMixin
from .memory import Memory, MemoryImportance, MemoryType
from .storage import StorageEngine


class MemoryPriority(str, Enum):
    """记忆优先级"""
    URGENT = "urgent"        # 紧急 - 立即处理
    HIGH = "high"           # 高 - 优先处理
    NORMAL = "normal"       # 正常 - 常规处理
    LOW = "low"            # 低 - 延后处理
    ARCHIVE = "archive"     # 归档 - 长期存储


class PriorityRule:
    """优先级规则"""
    
    def __init__(
        self,
        name: str,
        condition: callable,
        priority: MemoryPriority,
        weight: float = 1.0,
        description: str = ""
    ):
        self.name = name
        self.condition = condition
        self.priority = priority
        self.weight = weight
        self.description = description


class MemoryPriorityManager(LoggerMixin):
    """记忆优先级管理器"""
    
    def __init__(self, config: StorageConfig, storage: StorageEngine):
        self.config = config
        self.storage = storage
        
        # 优先级规则
        self.priority_rules = self._initialize_priority_rules()
        
        # 优先级统计
        self.priority_stats = {
            "total_evaluations": 0,
            "priority_distribution": defaultdict(int),
            "rule_applications": defaultdict(int),
        }
        
        # 归档配置
        self.archive_config = {
            "auto_archive_days": 90,      # 自动归档天数
            "min_access_count": 2,        # 最小访问次数
            "importance_threshold": MemoryImportance.MEDIUM,  # 重要性阈值
        }
    
    def _initialize_priority_rules(self) -> List[PriorityRule]:
        """初始化优先级规则"""
        return [
            # 紧急规则
            PriorityRule(
                name="critical_importance",
                condition=lambda m: m.importance == MemoryImportance.CRITICAL,
                priority=MemoryPriority.URGENT,
                weight=2.0,
                description="关键重要性记忆"
            ),
            
            PriorityRule(
                name="recent_high_access",
                condition=lambda m: m.is_recent(hours=6) and m.access_count > 5,
                priority=MemoryPriority.URGENT,
                weight=1.5,
                description="最近高频访问记忆"
            ),
            
            # 高优先级规则
            PriorityRule(
                name="high_importance",
                condition=lambda m: m.importance == MemoryImportance.HIGH,
                priority=MemoryPriority.HIGH,
                weight=1.5,
                description="高重要性记忆"
            ),
            
            PriorityRule(
                name="recent_memory",
                condition=lambda m: m.is_recent(hours=24),
                priority=MemoryPriority.HIGH,
                weight=1.2,
                description="最近24小时记忆"
            ),
            
            PriorityRule(
                name="frequently_accessed",
                condition=lambda m: m.access_count > 10,
                priority=MemoryPriority.HIGH,
                weight=1.3,
                description="高频访问记忆"
            ),
            
            # 正常优先级规则
            PriorityRule(
                name="medium_importance",
                condition=lambda m: m.importance == MemoryImportance.MEDIUM,
                priority=MemoryPriority.NORMAL,
                weight=1.0,
                description="中等重要性记忆"
            ),
            
            PriorityRule(
                name="this_week",
                condition=lambda m: m.is_recent(hours=168),  # 一周
                priority=MemoryPriority.NORMAL,
                weight=1.0,
                description="本周记忆"
            ),
            
            # 低优先级规则
            PriorityRule(
                name="low_importance",
                condition=lambda m: m.importance == MemoryImportance.LOW,
                priority=MemoryPriority.LOW,
                weight=0.8,
                description="低重要性记忆"
            ),
            
            PriorityRule(
                name="old_unused",
                condition=lambda m: m.age_in_days() > 30 and m.access_count <= 2,
                priority=MemoryPriority.LOW,
                weight=0.5,
                description="旧的未使用记忆"
            ),
            
            # 归档规则
            PriorityRule(
                name="minimal_importance",
                condition=lambda m: m.importance == MemoryImportance.MINIMAL,
                priority=MemoryPriority.ARCHIVE,
                weight=0.3,
                description="最低重要性记忆"
            ),
            
            PriorityRule(
                name="very_old_unused",
                condition=lambda m: m.age_in_days() > 90 and m.access_count == 0,
                priority=MemoryPriority.ARCHIVE,
                weight=0.2,
                description="很旧的未访问记忆"
            ),
        ]
    
    async def calculate_priority(self, memory: Memory) -> Tuple[MemoryPriority, float, List[str]]:
        """计算记忆优先级"""
        
        # 更新统计
        self.priority_stats["total_evaluations"] += 1
        
        # 应用所有规则
        priority_scores = defaultdict(float)
        applied_rules = []
        
        for rule in self.priority_rules:
            try:
                if rule.condition(memory):
                    priority_scores[rule.priority] += rule.weight
                    applied_rules.append(rule.name)
                    self.priority_stats["rule_applications"][rule.name] += 1
            except Exception as e:
                self.logger.warning(f"优先级规则 {rule.name} 执行失败: {e}")
        
        # 确定最终优先级
        if not priority_scores:
            final_priority = MemoryPriority.NORMAL
            final_score = 1.0
        else:
            # 选择得分最高的优先级
            final_priority = max(priority_scores.items(), key=lambda x: x[1])[0]
            final_score = priority_scores[final_priority]
        
        # 更新统计
        self.priority_stats["priority_distribution"][final_priority.value] += 1
        
        self.logger.debug(f"记忆 {memory.id} 优先级: {final_priority.value} (得分: {final_score:.2f})")
        
        return final_priority, final_score, applied_rules
    
    async def get_prioritized_memories(
        self,
        limit: int = 50,
        priority_filter: Optional[List[MemoryPriority]] = None,
        time_window_hours: Optional[int] = None
    ) -> List[Tuple[Memory, MemoryPriority, float]]:
        """获取按优先级排序的记忆"""
        
        # 构建查询条件
        query_kwargs = {"limit": limit * 2}  # 获取更多候选
        
        if time_window_hours:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=time_window_hours)
            query_kwargs["created_after"] = cutoff_time
        
        # 获取记忆
        from .memory import MemoryQuery
        query = MemoryQuery(**query_kwargs)
        memories = await self.storage.search_memories(query)
        
        # 计算优先级
        prioritized_memories = []
        for memory in memories:
            priority, score, _ = await self.calculate_priority(memory)
            
            # 应用过滤器
            if priority_filter and priority not in priority_filter:
                continue
            
            prioritized_memories.append((memory, priority, score))
        
        # 按优先级和得分排序
        priority_order = {
            MemoryPriority.URGENT: 4,
            MemoryPriority.HIGH: 3,
            MemoryPriority.NORMAL: 2,
            MemoryPriority.LOW: 1,
            MemoryPriority.ARCHIVE: 0,
        }
        
        prioritized_memories.sort(
            key=lambda x: (priority_order[x[1]], x[2]),
            reverse=True
        )
        
        return prioritized_memories[:limit]
    
    async def get_urgent_memories(self, limit: int = 10) -> List[Memory]:
        """获取紧急记忆"""
        prioritized = await self.get_prioritized_memories(
            limit=limit,
            priority_filter=[MemoryPriority.URGENT]
        )
        return [memory for memory, _, _ in prioritized]
    
    async def get_archive_candidates(
        self,
        limit: int = 100,
        min_age_days: int = None
    ) -> List[Memory]:
        """获取归档候选记忆"""
        
        min_age_days = min_age_days or self.archive_config["auto_archive_days"]
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=min_age_days)
        
        # 获取旧记忆
        from .memory import MemoryQuery
        query = MemoryQuery(
            created_before=cutoff_time,
            limit=limit * 2
        )
        old_memories = await self.storage.search_memories(query)
        
        # 筛选归档候选
        candidates = []
        for memory in old_memories:
            priority, _, _ = await self.calculate_priority(memory)
            
            # 归档条件
            should_archive = (
                priority == MemoryPriority.ARCHIVE or
                (memory.importance in [MemoryImportance.LOW, MemoryImportance.MINIMAL] and
                 memory.access_count <= self.archive_config["min_access_count"])
            )
            
            if should_archive:
                candidates.append(memory)
        
        return candidates[:limit]
    
    async def auto_archive_memories(self, dry_run: bool = True) -> Dict[str, Any]:
        """自动归档记忆"""
        
        candidates = await self.get_archive_candidates()
        
        if dry_run:
            return {
                "action": "dry_run",
                "candidates_count": len(candidates),
                "candidates": [
                    {
                        "id": str(memory.id),
                        "content": memory.content[:50] + "...",
                        "age_days": memory.age_in_days(),
                        "access_count": memory.access_count,
                        "importance": memory.importance.value,
                    }
                    for memory in candidates[:10]  # 只显示前10个
                ]
            }
        
        # 实际归档
        archived_count = 0
        for memory in candidates:
            # 更新记忆元数据标记为已归档
            memory.metadata["archived"] = True
            memory.metadata["archived_at"] = datetime.now(timezone.utc).isoformat()
            
            await self.storage.update_memory(memory)
            archived_count += 1
        
        self.logger.info(f"自动归档了 {archived_count} 个记忆")
        
        return {
            "action": "archived",
            "archived_count": archived_count,
            "total_candidates": len(candidates),
        }
    
    async def promote_memory_priority(self, memory_id: UUID, reason: str = "") -> bool:
        """提升记忆优先级"""
        memory = await self.storage.get_memory(memory_id)
        if not memory:
            return False
        
        # 增加访问计数
        memory.update_access()
        
        # 如果有原因，添加到元数据
        if reason:
            if "priority_promotions" not in memory.metadata:
                memory.metadata["priority_promotions"] = []
            
            memory.metadata["priority_promotions"].append({
                "reason": reason,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            })
        
        await self.storage.update_memory(memory)
        
        self.logger.info(f"提升记忆优先级: {memory_id} - {reason}")
        return True
    
    async def get_priority_statistics(self) -> Dict[str, Any]:
        """获取优先级统计信息"""
        
        total_evaluations = self.priority_stats["total_evaluations"]
        
        stats = {
            "total_evaluations": total_evaluations,
            "priority_distribution": dict(self.priority_stats["priority_distribution"]),
            "rule_applications": dict(self.priority_stats["rule_applications"]),
            "priority_percentages": {},
            "most_applied_rules": [],
        }
        
        # 计算优先级百分比
        if total_evaluations > 0:
            for priority, count in self.priority_stats["priority_distribution"].items():
                stats["priority_percentages"][priority] = (count / total_evaluations) * 100
        
        # 最常应用的规则
        if self.priority_stats["rule_applications"]:
            sorted_rules = sorted(
                self.priority_stats["rule_applications"].items(),
                key=lambda x: x[1],
                reverse=True
            )
            stats["most_applied_rules"] = sorted_rules[:5]
        
        return stats
    
    async def update_archive_config(self, **kwargs) -> None:
        """更新归档配置"""
        for key, value in kwargs.items():
            if key in self.archive_config:
                self.archive_config[key] = value
                self.logger.info(f"归档配置已更新: {key} = {value}")
    
    async def add_priority_rule(self, rule: PriorityRule) -> None:
        """添加优先级规则"""
        self.priority_rules.append(rule)
        self.logger.info(f"添加优先级规则: {rule.name}")
    
    async def remove_priority_rule(self, rule_name: str) -> bool:
        """移除优先级规则"""
        for i, rule in enumerate(self.priority_rules):
            if rule.name == rule_name:
                del self.priority_rules[i]
                self.logger.info(f"移除优先级规则: {rule_name}")
                return True
        return False
    
    async def reset_statistics(self) -> None:
        """重置统计信息"""
        self.priority_stats = {
            "total_evaluations": 0,
            "priority_distribution": defaultdict(int),
            "rule_applications": defaultdict(int),
        }
        self.logger.info("优先级统计信息已重置")
