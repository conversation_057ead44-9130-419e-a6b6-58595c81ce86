"""
AutoMem配置设置

使用Pydantic Settings进行配置管理，支持环境变量和配置文件。
"""

from pathlib import Path
from typing import Optional, List, Dict, Any
from datetime import timedelta

from pydantic import Field, validator
from pydantic_settings import BaseSettings
from pydantic_settings import BaseSettings as PydanticBaseSettings


class StorageConfig(BaseSettings):
    """存储配置"""
    
    data_dir: Path = Field(
        default=Path("./data"),
        description="数据存储目录"
    )
    max_memories: int = Field(
        default=10000,
        description="最大记忆数量"
    )
    cleanup_interval: str = Field(
        default="24h",
        description="清理间隔时间"
    )
    backup_enabled: bool = Field(
        default=True,
        description="是否启用备份"
    )
    backup_interval: str = Field(
        default="7d",
        description="备份间隔时间"
    )
    
    @validator('data_dir')
    def validate_data_dir(cls, v):
        """验证数据目录"""
        if isinstance(v, str):
            v = Path(v)
        v.mkdir(parents=True, exist_ok=True)
        return v
    
    class Config:
        env_prefix = "AUTOMEM_STORAGE_"


class IntelligenceConfig(BaseSettings):
    """智能层配置"""
    
    embedding_model: str = Field(
        default="all-MiniLM-L6-v2",
        description="嵌入模型名称"
    )
    classification_threshold: float = Field(
        default=0.7,
        description="分类阈值"
    )
    auto_tag_enabled: bool = Field(
        default=True,
        description="是否启用自动标签"
    )
    max_tags_per_memory: int = Field(
        default=5,
        description="每个记忆的最大标签数"
    )
    importance_threshold: float = Field(
        default=0.5,
        description="重要性阈值"
    )
    language_detection: bool = Field(
        default=True,
        description="是否启用语言检测"
    )
    supported_languages: List[str] = Field(
        default=["zh", "en"],
        description="支持的语言列表"
    )
    
    @validator('classification_threshold', 'importance_threshold')
    def validate_threshold(cls, v):
        """验证阈值范围"""
        if not 0 <= v <= 1:
            raise ValueError("阈值必须在0-1之间")
        return v
    
    class Config:
        env_prefix = "AUTOMEM_INTELLIGENCE_"


class RetrievalConfig(BaseSettings):
    """检索配置"""
    
    max_results: int = Field(
        default=10,
        description="最大检索结果数"
    )
    similarity_threshold: float = Field(
        default=0.6,
        description="相似度阈值"
    )
    time_decay_factor: float = Field(
        default=0.1,
        description="时间衰减因子"
    )
    context_window_size: int = Field(
        default=5,
        description="上下文窗口大小"
    )
    enable_cross_reference: bool = Field(
        default=True,
        description="是否启用交叉引用"
    )
    semantic_search_enabled: bool = Field(
        default=True,
        description="是否启用语义搜索"
    )
    
    @validator('similarity_threshold')
    def validate_similarity_threshold(cls, v):
        """验证相似度阈值"""
        if not 0 <= v <= 1:
            raise ValueError("相似度阈值必须在0-1之间")
        return v
    
    class Config:
        env_prefix = "AUTOMEM_RETRIEVAL_"


class LoggingConfig(BaseSettings):
    """日志配置"""
    
    level: str = Field(
        default="INFO",
        description="日志级别"
    )
    file: Optional[Path] = Field(
        default=Path("./logs/automem.log"),
        description="日志文件路径"
    )
    max_file_size: str = Field(
        default="10MB",
        description="最大日志文件大小"
    )
    backup_count: int = Field(
        default=5,
        description="日志备份文件数量"
    )
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    enable_console: bool = Field(
        default=True,
        description="是否启用控制台输出"
    )
    
    @validator('level')
    def validate_level(cls, v):
        """验证日志级别"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"日志级别必须是: {valid_levels}")
        return v.upper()
    
    @validator('file')
    def validate_log_file(cls, v):
        """验证日志文件路径"""
        if v:
            if isinstance(v, str):
                v = Path(v)
            v.parent.mkdir(parents=True, exist_ok=True)
        return v
    
    class Config:
        env_prefix = "AUTOMEM_LOGGING_"


class AutoMemConfig(PydanticBaseSettings):
    """AutoMem主配置"""
    
    # 基本设置
    server_name: str = Field(
        default="AutoMem",
        description="服务器名称"
    )
    server_version: str = Field(
        default="0.1.0",
        description="服务器版本"
    )
    debug: bool = Field(
        default=False,
        description="是否启用调试模式"
    )
    
    # 子配置
    storage: StorageConfig = Field(
        default_factory=StorageConfig,
        description="存储配置"
    )
    intelligence: IntelligenceConfig = Field(
        default_factory=IntelligenceConfig,
        description="智能层配置"
    )
    retrieval: RetrievalConfig = Field(
        default_factory=RetrievalConfig,
        description="检索配置"
    )
    logging: LoggingConfig = Field(
        default_factory=LoggingConfig,
        description="日志配置"
    )
    
    # MCP设置
    mcp_transport: str = Field(
        default="stdio",
        description="MCP传输协议"
    )
    mcp_host: str = Field(
        default="localhost",
        description="MCP主机地址"
    )
    mcp_port: int = Field(
        default=8000,
        description="MCP端口"
    )
    
    # 高级设置
    experimental_features: Dict[str, Any] = Field(
        default_factory=dict,
        description="实验性功能"
    )
    
    class Config:
        env_prefix = "AUTOMEM_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @classmethod
    def load_from_file(cls, config_path: Path) -> "AutoMemConfig":
        """从配置文件加载配置"""
        import yaml
        
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        return cls(**config_data)
    
    def save_to_file(self, config_path: Path) -> None:
        """保存配置到文件"""
        import yaml
        
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(
                self.dict(),
                f,
                default_flow_style=False,
                allow_unicode=True,
                indent=2
            )
