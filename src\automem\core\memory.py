"""
记忆数据模型和管理器

定义记忆的数据结构和基本操作。
"""

from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Set
from uuid import uuid4, UUID
from enum import Enum

from pydantic import BaseModel, Field, validator


class MemoryType(str, Enum):
    """记忆类型枚举"""
    CONVERSATION = "conversation"  # 对话记忆
    FACT = "fact"                 # 事实记忆
    PROCEDURE = "procedure"       # 程序记忆
    EPISODIC = "episodic"        # 情节记忆
    SEMANTIC = "semantic"        # 语义记忆


class MemoryImportance(str, Enum):
    """记忆重要性级别"""
    CRITICAL = "critical"    # 关键
    HIGH = "high"           # 高
    MEDIUM = "medium"       # 中
    LOW = "low"            # 低
    MINIMAL = "minimal"     # 最低


class Memory(BaseModel):
    """记忆数据模型"""
    
    # 基本信息
    id: UUID = Field(default_factory=uuid4, description="记忆唯一标识")
    content: str = Field(..., description="记忆内容")
    summary: Optional[str] = Field(None, description="记忆摘要")
    
    # 分类信息
    memory_type: MemoryType = Field(default=MemoryType.CONVERSATION, description="记忆类型")
    importance: MemoryImportance = Field(default=MemoryImportance.MEDIUM, description="重要性级别")
    tags: Set[str] = Field(default_factory=set, description="标签集合")
    categories: Set[str] = Field(default_factory=set, description="分类集合")
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="创建时间")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="更新时间")
    accessed_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="最后访问时间")
    
    # 上下文信息
    context: Dict[str, Any] = Field(default_factory=dict, description="上下文信息")
    session_id: Optional[str] = Field(None, description="会话ID")
    conversation_id: Optional[str] = Field(None, description="对话ID")
    
    # 关联信息
    related_memories: Set[UUID] = Field(default_factory=set, description="相关记忆ID集合")
    source: Optional[str] = Field(None, description="记忆来源")
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    embedding: Optional[List[float]] = Field(None, description="向量嵌入")
    
    # 统计信息
    access_count: int = Field(default=0, description="访问次数")
    relevance_score: float = Field(default=0.0, description="相关性评分")
    
    @validator('content')
    def validate_content(cls, v):
        """验证内容不为空"""
        if not v or not v.strip():
            raise ValueError("记忆内容不能为空")
        return v.strip()
    
    @validator('tags', 'categories', pre=True)
    def validate_tags_categories(cls, v):
        """验证标签和分类"""
        if isinstance(v, (list, tuple)):
            return set(v)
        return v
    
    @validator('relevance_score')
    def validate_relevance_score(cls, v):
        """验证相关性评分范围"""
        if not 0 <= v <= 1:
            raise ValueError("相关性评分必须在0-1之间")
        return v
    
    def update_access(self) -> None:
        """更新访问信息"""
        self.accessed_at = datetime.now(timezone.utc)
        self.access_count += 1
    
    def add_tag(self, tag: str) -> None:
        """添加标签"""
        if tag and tag.strip():
            self.tags.add(tag.strip().lower())
            self.updated_at = datetime.now(timezone.utc)
    
    def remove_tag(self, tag: str) -> None:
        """移除标签"""
        self.tags.discard(tag.strip().lower())
        self.updated_at = datetime.now(timezone.utc)
    
    def add_category(self, category: str) -> None:
        """添加分类"""
        if category and category.strip():
            self.categories.add(category.strip().lower())
            self.updated_at = datetime.now(timezone.utc)
    
    def add_related_memory(self, memory_id: UUID) -> None:
        """添加相关记忆"""
        self.related_memories.add(memory_id)
        self.updated_at = datetime.now(timezone.utc)
    
    def update_content(self, new_content: str, summary: Optional[str] = None) -> None:
        """更新内容"""
        self.content = new_content.strip()
        if summary:
            self.summary = summary.strip()
        self.updated_at = datetime.now(timezone.utc)
    
    def set_importance(self, importance: MemoryImportance) -> None:
        """设置重要性"""
        self.importance = importance
        self.updated_at = datetime.now(timezone.utc)
    
    def is_recent(self, hours: int = 24) -> bool:
        """判断是否为近期记忆"""
        now = datetime.now(timezone.utc)
        return (now - self.created_at).total_seconds() < hours * 3600
    
    def age_in_days(self) -> float:
        """获取记忆年龄（天数）"""
        now = datetime.now(timezone.utc)
        return (now - self.created_at).total_seconds() / 86400
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.dict()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Memory":
        """从字典创建记忆"""
        return cls(**data)
    
    class Config:
        # 允许使用枚举值
        use_enum_values = True
        # JSON编码器
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
            set: lambda v: list(v),
        }


class MemoryQuery(BaseModel):
    """记忆查询模型"""
    
    # 内容查询
    content: Optional[str] = Field(None, description="内容关键词")
    summary: Optional[str] = Field(None, description="摘要关键词")
    
    # 分类查询
    memory_types: Optional[List[MemoryType]] = Field(None, description="记忆类型列表")
    importance_levels: Optional[List[MemoryImportance]] = Field(None, description="重要性级别列表")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    categories: Optional[List[str]] = Field(None, description="分类列表")
    
    # 时间查询
    created_after: Optional[datetime] = Field(None, description="创建时间之后")
    created_before: Optional[datetime] = Field(None, description="创建时间之前")
    accessed_after: Optional[datetime] = Field(None, description="访问时间之后")
    
    # 上下文查询
    session_id: Optional[str] = Field(None, description="会话ID")
    conversation_id: Optional[str] = Field(None, description="对话ID")
    
    # 关联查询
    related_to: Optional[UUID] = Field(None, description="相关记忆ID")
    
    # 排序和限制
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", description="排序顺序")
    limit: int = Field(default=10, description="结果数量限制")
    offset: int = Field(default=0, description="结果偏移量")
    
    # 相似性查询
    similarity_threshold: Optional[float] = Field(None, description="相似性阈值")
    embedding: Optional[List[float]] = Field(None, description="查询向量")


class MemoryManager:
    """记忆管理器基类"""
    
    def __init__(self):
        self._memories: Dict[UUID, Memory] = {}
    
    async def create_memory(self, memory: Memory) -> Memory:
        """创建记忆"""
        raise NotImplementedError
    
    async def get_memory(self, memory_id: UUID) -> Optional[Memory]:
        """获取记忆"""
        raise NotImplementedError
    
    async def update_memory(self, memory: Memory) -> Memory:
        """更新记忆"""
        raise NotImplementedError
    
    async def delete_memory(self, memory_id: UUID) -> bool:
        """删除记忆"""
        raise NotImplementedError
    
    async def search_memories(self, query: MemoryQuery) -> List[Memory]:
        """搜索记忆"""
        raise NotImplementedError
    
    async def get_related_memories(self, memory_id: UUID, limit: int = 5) -> List[Memory]:
        """获取相关记忆"""
        raise NotImplementedError
    
    async def get_recent_memories(self, hours: int = 24, limit: int = 10) -> List[Memory]:
        """获取近期记忆"""
        raise NotImplementedError
    
    async def cleanup_old_memories(self, days: int = 30) -> int:
        """清理旧记忆"""
        raise NotImplementedError
