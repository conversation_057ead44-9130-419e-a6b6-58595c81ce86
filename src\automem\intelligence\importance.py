"""
重要性分析器

分析和评估记忆内容的重要性级别。
"""

import re
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Set, Any, Optional, Tuple
from collections import Counter

from ..config.settings import IntelligenceConfig
from ..utils.logging import LoggerMixin
from ..core.memory import Memory, MemoryImportance


class ImportanceAnalyzer(LoggerMixin):
    """重要性分析器"""
    
    def __init__(self, config: IntelligenceConfig):
        self.config = config
        
        # 重要性指标权重
        self.importance_weights = {
            "content_quality": 0.25,      # 内容质量
            "urgency": 0.20,              # 紧急程度
            "relevance": 0.20,            # 相关性
            "uniqueness": 0.15,           # 独特性
            "actionability": 0.10,        # 可操作性
            "context": 0.10,              # 上下文重要性
        }
        
        # 重要性关键词
        self.importance_keywords = self._initialize_importance_keywords()
        
        # 历史重要性数据
        self.importance_history: List[Tuple[str, float]] = []
    
    def _initialize_importance_keywords(self) -> Dict[str, Dict[str, float]]:
        """初始化重要性关键词"""
        return {
            "critical": {
                "keywords": [
                    "紧急", "关键", "重要", "核心", "必须", "立即", "马上",
                    "urgent", "critical", "important", "key", "must", "immediately", "asap"
                ],
                "weight": 1.0,
            },
            "high": {
                "keywords": [
                    "重点", "主要", "优先", "重大", "显著", "突出",
                    "priority", "major", "significant", "primary", "main", "prominent"
                ],
                "weight": 0.8,
            },
            "medium": {
                "keywords": [
                    "一般", "普通", "常规", "标准", "正常", "基本",
                    "normal", "standard", "regular", "basic", "common", "typical"
                ],
                "weight": 0.5,
            },
            "low": {
                "keywords": [
                    "次要", "辅助", "补充", "可选", "备用", "额外",
                    "secondary", "auxiliary", "optional", "additional", "extra", "minor"
                ],
                "weight": 0.3,
            },
            "minimal": {
                "keywords": [
                    "无关", "琐碎", "随便", "闲聊", "测试", "临时",
                    "irrelevant", "trivial", "casual", "chat", "test", "temporary"
                ],
                "weight": 0.1,
            },
        }
    
    async def analyze_importance(
        self, 
        memory: Memory, 
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[MemoryImportance, float, Dict[str, float]]:
        """分析记忆重要性"""
        
        # 计算各个维度的得分
        scores = {}
        
        # 内容质量得分
        scores["content_quality"] = await self._analyze_content_quality(memory)
        
        # 紧急程度得分
        scores["urgency"] = await self._analyze_urgency(memory, context)
        
        # 相关性得分
        scores["relevance"] = await self._analyze_relevance(memory, context)
        
        # 独特性得分
        scores["uniqueness"] = await self._analyze_uniqueness(memory)
        
        # 可操作性得分
        scores["actionability"] = await self._analyze_actionability(memory)
        
        # 上下文重要性得分
        scores["context"] = await self._analyze_context_importance(memory, context)
        
        # 计算加权总分
        total_score = sum(
            scores[dimension] * self.importance_weights[dimension]
            for dimension in scores
        )
        
        # 确定重要性级别
        importance_level = self._score_to_importance(total_score)
        
        # 记录历史数据
        self.importance_history.append((memory.content[:50], total_score))
        if len(self.importance_history) > 1000:
            self.importance_history = self.importance_history[-1000:]
        
        self.logger.debug(f"重要性分析: {importance_level.value} (得分: {total_score:.2f})")
        
        return importance_level, total_score, scores
    
    async def _analyze_content_quality(self, memory: Memory) -> float:
        """分析内容质量"""
        content = memory.content
        score = 0.0
        
        # 长度因子
        length_score = min(len(content) / 200.0, 1.0)  # 200字符为满分
        score += length_score * 0.3
        
        # 结构化程度
        structure_score = 0.0
        if re.search(r'[.!?。！？]', content):  # 有标点符号
            structure_score += 0.3
        if re.search(r'\n', content):  # 有换行
            structure_score += 0.2
        if re.search(r'[0-9]', content):  # 包含数字
            structure_score += 0.2
        if re.search(r'[A-Za-z]', content):  # 包含英文
            structure_score += 0.3
        
        score += min(structure_score, 1.0) * 0.3
        
        # 信息密度
        words = content.split()
        unique_words = set(words)
        if words:
            density = len(unique_words) / len(words)
            score += density * 0.4
        
        return min(score, 1.0)
    
    async def _analyze_urgency(self, memory: Memory, context: Optional[Dict[str, Any]]) -> float:
        """分析紧急程度"""
        content = memory.content.lower()
        score = 0.0
        
        # 紧急关键词检测
        urgent_patterns = [
            r"紧急|urgent|asap|立即|马上|immediately",
            r"deadline|截止|due|到期",
            r"问题|错误|故障|problem|error|issue|bug",
            r"重要|关键|critical|important|key",
        ]
        
        for pattern in urgent_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                score += 0.25
        
        # 时间相关的紧急性
        time_patterns = [
            r"今天|today|现在|now",
            r"明天|tomorrow|下周|next week",
            r"本周|this week|这周",
        ]
        
        for pattern in time_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                score += 0.15
        
        # 上下文紧急性
        if context:
            if context.get("urgent", False):
                score += 0.5
            if context.get("deadline", False):
                score += 0.3
        
        # 记忆类型影响紧急性
        if memory.memory_type.value == "procedure":
            score += 0.1  # 流程类记忆通常更紧急
        
        return min(score, 1.0)
    
    async def _analyze_relevance(self, memory: Memory, context: Optional[Dict[str, Any]]) -> float:
        """分析相关性"""
        score = 0.0
        
        # 基于标签的相关性
        if memory.tags:
            score += min(len(memory.tags) / 5.0, 0.3)
        
        # 基于分类的相关性
        if memory.categories:
            score += min(len(memory.categories) / 3.0, 0.2)
        
        # 上下文相关性
        if context:
            # 会话相关性
            if context.get("session_id") == memory.session_id:
                score += 0.3
            
            # 主题相关性
            context_topics = context.get("topics", [])
            if context_topics and memory.tags:
                common_topics = set(context_topics) & memory.tags
                if common_topics:
                    score += len(common_topics) / len(context_topics) * 0.2
        
        # 关联记忆数量
        if memory.related_memories:
            score += min(len(memory.related_memories) / 10.0, 0.3)
        
        return min(score, 1.0)
    
    async def _analyze_uniqueness(self, memory: Memory) -> float:
        """分析独特性"""
        content = memory.content.lower()
        score = 0.0
        
        # 特殊信息检测
        unique_patterns = [
            r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}",  # 邮箱
            r"http[s]?://[^\s]+",  # URL
            r"\b\d{3}[-.]?\d{3}[-.]?\d{4}\b",  # 电话
            r"\b\d{4}[-/]\d{1,2}[-/]\d{1,2}\b",  # 日期
            r"\$\d+(?:,\d{3})*(?:\.\d{2})?",  # 金额
        ]
        
        for pattern in unique_patterns:
            if re.search(pattern, content):
                score += 0.2
        
        # 专业术语检测
        technical_patterns = [
            r"API|SDK|HTTP|JSON|XML|SQL",
            r"算法|数据结构|algorithm|data structure",
            r"框架|framework|library|库",
        ]
        
        for pattern in technical_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                score += 0.15
        
        # 数字和统计信息
        if re.search(r'\d+%|\d+\.\d+', content):
            score += 0.2
        
        # 引用和链接
        if re.search(r'参考|引用|来源|reference|source', content, re.IGNORECASE):
            score += 0.1
        
        return min(score, 1.0)
    
    async def _analyze_actionability(self, memory: Memory) -> float:
        """分析可操作性"""
        content = memory.content.lower()
        score = 0.0
        
        # 行动词汇检测
        action_patterns = [
            r"需要|要|应该|必须|need|should|must|have to",
            r"做|执行|完成|实现|do|execute|complete|implement",
            r"计划|安排|准备|plan|schedule|prepare",
            r"联系|沟通|讨论|contact|communicate|discuss",
            r"检查|确认|验证|check|confirm|verify",
        ]
        
        for pattern in action_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                score += 0.2
        
        # 时间限制
        if re.search(r"deadline|截止|due|到期|before|之前", content, re.IGNORECASE):
            score += 0.3
        
        # 具体步骤
        if re.search(r"步骤|流程|方法|step|process|method", content, re.IGNORECASE):
            score += 0.2
        
        # 记忆类型影响可操作性
        if memory.memory_type.value == "procedure":
            score += 0.3
        
        return min(score, 1.0)
    
    async def _analyze_context_importance(
        self, 
        memory: Memory, 
        context: Optional[Dict[str, Any]]
    ) -> float:
        """分析上下文重要性"""
        score = 0.0
        
        if not context:
            return 0.5  # 默认中等重要性
        
        # 工作相关上下文
        if context.get("work_related", False):
            score += 0.4
        
        # 学习相关上下文
        if context.get("learning_related", False):
            score += 0.3
        
        # 项目相关上下文
        if context.get("project_related", False):
            score += 0.4
        
        # 会话长度影响重要性
        conversation_length = context.get("conversation_length", 0)
        if conversation_length > 10:
            score += 0.2
        elif conversation_length > 5:
            score += 0.1
        
        # 用户明确标记的重要性
        if context.get("user_marked_important", False):
            score += 0.5
        
        return min(score, 1.0)
    
    def _score_to_importance(self, score: float) -> MemoryImportance:
        """将得分转换为重要性级别"""
        if score >= 0.8:
            return MemoryImportance.CRITICAL
        elif score >= 0.6:
            return MemoryImportance.HIGH
        elif score >= 0.4:
            return MemoryImportance.MEDIUM
        elif score >= 0.2:
            return MemoryImportance.LOW
        else:
            return MemoryImportance.MINIMAL
    
    async def get_importance_distribution(self) -> Dict[str, float]:
        """获取重要性分布统计"""
        if not self.importance_history:
            return {}
        
        scores = [score for _, score in self.importance_history]
        
        distribution = {
            "critical": sum(1 for s in scores if s >= 0.8) / len(scores),
            "high": sum(1 for s in scores if 0.6 <= s < 0.8) / len(scores),
            "medium": sum(1 for s in scores if 0.4 <= s < 0.6) / len(scores),
            "low": sum(1 for s in scores if 0.2 <= s < 0.4) / len(scores),
            "minimal": sum(1 for s in scores if s < 0.2) / len(scores),
        }
        
        return distribution
    
    async def adjust_importance_weights(self, new_weights: Dict[str, float]) -> None:
        """调整重要性权重"""
        # 验证权重总和为1
        total_weight = sum(new_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            raise ValueError("权重总和必须为1.0")
        
        # 验证所有必需的维度都存在
        required_dimensions = set(self.importance_weights.keys())
        provided_dimensions = set(new_weights.keys())
        
        if required_dimensions != provided_dimensions:
            missing = required_dimensions - provided_dimensions
            extra = provided_dimensions - required_dimensions
            raise ValueError(f"缺少维度: {missing}, 多余维度: {extra}")
        
        self.importance_weights.update(new_weights)
        self.logger.info("重要性权重已更新")
    
    async def get_analysis_stats(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        if not self.importance_history:
            return {"message": "暂无分析历史"}
        
        scores = [score for _, score in self.importance_history]
        
        return {
            "total_analyzed": len(self.importance_history),
            "average_score": sum(scores) / len(scores),
            "max_score": max(scores),
            "min_score": min(scores),
            "distribution": await self.get_importance_distribution(),
            "current_weights": dict(self.importance_weights),
        }
