"""
AutoMem MCP工具接口

提供记忆管理的MCP工具。
"""

import asyncio
from typing import Dict, List, Any, Optional
from uuid import UUID
from datetime import datetime, timezone

from ..utils.logging import LoggerMixin
from ..core.memory import Memory, MemoryQuery, MemoryType, MemoryImportance
from ..intelligence.autonomous import AutonomousMemorySystem


class AutoMemTools(LoggerMixin):
    """AutoMem MCP工具"""
    
    def __init__(self, autonomous_system: AutonomousMemorySystem):
        self.autonomous_system = autonomous_system
    
    async def store_memory(
        self,
        content: str,
        memory_type: str = "conversation",
        importance: str = "medium",
        tags: Optional[List[str]] = None,
        categories: Optional[List[str]] = None,
        session_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        source: Optional[str] = None,
        auto_process: bool = True
    ) -> Dict[str, Any]:
        """存储记忆到AutoMem系统
        
        Args:
            content: 记忆内容
            memory_type: 记忆类型 (conversation, fact, procedure, episodic, semantic)
            importance: 重要性级别 (critical, high, medium, low, minimal)
            tags: 标签列表
            categories: 分类列表
            session_id: 会话ID
            conversation_id: 对话ID
            source: 记忆来源
            auto_process: 是否启用自动处理
        
        Returns:
            存储结果字典
        """
        try:
            # 构建上下文
            context = {
                "session_id": session_id,
                "conversation_id": conversation_id,
                "source": source,
                "manual_tags": tags or [],
                "manual_categories": categories or [],
                "manual_memory_type": memory_type,
                "manual_importance": importance,
            }
            
            if auto_process:
                # 使用自主系统处理
                success, memory, actions = await self.autonomous_system.process_new_memory(
                    content, context
                )
                
                if success and memory:
                    return {
                        "success": True,
                        "memory_id": str(memory.id),
                        "content": memory.content,
                        "memory_type": memory.memory_type.value,
                        "importance": memory.importance.value,
                        "tags": list(memory.tags),
                        "categories": list(memory.categories),
                        "auto_actions": [
                            {
                                "action_type": action.action_type,
                                "confidence": action.confidence,
                                "reasoning": action.reasoning,
                            }
                            for action in actions
                        ],
                        "message": "记忆已成功存储并自动处理",
                    }
                else:
                    return {
                        "success": False,
                        "error": "自动处理决定不存储此记忆",
                        "actions": [
                            {
                                "action_type": action.action_type,
                                "reasoning": action.reasoning,
                            }
                            for action in actions
                        ],
                    }
            else:
                # 手动存储
                try:
                    memory_type_enum = MemoryType(memory_type)
                except ValueError:
                    memory_type_enum = MemoryType.CONVERSATION
                
                try:
                    importance_enum = MemoryImportance(importance)
                except ValueError:
                    importance_enum = MemoryImportance.MEDIUM
                
                memory = Memory(
                    content=content,
                    memory_type=memory_type_enum,
                    importance=importance_enum,
                    tags=set(tags or []),
                    categories=set(categories or []),
                    session_id=session_id,
                    conversation_id=conversation_id,
                    source=source,
                    context=context,
                )
                
                stored_memory = await self.autonomous_system.storage.store_memory(memory)
                await self.autonomous_system.vector_db.add_memory(stored_memory)
                
                return {
                    "success": True,
                    "memory_id": str(stored_memory.id),
                    "content": stored_memory.content,
                    "memory_type": stored_memory.memory_type.value,
                    "importance": stored_memory.importance.value,
                    "tags": list(stored_memory.tags),
                    "categories": list(stored_memory.categories),
                    "message": "记忆已手动存储",
                }
                
        except Exception as e:
            self.logger.error(f"存储记忆失败: {e}")
            return {
                "success": False,
                "error": str(e),
            }
    
    async def search_memories(
        self,
        query: str,
        limit: int = 10,
        memory_types: Optional[List[str]] = None,
        importance_levels: Optional[List[str]] = None,
        tags: Optional[List[str]] = None,
        categories: Optional[List[str]] = None,
        session_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        similarity_threshold: float = 0.6,
        include_context: bool = True
    ) -> Dict[str, Any]:
        """搜索相关记忆
        
        Args:
            query: 搜索查询
            limit: 结果数量限制
            memory_types: 记忆类型过滤
            importance_levels: 重要性级别过滤
            tags: 标签过滤
            categories: 分类过滤
            session_id: 会话ID过滤
            conversation_id: 对话ID过滤
            similarity_threshold: 相似度阈值
            include_context: 是否包含上下文信息
        
        Returns:
            搜索结果字典
        """
        try:
            # 构建过滤器
            filters = {}
            
            if memory_types:
                try:
                    filters["memory_types"] = [MemoryType(mt) for mt in memory_types]
                except ValueError as e:
                    return {"success": False, "error": f"无效的记忆类型: {e}"}
            
            if importance_levels:
                try:
                    filters["importance_levels"] = [MemoryImportance(il) for il in importance_levels]
                except ValueError as e:
                    return {"success": False, "error": f"无效的重要性级别: {e}"}
            
            if tags:
                filters["tags"] = tags
            
            if categories:
                filters["categories"] = categories
            
            if session_id:
                filters["session_id"] = session_id
            
            if conversation_id:
                filters["conversation_id"] = conversation_id
            
            # 执行搜索
            from ..retrieval.semantic_search import SemanticSearchEngine
            search_engine = SemanticSearchEngine(
                self.autonomous_system.retrieval_config,
                self.autonomous_system.vector_db,
                self.autonomous_system.storage
            )
            
            search_results = await search_engine.search(
                query=query,
                filters=filters,
                limit=limit,
                similarity_threshold=similarity_threshold,
                include_context=include_context
            )
            
            # 格式化结果
            results = []
            for result in search_results:
                memory = result.memory
                result_dict = {
                    "memory_id": str(memory.id),
                    "content": memory.content,
                    "summary": memory.summary,
                    "memory_type": memory.memory_type.value,
                    "importance": memory.importance.value,
                    "tags": list(memory.tags),
                    "categories": list(memory.categories),
                    "created_at": memory.created_at.isoformat(),
                    "updated_at": memory.updated_at.isoformat(),
                    "access_count": memory.access_count,
                    "similarity_score": result.similarity_score,
                    "relevance_score": result.relevance_score,
                    "combined_score": result.combined_score,
                    "match_reasons": result.match_reasons,
                }
                
                if include_context:
                    result_dict.update({
                        "session_id": memory.session_id,
                        "conversation_id": memory.conversation_id,
                        "source": memory.source,
                        "context": memory.context,
                    })
                
                results.append(result_dict)
            
            return {
                "success": True,
                "query": query,
                "results": results,
                "total_found": len(results),
                "limit": limit,
                "similarity_threshold": similarity_threshold,
            }
            
        except Exception as e:
            self.logger.error(f"搜索记忆失败: {e}")
            return {
                "success": False,
                "error": str(e),
            }
    
    async def get_context(
        self,
        topic: Optional[str] = None,
        session_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        time_window_hours: int = 24,
        limit: int = 5,
        include_related: bool = True
    ) -> Dict[str, Any]:
        """获取当前对话的相关上下文
        
        Args:
            topic: 主题关键词
            session_id: 会话ID
            conversation_id: 对话ID
            time_window_hours: 时间窗口（小时）
            limit: 结果数量限制
            include_related: 是否包含相关记忆
        
        Returns:
            上下文信息字典
        """
        try:
            from ..retrieval.context_retriever import ContextRetriever, ConversationContext
            
            # 构建上下文检索器
            context_retriever = ContextRetriever(
                self.autonomous_system.retrieval_config,
                self.autonomous_system.storage,
                None  # 暂时不传入semantic_search
            )
            
            # 构建对话上下文
            context = ConversationContext(
                session_id=session_id,
                conversation_id=conversation_id,
                topics=[topic] if topic else [],
            )
            
            # 检索上下文记忆
            query = topic or "相关上下文"
            contextual_memories = await context_retriever.retrieve_context(
                query=query,
                context=context,
                limit=limit,
                include_related=include_related,
                time_window_hours=time_window_hours
            )
            
            # 格式化结果
            context_results = []
            for contextual_memory in contextual_memories:
                memory = contextual_memory.memory
                context_results.append({
                    "memory_id": str(memory.id),
                    "content": memory.content,
                    "summary": memory.summary,
                    "memory_type": memory.memory_type.value,
                    "importance": memory.importance.value,
                    "tags": list(memory.tags),
                    "created_at": memory.created_at.isoformat(),
                    "context_score": contextual_memory.context_score,
                    "context_reasons": contextual_memory.context_reasons,
                    "relationship_type": contextual_memory.relationship_type,
                })
            
            return {
                "success": True,
                "topic": topic,
                "session_id": session_id,
                "conversation_id": conversation_id,
                "time_window_hours": time_window_hours,
                "context_memories": context_results,
                "total_found": len(context_results),
            }
            
        except Exception as e:
            self.logger.error(f"获取上下文失败: {e}")
            return {
                "success": False,
                "error": str(e),
            }

    async def manage_tags(
        self,
        action: str,
        memory_id: Optional[str] = None,
        tag: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """管理记忆标签

        Args:
            action: 操作类型 (add, remove, list, suggest)
            memory_id: 记忆ID
            tag: 单个标签
            tags: 标签列表

        Returns:
            操作结果字典
        """
        try:
            if action == "add" and memory_id and (tag or tags):
                memory = await self.autonomous_system.storage.get_memory(UUID(memory_id))
                if not memory:
                    return {"success": False, "error": "记忆不存在"}

                tags_to_add = [tag] if tag else tags
                for t in tags_to_add:
                    memory.add_tag(t)

                await self.autonomous_system.storage.update_memory(memory)
                await self.autonomous_system.vector_db.update_memory(memory)

                return {
                    "success": True,
                    "action": "add",
                    "memory_id": memory_id,
                    "added_tags": tags_to_add,
                    "current_tags": list(memory.tags),
                    "message": f"已添加 {len(tags_to_add)} 个标签",
                }

            elif action == "remove" and memory_id and (tag or tags):
                memory = await self.autonomous_system.storage.get_memory(UUID(memory_id))
                if not memory:
                    return {"success": False, "error": "记忆不存在"}

                tags_to_remove = [tag] if tag else tags
                for t in tags_to_remove:
                    memory.remove_tag(t)

                await self.autonomous_system.storage.update_memory(memory)
                await self.autonomous_system.vector_db.update_memory(memory)

                return {
                    "success": True,
                    "action": "remove",
                    "memory_id": memory_id,
                    "removed_tags": tags_to_remove,
                    "current_tags": list(memory.tags),
                    "message": f"已移除 {len(tags_to_remove)} 个标签",
                }

            elif action == "list":
                if memory_id:
                    # 获取特定记忆的标签
                    memory = await self.autonomous_system.storage.get_memory(UUID(memory_id))
                    if not memory:
                        return {"success": False, "error": "记忆不存在"}

                    return {
                        "success": True,
                        "action": "list",
                        "memory_id": memory_id,
                        "tags": list(memory.tags),
                        "tag_count": len(memory.tags),
                    }
                else:
                    # 获取热门标签
                    popular_tags = await self.autonomous_system.tagger.get_popular_tags(limit=20)

                    return {
                        "success": True,
                        "action": "list",
                        "popular_tags": [{"tag": tag, "usage_count": count} for tag, count in popular_tags],
                        "total_unique_tags": len(popular_tags),
                    }

            elif action == "suggest" and memory_id:
                memory = await self.autonomous_system.storage.get_memory(UUID(memory_id))
                if not memory:
                    return {"success": False, "error": "记忆不存在"}

                # 生成标签建议
                suggested_tags = await self.autonomous_system.tagger.generate_tags(memory)

                # 获取相关标签建议
                related_tags = await self.autonomous_system.tagger.suggest_related_tags(
                    list(memory.tags), limit=5
                )

                return {
                    "success": True,
                    "action": "suggest",
                    "memory_id": memory_id,
                    "current_tags": list(memory.tags),
                    "suggested_tags": suggested_tags,
                    "related_tags": related_tags,
                    "message": f"为记忆建议了 {len(suggested_tags)} 个新标签",
                }

            else:
                return {
                    "success": False,
                    "error": f"不支持的操作或缺少必需参数: {action}",
                }

        except Exception as e:
            self.logger.error(f"标签管理失败: {e}")
            return {
                "success": False,
                "error": str(e),
            }

    async def cleanup_memories(
        self,
        days: int = 30,
        dry_run: bool = True,
        min_importance: str = "minimal",
        max_access_count: int = 2
    ) -> Dict[str, Any]:
        """清理过时记忆

        Args:
            days: 清理多少天前的记忆
            dry_run: 是否为预览模式
            min_importance: 最低重要性级别
            max_access_count: 最大访问次数

        Returns:
            清理结果字典
        """
        try:
            if dry_run:
                # 预览模式 - 获取归档候选
                candidates = await self.autonomous_system.priority_manager.get_archive_candidates(
                    limit=100,
                    min_age_days=days
                )

                # 过滤条件
                try:
                    min_importance_enum = MemoryImportance(min_importance)
                except ValueError:
                    min_importance_enum = MemoryImportance.MINIMAL

                filtered_candidates = [
                    memory for memory in candidates
                    if (memory.importance.value <= min_importance_enum.value and
                        memory.access_count <= max_access_count)
                ]

                return {
                    "success": True,
                    "dry_run": True,
                    "days": days,
                    "total_candidates": len(candidates),
                    "filtered_candidates": len(filtered_candidates),
                    "candidates_preview": [
                        {
                            "memory_id": str(memory.id),
                            "content": memory.content[:100] + "..." if len(memory.content) > 100 else memory.content,
                            "age_days": memory.age_in_days(),
                            "access_count": memory.access_count,
                            "importance": memory.importance.value,
                            "created_at": memory.created_at.isoformat(),
                        }
                        for memory in filtered_candidates[:10]  # 只显示前10个
                    ],
                    "message": f"预览: 找到 {len(filtered_candidates)} 个可清理的记忆",
                }
            else:
                # 实际清理
                cleaned_count = await self.autonomous_system.storage.cleanup_old_memories(days)

                return {
                    "success": True,
                    "dry_run": False,
                    "days": days,
                    "cleaned_count": cleaned_count,
                    "message": f"已清理 {cleaned_count} 个过时记忆",
                }

        except Exception as e:
            self.logger.error(f"清理记忆失败: {e}")
            return {
                "success": False,
                "error": str(e),
            }

    async def get_memory_stats(self) -> Dict[str, Any]:
        """获取记忆统计信息

        Returns:
            统计信息字典
        """
        try:
            # 基础统计
            basic_stats = await self.autonomous_system.storage.get_memory_stats()

            # 优先级统计
            priority_stats = await self.autonomous_system.priority_manager.get_priority_statistics()

            # 生命周期统计
            lifecycle_stats = await self.autonomous_system.lifecycle_manager.get_lifecycle_statistics()

            # 标签统计
            tag_stats = await self.autonomous_system.tagger.get_tag_stats()

            # 分类统计
            classification_stats = await self.autonomous_system.classifier.get_classification_stats()

            # 自主系统洞察
            autonomous_insights = await self.autonomous_system.get_autonomous_insights()

            return {
                "success": True,
                "basic_stats": basic_stats,
                "priority_stats": priority_stats,
                "lifecycle_stats": lifecycle_stats,
                "tag_stats": tag_stats,
                "classification_stats": classification_stats,
                "autonomous_insights": autonomous_insights,
                "generated_at": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {
                "success": False,
                "error": str(e),
            }
