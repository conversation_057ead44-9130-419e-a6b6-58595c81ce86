@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: AutoMem Windows 快速开始脚本

echo.
echo     ___        __       __  __                 
echo    /   ^| __  __/ /_____/  ^|/  /__  ____ ___    
echo   / /^| ^|/ / / / __/ __ \  /^|_/ / _ \/ __ `__ \   
echo  / ___ / /_/ / /_/ /_/ / /  / /  __/ / / / / /   
echo /_/  ^|_\__,_/\__/\____/_/  /_/\___/_/ /_/ /_/    
echo.                                                 
echo     AutoMem 智能记忆管理系统
echo     Windows 快速开始 - 5分钟上手指南
echo.

echo 🚀 欢迎使用 AutoMem！
echo 这个脚本将帮助您快速安装和启动 AutoMem 智能记忆管理系统。
echo.

:: 检查系统
echo 📋 检查系统要求...

:: 检查 Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Python 未安装，请先安装 Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo ✅ Python 版本: %python_version%

:: 检查 pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  pip 未安装
    pause
    exit /b 1
)
echo ✅ pip 已安装

:: 选择安装方式
echo.
echo 🔧 选择安装方式:
echo 1) 自动安装 (推荐)
echo 2) Docker 安装
echo 3) 手动安装
echo.

set /p choice="请选择 (1-3): "

if "%choice%"=="1" (
    echo 🎯 执行自动安装...
    if exist "scripts\install.ps1" (
        powershell -ExecutionPolicy Bypass -File "scripts\install.ps1"
    ) else (
        echo 下载并执行安装脚本...
        powershell -Command "iwr -useb https://raw.githubusercontent.com/your-org/automem/main/scripts/install.ps1 | iex"
    )
) else if "%choice%"=="2" (
    echo 🐳 执行 Docker 安装...
    docker --version >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Docker 未安装，请先安装 Docker Desktop
        echo 下载地址: https://docs.docker.com/desktop/windows/
        pause
        exit /b 1
    )
    
    if exist "docker-compose.yml" (
        docker-compose up -d
    ) else (
        echo 克隆仓库并启动...
        git clone https://github.com/your-org/automem.git
        cd automem
        docker-compose up -d
    )
) else if "%choice%"=="3" (
    echo 🔨 执行手动安装...
    
    :: 创建虚拟环境
    echo 创建 Python 虚拟环境...
    python -m venv venv
    call venv\Scripts\activate.bat
    
    :: 安装依赖
    echo 安装依赖包...
    pip install -r requirements.txt
    
    :: 初始化配置
    echo 初始化配置...
    if exist "config.example.yaml" (
        copy "config.example.yaml" "config.yaml"
    )
    
    :: 创建数据目录
    if not exist "data" mkdir data
    
    echo ✅ 手动安装完成
) else (
    echo 无效选择
    pause
    exit /b 1
)

echo.
echo 🎉 安装完成！
echo.

:: 启动服务
echo 🚀 启动 AutoMem 服务...

if "%choice%"=="2" (
    echo Docker 服务已在后台运行
    echo 访问地址: http://localhost:8000
) else (
    echo 启动 MCP 服务器...
    if exist "automem.bat" (
        start /min automem.bat serve --stdio
    ) else if exist "venv\Scripts\python.exe" (
        start /min venv\Scripts\python.exe -m automem.cli serve --stdio
    ) else (
        start /min python -m automem.cli serve --stdio
    )
    
    timeout /t 3 /nobreak >nul
    echo ✅ 服务已启动
)

echo.
echo 🎯 快速配置 Claude Desktop:
echo.
echo 1. 打开 Claude Desktop 配置文件:
echo    Windows: %%APPDATA%%\Claude\claude_desktop_config.json
echo.
echo 2. 添加以下配置:
echo {
echo   "mcpServers": {
echo     "automem": {
echo       "command": "C:\\path\\to\\automem\\automem.bat",
echo       "args": ["serve", "--stdio"]
echo     }
echo   }
echo }
echo.
echo 3. 重启 Claude Desktop
echo.
echo 4. 在 Claude 中测试:
echo    "请帮我存储一个记忆：今天学习了 AutoMem 的使用方法"
echo.

echo 📚 更多资源:
echo • 完整文档: docs\README.md
echo • 使用指南: docs\usage.md
echo • 配置指南: docs\configuration.md
echo • 问题反馈: https://github.com/your-org/automem/issues
echo.

echo ✨ 开始使用 AutoMem，让 AI 拥有持久记忆！
echo.
pause
