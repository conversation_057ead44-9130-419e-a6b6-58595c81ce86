"""
pytest 配置文件

定义测试夹具和全局配置。
"""

import asyncio
import tempfile
import shutil
from pathlib import Path
from typing import AsyncGenerator, Generator
import pytest
import pytest_asyncio

from src.automem.config import AutoMemConfig
from src.automem.core.storage import SQLiteMemoryManager
from src.automem.core.vector_db import VectorDatabase
from src.automem.core.memory import Memory, MemoryType, MemoryImportance
from src.automem.intelligence.autonomous import AutonomousMemorySystem


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """创建临时目录"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def test_config(temp_dir: Path) -> AutoMemConfig:
    """创建测试配置"""
    config = AutoMemConfig()
    config.storage.data_dir = temp_dir
    config.storage.max_memories = 1000
    config.logging.level = "DEBUG"
    config.logging.enable_console = False
    config.logging.enable_file = False
    return config


@pytest_asyncio.fixture
async def memory_manager(test_config: AutoMemConfig) -> AsyncGenerator[SQLiteMemoryManager, None]:
    """创建内存管理器"""
    manager = SQLiteMemoryManager(test_config.storage)
    await manager.initialize()
    yield manager
    await manager.close()


@pytest_asyncio.fixture
async def vector_db(test_config: AutoMemConfig) -> AsyncGenerator[VectorDatabase, None]:
    """创建向量数据库"""
    db = VectorDatabase(test_config.storage, test_config.intelligence)
    await db.initialize()
    yield db
    await db.close()


@pytest_asyncio.fixture
async def autonomous_system(
    test_config: AutoMemConfig,
    memory_manager: SQLiteMemoryManager,
    vector_db: VectorDatabase
) -> AsyncGenerator[AutonomousMemorySystem, None]:
    """创建自主记忆系统"""
    system = AutonomousMemorySystem(
        test_config.intelligence,
        test_config.retrieval,
        test_config.storage,
        memory_manager.storage,
        vector_db
    )
    yield system


@pytest.fixture
def sample_memory() -> Memory:
    """创建示例记忆"""
    return Memory(
        content="这是一个测试记忆，用于验证系统功能。",
        memory_type=MemoryType.CONVERSATION,
        importance=MemoryImportance.MEDIUM,
        tags={"测试", "示例"},
        categories={"技术", "开发"},
        session_id="test_session",
        conversation_id="test_conversation",
        source="pytest"
    )


@pytest.fixture
def sample_memories() -> list[Memory]:
    """创建多个示例记忆"""
    memories = []
    
    # 技术相关记忆
    memories.append(Memory(
        content="Python是一种高级编程语言，具有简洁的语法和强大的功能。",
        memory_type=MemoryType.FACT,
        importance=MemoryImportance.HIGH,
        tags={"Python", "编程", "语言"},
        categories={"技术", "编程"},
        source="test_data"
    ))
    
    # 对话记忆
    memories.append(Memory(
        content="用户询问如何使用AutoMem系统存储和检索记忆。",
        memory_type=MemoryType.CONVERSATION,
        importance=MemoryImportance.MEDIUM,
        tags={"用户", "询问", "使用"},
        categories={"对话", "帮助"},
        source="test_data"
    ))
    
    # 程序性记忆
    memories.append(Memory(
        content="要启动AutoMem服务器，运行命令: automem serve --config config.yaml",
        memory_type=MemoryType.PROCEDURE,
        importance=MemoryImportance.HIGH,
        tags={"启动", "命令", "配置"},
        categories={"操作", "指南"},
        source="test_data"
    ))
    
    # 情节性记忆
    memories.append(Memory(
        content="2024年1月15日，团队讨论了AutoMem的架构设计和实现方案。",
        memory_type=MemoryType.EPISODIC,
        importance=MemoryImportance.MEDIUM,
        tags={"团队", "讨论", "架构"},
        categories={"会议", "设计"},
        source="test_data"
    ))
    
    # 语义记忆
    memories.append(Memory(
        content="MCP (Model Context Protocol) 是一种用于AI模型上下文管理的协议。",
        memory_type=MemoryType.SEMANTIC,
        importance=MemoryImportance.HIGH,
        tags={"MCP", "协议", "AI"},
        categories={"概念", "技术"},
        source="test_data"
    ))
    
    return memories


@pytest.fixture
def mock_embedding_model():
    """模拟嵌入模型"""
    class MockEmbeddingModel:
        def encode(self, texts, **kwargs):
            import numpy as np
            # 返回固定维度的随机向量
            if isinstance(texts, str):
                texts = [texts]
            return np.random.rand(len(texts), 384).astype(np.float32)
    
    return MockEmbeddingModel()


# 测试标记
pytest_plugins = ["pytest_asyncio"]

# 测试配置
def pytest_configure(config):
    """pytest 配置"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "e2e: 端到端测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "requires_model: 需要ML模型的测试"
    )


# 测试收集配置
def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    for item in items:
        # 为异步测试添加标记
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)
        
        # 为需要模型的测试添加标记
        if "embedding" in item.name or "classification" in item.name:
            item.add_marker(pytest.mark.requires_model)
