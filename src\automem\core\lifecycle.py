"""
记忆生命周期管理器

管理记忆从创建到归档的完整生命周期。
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Set, Any, Optional, Tuple
from enum import Enum
from uuid import UUID
from collections import defaultdict

from ..config.settings import StorageConfig
from ..utils.logging import LoggerMixin
from .memory import Memory, MemoryImportance, MemoryType
from .storage import StorageEngine
from .priority import MemoryPriorityManager, MemoryPriority


class LifecycleStage(str, Enum):
    """生命周期阶段"""
    CREATED = "created"         # 新创建
    ACTIVE = "active"          # 活跃使用
    STABLE = "stable"          # 稳定存储
    AGING = "aging"            # 老化中
    ARCHIVED = "archived"      # 已归档
    DEPRECATED = "deprecated"   # 已废弃


class LifecycleEvent:
    """生命周期事件"""
    
    def __init__(
        self,
        memory_id: UUID,
        stage: LifecycleStage,
        timestamp: datetime,
        trigger: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.memory_id = memory_id
        self.stage = stage
        self.timestamp = timestamp
        self.trigger = trigger
        self.metadata = metadata or {}


class MemoryLifecycleManager(LoggerMixin):
    """记忆生命周期管理器"""
    
    def __init__(
        self, 
        config: StorageConfig, 
        storage: StorageEngine,
        priority_manager: MemoryPriorityManager
    ):
        self.config = config
        self.storage = storage
        self.priority_manager = priority_manager
        
        # 生命周期配置
        self.lifecycle_config = {
            "active_threshold_hours": 24,      # 活跃阶段阈值（小时）
            "stable_threshold_days": 7,        # 稳定阶段阈值（天）
            "aging_threshold_days": 30,        # 老化阶段阈值（天）
            "archive_threshold_days": 90,      # 归档阶段阈值（天）
            "deprecate_threshold_days": 365,   # 废弃阶段阈值（天）
            "min_access_for_active": 3,        # 保持活跃的最小访问次数
            "min_importance_for_stable": MemoryImportance.MEDIUM,  # 保持稳定的最小重要性
        }
        
        # 生命周期事件历史
        self.lifecycle_events: List[LifecycleEvent] = []
        
        # 统计信息
        self.lifecycle_stats = {
            "stage_transitions": defaultdict(int),
            "current_distribution": defaultdict(int),
            "total_managed": 0,
        }
    
    async def determine_lifecycle_stage(self, memory: Memory) -> LifecycleStage:
        """确定记忆的生命周期阶段"""
        
        age_hours = (datetime.now(timezone.utc) - memory.created_at).total_seconds() / 3600
        age_days = age_hours / 24
        
        # 检查是否已被明确标记
        if memory.metadata.get("archived", False):
            return LifecycleStage.ARCHIVED
        
        if memory.metadata.get("deprecated", False):
            return LifecycleStage.DEPRECATED
        
        # 基于年龄和使用情况判断阶段
        if age_hours <= self.lifecycle_config["active_threshold_hours"]:
            # 新创建的记忆
            if memory.access_count >= self.lifecycle_config["min_access_for_active"]:
                return LifecycleStage.ACTIVE
            else:
                return LifecycleStage.CREATED
        
        elif age_days <= self.lifecycle_config["stable_threshold_days"]:
            # 一周内的记忆
            if (memory.access_count >= self.lifecycle_config["min_access_for_active"] or
                memory.importance >= self.lifecycle_config["min_importance_for_stable"]):
                return LifecycleStage.ACTIVE
            else:
                return LifecycleStage.STABLE
        
        elif age_days <= self.lifecycle_config["aging_threshold_days"]:
            # 一个月内的记忆
            if memory.access_count > 5 or memory.importance == MemoryImportance.CRITICAL:
                return LifecycleStage.ACTIVE
            elif memory.importance >= MemoryImportance.MEDIUM:
                return LifecycleStage.STABLE
            else:
                return LifecycleStage.AGING
        
        elif age_days <= self.lifecycle_config["archive_threshold_days"]:
            # 三个月内的记忆
            if memory.access_count > 10 or memory.importance == MemoryImportance.CRITICAL:
                return LifecycleStage.STABLE
            else:
                return LifecycleStage.AGING
        
        elif age_days <= self.lifecycle_config["deprecate_threshold_days"]:
            # 一年内的记忆
            if (memory.access_count > 15 or 
                memory.importance in [MemoryImportance.CRITICAL, MemoryImportance.HIGH]):
                return LifecycleStage.STABLE
            else:
                return LifecycleStage.ARCHIVED
        
        else:
            # 超过一年的记忆
            if (memory.access_count > 20 or 
                memory.importance == MemoryImportance.CRITICAL):
                return LifecycleStage.STABLE
            else:
                return LifecycleStage.DEPRECATED
    
    async def update_memory_lifecycle(self, memory: Memory, trigger: str = "auto") -> LifecycleStage:
        """更新记忆的生命周期阶段"""
        
        # 获取当前阶段
        current_stage_str = memory.metadata.get("lifecycle_stage")
        current_stage = LifecycleStage(current_stage_str) if current_stage_str else None
        
        # 确定新阶段
        new_stage = await self.determine_lifecycle_stage(memory)
        
        # 如果阶段发生变化
        if current_stage != new_stage:
            # 更新记忆元数据
            memory.metadata["lifecycle_stage"] = new_stage.value
            memory.metadata["lifecycle_updated_at"] = datetime.now(timezone.utc).isoformat()
            memory.metadata["lifecycle_trigger"] = trigger
            
            # 记录生命周期事件
            event = LifecycleEvent(
                memory_id=memory.id,
                stage=new_stage,
                timestamp=datetime.now(timezone.utc),
                trigger=trigger,
                metadata={
                    "previous_stage": current_stage.value if current_stage else None,
                    "age_days": memory.age_in_days(),
                    "access_count": memory.access_count,
                    "importance": memory.importance.value,
                }
            )
            
            self.lifecycle_events.append(event)
            
            # 更新统计
            transition_key = f"{current_stage.value if current_stage else 'none'} -> {new_stage.value}"
            self.lifecycle_stats["stage_transitions"][transition_key] += 1
            
            # 保存记忆
            await self.storage.update_memory(memory)
            
            self.logger.debug(f"记忆 {memory.id} 生命周期: {current_stage} -> {new_stage}")
        
        # 更新当前分布统计
        self.lifecycle_stats["current_distribution"][new_stage.value] += 1
        self.lifecycle_stats["total_managed"] += 1
        
        return new_stage
    
    async def batch_update_lifecycles(
        self,
        limit: int = 100,
        stage_filter: Optional[List[LifecycleStage]] = None
    ) -> Dict[str, Any]:
        """批量更新记忆生命周期"""
        
        # 获取记忆
        from .memory import MemoryQuery
        query = MemoryQuery(limit=limit)
        memories = await self.storage.search_memories(query)
        
        # 过滤阶段
        if stage_filter:
            filtered_memories = []
            for memory in memories:
                current_stage_str = memory.metadata.get("lifecycle_stage")
                if current_stage_str:
                    current_stage = LifecycleStage(current_stage_str)
                    if current_stage in stage_filter:
                        filtered_memories.append(memory)
                else:
                    # 未设置阶段的记忆也包含在内
                    filtered_memories.append(memory)
            memories = filtered_memories
        
        # 批量更新
        updated_count = 0
        stage_changes = defaultdict(int)
        
        for memory in memories:
            old_stage_str = memory.metadata.get("lifecycle_stage")
            new_stage = await self.update_memory_lifecycle(memory, "batch_update")
            
            if old_stage_str != new_stage.value:
                updated_count += 1
                stage_changes[f"{old_stage_str or 'none'} -> {new_stage.value}"] += 1
        
        self.logger.info(f"批量更新了 {updated_count} 个记忆的生命周期")
        
        return {
            "total_processed": len(memories),
            "updated_count": updated_count,
            "stage_changes": dict(stage_changes),
        }
    
    async def get_memories_by_stage(
        self,
        stage: LifecycleStage,
        limit: int = 50
    ) -> List[Memory]:
        """获取指定阶段的记忆"""
        
        # 这里需要通过元数据查询，简化实现
        from .memory import MemoryQuery
        query = MemoryQuery(limit=limit * 2)  # 获取更多候选
        all_memories = await self.storage.search_memories(query)
        
        # 过滤指定阶段的记忆
        stage_memories = []
        for memory in all_memories:
            memory_stage_str = memory.metadata.get("lifecycle_stage")
            if memory_stage_str == stage.value:
                stage_memories.append(memory)
                if len(stage_memories) >= limit:
                    break
        
        return stage_memories
    
    async def promote_to_active(self, memory_id: UUID, reason: str = "") -> bool:
        """将记忆提升为活跃状态"""
        memory = await self.storage.get_memory(memory_id)
        if not memory:
            return False
        
        # 增加访问次数
        memory.update_access()
        
        # 强制设置为活跃阶段
        memory.metadata["lifecycle_stage"] = LifecycleStage.ACTIVE.value
        memory.metadata["lifecycle_updated_at"] = datetime.now(timezone.utc).isoformat()
        memory.metadata["lifecycle_trigger"] = f"manual_promotion: {reason}"
        
        # 记录事件
        event = LifecycleEvent(
            memory_id=memory.id,
            stage=LifecycleStage.ACTIVE,
            timestamp=datetime.now(timezone.utc),
            trigger=f"manual_promotion: {reason}",
            metadata={"promoted_by": "user"}
        )
        
        self.lifecycle_events.append(event)
        
        await self.storage.update_memory(memory)
        
        self.logger.info(f"记忆 {memory_id} 已提升为活跃状态: {reason}")
        return True
    
    async def archive_memory(self, memory_id: UUID, reason: str = "") -> bool:
        """归档记忆"""
        memory = await self.storage.get_memory(memory_id)
        if not memory:
            return False
        
        # 设置归档标记
        memory.metadata["archived"] = True
        memory.metadata["archived_at"] = datetime.now(timezone.utc).isoformat()
        memory.metadata["archive_reason"] = reason
        memory.metadata["lifecycle_stage"] = LifecycleStage.ARCHIVED.value
        
        # 记录事件
        event = LifecycleEvent(
            memory_id=memory.id,
            stage=LifecycleStage.ARCHIVED,
            timestamp=datetime.now(timezone.utc),
            trigger=f"manual_archive: {reason}",
            metadata={"archived_by": "user"}
        )
        
        self.lifecycle_events.append(event)
        
        await self.storage.update_memory(memory)
        
        self.logger.info(f"记忆 {memory_id} 已归档: {reason}")
        return True
    
    async def auto_lifecycle_maintenance(self) -> Dict[str, Any]:
        """自动生命周期维护"""
        
        maintenance_results = {
            "processed_memories": 0,
            "stage_transitions": defaultdict(int),
            "archived_count": 0,
            "deprecated_count": 0,
        }
        
        # 获取需要维护的记忆（老化和归档候选）
        aging_candidates = await self.get_memories_by_stage(LifecycleStage.AGING)
        stable_candidates = await self.get_memories_by_stage(LifecycleStage.STABLE)
        
        all_candidates = aging_candidates + stable_candidates
        
        for memory in all_candidates:
            old_stage_str = memory.metadata.get("lifecycle_stage")
            new_stage = await self.update_memory_lifecycle(memory, "auto_maintenance")
            
            maintenance_results["processed_memories"] += 1
            
            if old_stage_str != new_stage.value:
                transition = f"{old_stage_str} -> {new_stage.value}"
                maintenance_results["stage_transitions"][transition] += 1
                
                if new_stage == LifecycleStage.ARCHIVED:
                    maintenance_results["archived_count"] += 1
                elif new_stage == LifecycleStage.DEPRECATED:
                    maintenance_results["deprecated_count"] += 1
        
        self.logger.info(f"自动生命周期维护完成，处理了 {maintenance_results['processed_memories']} 个记忆")
        
        return {
            "processed_memories": maintenance_results["processed_memories"],
            "stage_transitions": dict(maintenance_results["stage_transitions"]),
            "archived_count": maintenance_results["archived_count"],
            "deprecated_count": maintenance_results["deprecated_count"],
        }
    
    async def get_lifecycle_statistics(self) -> Dict[str, Any]:
        """获取生命周期统计信息"""
        
        # 当前阶段分布
        current_distribution = {}
        total_current = sum(self.lifecycle_stats["current_distribution"].values())
        
        if total_current > 0:
            for stage, count in self.lifecycle_stats["current_distribution"].items():
                current_distribution[stage] = {
                    "count": count,
                    "percentage": (count / total_current) * 100
                }
        
        # 最近的生命周期事件
        recent_events = sorted(
            self.lifecycle_events,
            key=lambda e: e.timestamp,
            reverse=True
        )[:10]
        
        return {
            "total_managed": self.lifecycle_stats["total_managed"],
            "current_distribution": current_distribution,
            "stage_transitions": dict(self.lifecycle_stats["stage_transitions"]),
            "recent_events": [
                {
                    "memory_id": str(event.memory_id),
                    "stage": event.stage.value,
                    "timestamp": event.timestamp.isoformat(),
                    "trigger": event.trigger,
                }
                for event in recent_events
            ],
            "lifecycle_config": dict(self.lifecycle_config),
        }
    
    async def update_lifecycle_config(self, **kwargs) -> None:
        """更新生命周期配置"""
        for key, value in kwargs.items():
            if key in self.lifecycle_config:
                self.lifecycle_config[key] = value
                self.logger.info(f"生命周期配置已更新: {key} = {value}")
    
    async def reset_statistics(self) -> None:
        """重置统计信息"""
        self.lifecycle_stats = {
            "stage_transitions": defaultdict(int),
            "current_distribution": defaultdict(int),
            "total_managed": 0,
        }
        self.lifecycle_events.clear()
        self.logger.info("生命周期统计信息已重置")
