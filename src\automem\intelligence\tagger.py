"""
标签生成器

自动为记忆内容生成相关标签。
"""

import re
import asyncio
from typing import Dict, List, Set, Any, Optional, Tuple
from collections import Counter, defaultdict

from ..config.settings import IntelligenceConfig
from ..utils.logging import LoggerMixin
from ..core.memory import Memory


class TagGenerator(LoggerMixin):
    """标签生成器"""
    
    def __init__(self, config: IntelligenceConfig):
        self.config = config
        
        # 预定义标签规则
        self.tag_rules = self._initialize_tag_rules()
        
        # 标签统计
        self.tag_stats: Counter = Counter()
        
        # 标签共现矩阵
        self.tag_cooccurrence: Dict[str, Counter] = defaultdict(Counter)
        
        # 停用标签
        self.stop_tags = {
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一",
            "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for"
        }
    
    def _initialize_tag_rules(self) -> Dict[str, Dict[str, Any]]:
        """初始化标签规则"""
        return {
            # 技术相关标签
            "技术": {
                "patterns": [
                    (r"python|java|javascript|c\+\+|golang", "编程语言"),
                    (r"react|vue|angular|django|flask", "框架"),
                    (r"mysql|postgresql|mongodb|redis", "数据库"),
                    (r"docker|kubernetes|aws|azure", "云技术"),
                    (r"git|github|gitlab", "版本控制"),
                    (r"api|rest|graphql", "接口"),
                    (r"机器学习|深度学习|ai|人工智能", "AI"),
                    (r"算法|数据结构", "算法"),
                ],
                "keywords": {
                    "代码": "编程",
                    "bug": "调试",
                    "测试": "测试",
                    "部署": "部署",
                    "优化": "性能",
                    "安全": "安全",
                }
            },
            
            # 工作相关标签
            "工作": {
                "patterns": [
                    (r"会议|meeting", "会议"),
                    (r"项目|project", "项目"),
                    (r"任务|task", "任务"),
                    (r"客户|client|customer", "客户"),
                    (r"团队|team", "团队"),
                    (r"管理|management", "管理"),
                    (r"计划|plan|planning", "计划"),
                    (r"报告|report", "报告"),
                ],
                "keywords": {
                    "deadline": "截止日期",
                    "需求": "需求",
                    "沟通": "沟通",
                    "协作": "协作",
                    "进度": "进度",
                    "质量": "质量",
                }
            },
            
            # 学习相关标签
            "学习": {
                "patterns": [
                    (r"教程|tutorial", "教程"),
                    (r"课程|course", "课程"),
                    (r"文档|documentation", "文档"),
                    (r"笔记|note", "笔记"),
                    (r"总结|summary", "总结"),
                    (r"练习|exercise", "练习"),
                    (r"考试|exam|test", "考试"),
                    (r"研究|research", "研究"),
                ],
                "keywords": {
                    "知识": "知识",
                    "技能": "技能",
                    "经验": "经验",
                    "方法": "方法",
                    "理论": "理论",
                    "实践": "实践",
                }
            },
            
            # 问题相关标签
            "问题": {
                "patterns": [
                    (r"错误|error", "错误"),
                    (r"异常|exception", "异常"),
                    (r"故障|failure", "故障"),
                    (r"问题|problem|issue", "问题"),
                    (r"困难|difficulty", "困难"),
                    (r"挑战|challenge", "挑战"),
                ],
                "keywords": {
                    "解决": "解决方案",
                    "修复": "修复",
                    "调试": "调试",
                    "分析": "分析",
                    "原因": "原因分析",
                    "预防": "预防",
                }
            },
            
            # 时间相关标签
            "时间": {
                "patterns": [
                    (r"今天|today", "今天"),
                    (r"明天|tomorrow", "明天"),
                    (r"昨天|yesterday", "昨天"),
                    (r"本周|this week", "本周"),
                    (r"下周|next week", "下周"),
                    (r"本月|this month", "本月"),
                    (r"紧急|urgent", "紧急"),
                    (r"重要|important", "重要"),
                ],
                "keywords": {
                    "计划": "计划",
                    "安排": "安排",
                    "提醒": "提醒",
                    "截止": "截止日期",
                }
            },
        }
    
    async def generate_tags(self, memory: Memory) -> List[str]:
        """为记忆生成标签"""
        content = memory.content.lower()
        summary = (memory.summary or "").lower()
        combined_text = f"{content} {summary}".strip()
        
        # 生成的标签集合
        generated_tags = set()
        
        # 基于规则生成标签
        rule_tags = await self._generate_rule_based_tags(combined_text)
        generated_tags.update(rule_tags)
        
        # 基于关键词生成标签
        keyword_tags = await self._generate_keyword_tags(combined_text)
        generated_tags.update(keyword_tags)
        
        # 基于实体识别生成标签
        entity_tags = await self._generate_entity_tags(combined_text)
        generated_tags.update(entity_tags)
        
        # 基于上下文生成标签
        context_tags = await self._generate_context_tags(memory)
        generated_tags.update(context_tags)
        
        # 过滤和排序标签
        filtered_tags = await self._filter_and_rank_tags(
            list(generated_tags), 
            combined_text
        )
        
        # 限制标签数量
        max_tags = self.config.max_tags_per_memory
        final_tags = filtered_tags[:max_tags]
        
        # 更新统计信息
        self._update_tag_stats(final_tags)
        
        self.logger.debug(f"生成标签: {final_tags}")
        return final_tags
    
    async def _generate_rule_based_tags(self, text: str) -> List[str]:
        """基于规则生成标签"""
        tags = []
        
        for category, rules in self.tag_rules.items():
            # 模式匹配
            patterns = rules.get("patterns", [])
            for pattern, tag in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    tags.append(tag)
            
            # 关键词匹配
            keywords = rules.get("keywords", {})
            for keyword, tag in keywords.items():
                if keyword.lower() in text:
                    tags.append(tag)
        
        return tags
    
    async def _generate_keyword_tags(self, text: str) -> List[str]:
        """基于关键词生成标签"""
        # 提取重要词汇
        words = re.findall(r'\b\w{2,}\b', text)
        word_freq = Counter(words)
        
        # 过滤停用词和低频词
        important_words = [
            word for word, freq in word_freq.items()
            if (word not in self.stop_tags and 
                freq >= 2 and 
                len(word) >= 3)
        ]
        
        # 返回前几个高频词作为标签
        return important_words[:5]
    
    async def _generate_entity_tags(self, text: str) -> List[str]:
        """基于实体识别生成标签"""
        tags = []
        
        # 简单的实体识别模式
        entity_patterns = {
            "邮箱": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            "网址": r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
            "电话": r'\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b',
            "日期": r'\b\d{4}[-/]\d{1,2}[-/]\d{1,2}\b|\b\d{1,2}[-/]\d{1,2}[-/]\d{4}\b',
            "时间": r'\b\d{1,2}:\d{2}(?::\d{2})?\s?(?:AM|PM|am|pm)?\b',
            "金额": r'\$\d+(?:,\d{3})*(?:\.\d{2})?|\d+(?:,\d{3})*(?:\.\d{2})?\s*(?:元|美元|dollar)',
        }
        
        for entity_type, pattern in entity_patterns.items():
            if re.search(pattern, text):
                tags.append(entity_type)
        
        return tags
    
    async def _generate_context_tags(self, memory: Memory) -> List[str]:
        """基于上下文生成标签"""
        tags = []
        
        # 基于记忆类型
        type_tags = {
            "conversation": "对话",
            "fact": "事实",
            "procedure": "流程",
            "episodic": "经历",
            "semantic": "知识",
        }
        
        if memory.memory_type.value in type_tags:
            tags.append(type_tags[memory.memory_type.value])
        
        # 基于重要性
        importance_tags = {
            "critical": "关键",
            "high": "重要",
            "medium": "一般",
            "low": "次要",
            "minimal": "备注",
        }
        
        if memory.importance.value in importance_tags:
            tags.append(importance_tags[memory.importance.value])
        
        # 基于会话信息
        if memory.session_id:
            tags.append("会话")
        
        if memory.conversation_id:
            tags.append("对话")
        
        # 基于时间
        if memory.is_recent(hours=24):
            tags.append("最近")
        elif memory.is_recent(hours=168):  # 一周
            tags.append("本周")
        
        return tags
    
    async def _filter_and_rank_tags(self, tags: List[str], text: str) -> List[str]:
        """过滤和排序标签"""
        if not tags:
            return []
        
        # 去重
        unique_tags = list(set(tags))
        
        # 过滤无效标签
        valid_tags = [
            tag for tag in unique_tags
            if (len(tag) >= 2 and 
                tag not in self.stop_tags and
                not tag.isdigit())
        ]
        
        # 计算标签相关性得分
        tag_scores = {}
        for tag in valid_tags:
            score = self._calculate_tag_relevance(tag, text)
            tag_scores[tag] = score
        
        # 按得分排序
        sorted_tags = sorted(
            tag_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        return [tag for tag, score in sorted_tags]
    
    def _calculate_tag_relevance(self, tag: str, text: str) -> float:
        """计算标签相关性得分"""
        score = 0.0
        
        # 基础匹配得分
        if tag.lower() in text.lower():
            score += 1.0
        
        # 历史使用频率得分
        if tag in self.tag_stats:
            frequency_score = min(self.tag_stats[tag] / 100.0, 0.5)
            score += frequency_score
        
        # 标签长度得分（更具体的标签得分更高）
        length_score = min(len(tag) / 10.0, 0.3)
        score += length_score
        
        return score
    
    def _update_tag_stats(self, tags: List[str]) -> None:
        """更新标签统计信息"""
        for tag in tags:
            self.tag_stats[tag] += 1
        
        # 更新标签共现矩阵
        for i, tag1 in enumerate(tags):
            for tag2 in tags[i+1:]:
                self.tag_cooccurrence[tag1][tag2] += 1
                self.tag_cooccurrence[tag2][tag1] += 1
    
    async def suggest_related_tags(self, existing_tags: List[str], limit: int = 5) -> List[str]:
        """基于现有标签建议相关标签"""
        if not existing_tags:
            return []
        
        # 收集相关标签
        related_tags = Counter()
        
        for tag in existing_tags:
            if tag in self.tag_cooccurrence:
                for related_tag, count in self.tag_cooccurrence[tag].items():
                    if related_tag not in existing_tags:
                        related_tags[related_tag] += count
        
        # 返回最相关的标签
        return [tag for tag, count in related_tags.most_common(limit)]
    
    async def get_popular_tags(self, limit: int = 20) -> List[Tuple[str, int]]:
        """获取热门标签"""
        return self.tag_stats.most_common(limit)
    
    async def get_tag_stats(self) -> Dict[str, Any]:
        """获取标签统计信息"""
        total_tags = sum(self.tag_stats.values())
        unique_tags = len(self.tag_stats)
        
        return {
            "total_tag_usages": total_tags,
            "unique_tags": unique_tags,
            "average_usage": total_tags / unique_tags if unique_tags > 0 else 0,
            "top_tags": dict(self.tag_stats.most_common(10)),
            "tag_cooccurrence_count": sum(
                len(related) for related in self.tag_cooccurrence.values()
            ),
        }
    
    async def cleanup_rare_tags(self, min_usage: int = 2) -> int:
        """清理低频标签"""
        rare_tags = [
            tag for tag, count in self.tag_stats.items()
            if count < min_usage
        ]
        
        for tag in rare_tags:
            del self.tag_stats[tag]
            if tag in self.tag_cooccurrence:
                del self.tag_cooccurrence[tag]
        
        self.logger.info(f"清理了 {len(rare_tags)} 个低频标签")
        return len(rare_tags)
