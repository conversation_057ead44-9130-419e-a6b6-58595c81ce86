{"dashboard": {"id": null, "title": "AutoMem 监控仪表板", "tags": ["automem", "mcp", "memory"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "服务状态", "type": "stat", "targets": [{"expr": "up{job=\"automem\"}", "legendFormat": "服务状态"}], "fieldConfig": {"defaults": {"mappings": [{"options": {"0": {"text": "离线", "color": "red"}, "1": {"text": "在线", "color": "green"}}, "type": "value"}]}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "总记忆数", "type": "stat", "targets": [{"expr": "automem_memories_total", "legendFormat": "记忆总数"}], "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "请求速率", "type": "stat", "targets": [{"expr": "rate(automem_requests_total[5m])", "legendFormat": "请求/秒"}], "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "错误率", "type": "stat", "targets": [{"expr": "rate(automem_errors_total[5m])", "legendFormat": "错误/秒"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.1}]}}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "内存使用情况", "type": "timeseries", "targets": [{"expr": "process_resident_memory_bytes{job=\"automem\"} / 1024 / 1024", "legendFormat": "内存使用 (MB)"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}}, {"id": 6, "title": "CPU使用率", "type": "timeseries", "targets": [{"expr": "rate(process_cpu_seconds_total{job=\"automem\"}[5m]) * 100", "legendFormat": "CPU使用率 (%)"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}}, {"id": 7, "title": "请求响应时间", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(automem_request_duration_seconds_bucket[5m]))", "legendFormat": "50%分位"}, {"expr": "histogram_quantile(0.95, rate(automem_request_duration_seconds_bucket[5m]))", "legendFormat": "95%分位"}, {"expr": "histogram_quantile(0.99, rate(automem_request_duration_seconds_bucket[5m]))", "legendFormat": "99%分位"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}}, {"id": 8, "title": "记忆操作统计", "type": "timeseries", "targets": [{"expr": "rate(automem_memory_operations_total{operation=\"store\"}[5m])", "legendFormat": "存储操作/秒"}, {"expr": "rate(automem_memory_operations_total{operation=\"search\"}[5m])", "legendFormat": "搜索操作/秒"}, {"expr": "rate(automem_memory_operations_total{operation=\"retrieve\"}[5m])", "legendFormat": "检索操作/秒"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}}, {"id": 9, "title": "存储使用情况", "type": "piechart", "targets": [{"expr": "automem_disk_used_bytes", "legendFormat": "已使用"}, {"expr": "automem_disk_free_bytes", "legendFormat": "可用"}], "gridPos": {"h": 8, "w": 8, "x": 0, "y": 20}}, {"id": 10, "title": "记忆类型分布", "type": "piechart", "targets": [{"expr": "automem_memories_by_type", "legendFormat": "{{type}}"}], "gridPos": {"h": 8, "w": 8, "x": 8, "y": 20}}, {"id": 11, "title": "重要性级别分布", "type": "piechart", "targets": [{"expr": "automem_memories_by_importance", "legendFormat": "{{importance}}"}], "gridPos": {"h": 8, "w": 8, "x": 16, "y": 20}}]}}