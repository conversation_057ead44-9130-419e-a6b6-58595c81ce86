# AutoMem systemd 服务配置文件
# 复制到 /etc/systemd/system/automem.service

[Unit]
Description=AutoMem - 智能记忆管理系统
Documentation=https://github.com/your-org/automem
After=network.target
Wants=network.target

[Service]
Type=simple
User=automem
Group=automem
WorkingDirectory=/opt/automem
Environment=PYTHONPATH=/opt/automem/src
Environment=AUTOMEM_DATA_DIR=/var/lib/automem
Environment=AUTOMEM_CONFIG_FILE=/etc/automem/config.yaml
ExecStart=/opt/automem/venv/bin/python -m automem.cli serve --config /etc/automem/config.yaml
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=automem

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/automem /var/log/automem
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

[Install]
WantedBy=multi-user.target
