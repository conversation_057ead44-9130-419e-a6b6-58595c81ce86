"""
AutoMem MCP服务器主入口

实现基于FastMCP的智能记忆服务器。
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncIterator, Dict, Any, List, Optional
from pathlib import Path

from mcp.server.fastmcp import FastMCP, Context
from mcp.server.fastmcp.prompts import base

from .config import AutoMemConfig
from .core import MemoryManager, Memory, MemoryQuery
from .intelligence import AutoClassifier, TagGenerator, DecisionEngine
from .retrieval import <PERSON>manticSearchEngine, TimelineManager, ContextRetriever
from .utils.logging import get_logger

logger = get_logger(__name__)


class AutoMemServerContext:
    """AutoMem服务器上下文"""
    
    def __init__(self, config: AutoMemConfig):
        self.config = config
        self.memory_manager = None
        self.vector_db = None
        self.autonomous_system = None


class AutoMemServer:
    """AutoMem MCP服务器"""
    
    def __init__(self, config: AutoMemConfig):
        self.config = config
        self.mcp = FastMCP(
            name=config.server_name,
            lifespan=self._lifespan,
        )
        
        # 初始化MCP接口组件
        self.mcp_tools = None
        self.mcp_resources = None
        self.mcp_prompts = None

        # 注册MCP接口
        self._register_tools()
        self._register_resources()
        self._register_prompts()
        
        logger.info(f"AutoMem服务器初始化完成: {config.server_name}")
    
    @asynccontextmanager
    async def _lifespan(self, server: FastMCP) -> AsyncIterator[AutoMemServerContext]:
        """服务器生命周期管理"""
        logger.info("正在启动AutoMem服务器...")
        
        # 创建上下文
        context = AutoMemServerContext(self.config)
        
        try:
            # 初始化核心组件
            await self._initialize_components(context)
            logger.info("✅ 核心组件初始化完成")
            
            yield context
            
        except Exception as e:
            logger.error(f"❌ 服务器启动失败: {e}")
            raise
        finally:
            # 清理资源
            await self._cleanup_components(context)
            logger.info("🧹 服务器资源清理完成")
    
    async def _initialize_components(self, context: AutoMemServerContext) -> None:
        """初始化核心组件"""
        from .core.storage import SQLiteMemoryManager
        from .core.vector_db import VectorDatabase
        from .intelligence.autonomous import AutonomousMemorySystem
        from .mcp.tools import AutoMemTools
        from .mcp.resources import AutoMemResources
        from .mcp.prompts import AutoMemPrompts

        # 初始化存储引擎
        context.memory_manager = SQLiteMemoryManager(context.config.storage)
        await context.memory_manager.initialize()

        # 初始化向量数据库
        context.vector_db = VectorDatabase(context.config.storage, context.config.intelligence)
        await context.vector_db.initialize()

        # 初始化自主记忆系统
        context.autonomous_system = AutonomousMemorySystem(
            context.config.intelligence,
            context.config.retrieval,
            context.config.storage,
            context.memory_manager.storage,
            context.vector_db
        )

        # 初始化MCP接口组件
        self.mcp_tools = AutoMemTools(context.autonomous_system)
        self.mcp_resources = AutoMemResources(context.autonomous_system)
        self.mcp_prompts = AutoMemPrompts(context.autonomous_system)

        logger.info("✅ 核心组件初始化完成")
    
    async def _cleanup_components(self, context: AutoMemServerContext) -> None:
        """清理组件资源"""
        # TODO: 实现资源清理
        logger.info("资源清理占位符 - 待实现")
    
    def _register_tools(self) -> None:
        """注册MCP工具"""

        @self.mcp.tool()
        async def store_memory(
            content: str,
            memory_type: str = "conversation",
            importance: str = "medium",
            tags: List[str] = None,
            categories: List[str] = None,
            session_id: str = None,
            conversation_id: str = None,
            source: str = None,
            auto_process: bool = True,
            ctx: Context = None,
        ) -> Dict[str, Any]:
            """存储记忆到AutoMem系统"""
            if self.mcp_tools:
                return await self.mcp_tools.store_memory(
                    content=content,
                    memory_type=memory_type,
                    importance=importance,
                    tags=tags,
                    categories=categories,
                    session_id=session_id,
                    conversation_id=conversation_id,
                    source=source,
                    auto_process=auto_process
                )
            else:
                return {"success": False, "error": "服务器未完全初始化"}
        
        @self.mcp.tool()
        async def search_memories(
            query: str,
            limit: int = 10,
            memory_types: List[str] = None,
            importance_levels: List[str] = None,
            tags: List[str] = None,
            categories: List[str] = None,
            session_id: str = None,
            conversation_id: str = None,
            similarity_threshold: float = 0.6,
            include_context: bool = True,
            ctx: Context = None,
        ) -> Dict[str, Any]:
            """搜索相关记忆"""
            if self.mcp_tools:
                return await self.mcp_tools.search_memories(
                    query=query,
                    limit=limit,
                    memory_types=memory_types,
                    importance_levels=importance_levels,
                    tags=tags,
                    categories=categories,
                    session_id=session_id,
                    conversation_id=conversation_id,
                    similarity_threshold=similarity_threshold,
                    include_context=include_context
                )
            else:
                return {"success": False, "error": "服务器未完全初始化"}
        
        @self.mcp.tool()
        async def get_context(
            topic: str = None,
            session_id: str = None,
            conversation_id: str = None,
            time_window_hours: int = 24,
            limit: int = 5,
            include_related: bool = True,
            ctx: Context = None,
        ) -> Dict[str, Any]:
            """获取当前对话的相关上下文"""
            if self.mcp_tools:
                return await self.mcp_tools.get_context(
                    topic=topic,
                    session_id=session_id,
                    conversation_id=conversation_id,
                    time_window_hours=time_window_hours,
                    limit=limit,
                    include_related=include_related
                )
            else:
                return {"success": False, "error": "服务器未完全初始化"}

        @self.mcp.tool()
        async def manage_tags(
            action: str,
            memory_id: str = None,
            tag: str = None,
            tags: List[str] = None,
            ctx: Context = None,
        ) -> Dict[str, Any]:
            """管理记忆标签 (add, remove, list, suggest)"""
            if self.mcp_tools:
                return await self.mcp_tools.manage_tags(
                    action=action,
                    memory_id=memory_id,
                    tag=tag,
                    tags=tags
                )
            else:
                return {"success": False, "error": "服务器未完全初始化"}

        @self.mcp.tool()
        async def cleanup_memories(
            days: int = 30,
            dry_run: bool = True,
            min_importance: str = "minimal",
            max_access_count: int = 2,
            ctx: Context = None,
        ) -> Dict[str, Any]:
            """清理过时记忆"""
            if self.mcp_tools:
                return await self.mcp_tools.cleanup_memories(
                    days=days,
                    dry_run=dry_run,
                    min_importance=min_importance,
                    max_access_count=max_access_count
                )
            else:
                return {"success": False, "error": "服务器未完全初始化"}

        @self.mcp.tool()
        async def get_memory_stats(ctx: Context = None) -> Dict[str, Any]:
            """获取记忆统计信息"""
            if self.mcp_tools:
                return await self.mcp_tools.get_memory_stats()
            else:
                return {"success": False, "error": "服务器未完全初始化"}
        
        @self.mcp.tool()
        async def manage_tags(
            action: str,
            memory_id: str = None,
            tag: str = None,
            ctx: Context = None,
        ) -> Dict[str, Any]:
            """管理记忆标签"""
            try:
                # TODO: 实现标签管理逻辑
                logger.info(f"标签管理请求: {action} - {tag}")
                
                return {
                    "success": True,
                    "action": action,
                    "memory_id": memory_id,
                    "tag": tag,
                    "message": f"标签{action}操作完成",
                }
            except Exception as e:
                logger.error(f"标签管理失败: {e}")
                return {
                    "success": False,
                    "error": str(e),
                }
        
        @self.mcp.tool()
        async def cleanup_memories(
            days: int = 30,
            dry_run: bool = True,
            ctx: Context = None,
        ) -> Dict[str, Any]:
            """清理过时记忆"""
            try:
                # TODO: 实现记忆清理逻辑
                logger.info(f"清理记忆请求: {days}天前, dry_run={dry_run}")
                
                return {
                    "success": True,
                    "days": days,
                    "dry_run": dry_run,
                    "cleaned_count": 0,  # 占位符
                    "message": "清理操作完成" if not dry_run else "清理预览完成",
                }
            except Exception as e:
                logger.error(f"清理记忆失败: {e}")
                return {
                    "success": False,
                    "error": str(e),
                }
    
    def _register_resources(self) -> None:
        """注册MCP资源"""
        
        @self.mcp.resource("memory://recent")
        async def get_recent_memories() -> str:
            """获取最近的记忆"""
            if self.mcp_resources:
                return await self.mcp_resources.get_recent_memories(hours=24, limit=10)
            else:
                return "# 最近的记忆\n\n服务器未完全初始化。"

        @self.mcp.resource("memory://context/{topic}")
        async def get_context_memories(topic: str) -> str:
            """获取特定主题的上下文记忆"""
            if self.mcp_resources:
                return await self.mcp_resources.get_context_memories(topic=topic, limit=5)
            else:
                return f"# 主题 '{topic}' 的相关记忆\n\n服务器未完全初始化。"
        
        @self.mcp.resource("memory://timeline/{date}")
        async def get_timeline_memories(date: str) -> str:
            """获取特定日期的记忆"""
            if self.mcp_resources:
                return await self.mcp_resources.get_timeline_memories(date=date, format="daily")
            else:
                return f"# 日期 {date} 的记忆\n\n服务器未完全初始化。"

        @self.mcp.resource("memory://tags/{tag}")
        async def get_tagged_memories(tag: str) -> str:
            """获取特定标签的记忆"""
            if self.mcp_resources:
                return await self.mcp_resources.get_tagged_memories(tag=tag, limit=20)
            else:
                return f"# 标签 '{tag}' 的记忆\n\n服务器未完全初始化。"

        @self.mcp.resource("memory://summary")
        async def get_memory_summary() -> str:
            """记忆系统摘要"""
            if self.mcp_resources:
                return await self.mcp_resources.get_memory_summary()
            else:
                return "# AutoMem 记忆系统摘要\n\n服务器未完全初始化。"
    
    def _register_prompts(self) -> None:
        """注册MCP提示符"""
        
        @self.mcp.prompt(title="回忆相关上下文")
        async def recall_context(
            topic: str,
            depth: str = "medium",
            time_range: str = "recent",
            focus: str = "general"
        ) -> str:
            """生成回忆相关上下文的提示符"""
            if self.mcp_prompts:
                return await self.mcp_prompts.recall_context_prompt(
                    topic=topic,
                    depth=depth,
                    time_range=time_range,
                    focus=focus
                )
            else:
                return f"请回忆与主题 '{topic}' 相关的上下文信息。（服务器未完全初始化）"
        
        @self.mcp.prompt(title="总结记忆")
        async def summarize_memories(
            time_range: str = "recent",
            focus: str = "general",
            memory_types: List[str] = None,
            importance_filter: str = "medium"
        ) -> List[base.Message]:
            """生成总结记忆的提示符"""
            if self.mcp_prompts:
                messages = await self.mcp_prompts.summarize_memories_prompt(
                    time_range=time_range,
                    focus=focus,
                    memory_types=memory_types,
                    importance_filter=importance_filter
                )
                return [base.UserMessage(msg["content"]) for msg in messages]
            else:
                return [base.UserMessage("请总结相关记忆。（服务器未完全初始化）")]
        
        @self.mcp.prompt(title="发现记忆连接")
        async def find_connections(
            concept: str,
            connection_types: List[str] = None
        ) -> str:
            """生成发现记忆连接的提示符"""
            if self.mcp_prompts:
                return await self.mcp_prompts.find_connections_prompt(
                    concept=concept,
                    connection_types=connection_types
                )
            else:
                return f"请分析概念 '{concept}' 与已有记忆之间的潜在连接。（服务器未完全初始化）"

        @self.mcp.prompt(title="决策支持")
        async def decision_support(
            decision_context: str,
            decision_type: str = "general"
        ) -> str:
            """生成决策支持提示符"""
            if self.mcp_prompts:
                return await self.mcp_prompts.decision_support_prompt(
                    decision_context=decision_context,
                    decision_type=decision_type
                )
            else:
                return f"请为决策提供支持：{decision_context}（服务器未完全初始化）"
    
    async def run(self) -> None:
        """运行服务器"""
        try:
            if self.config.mcp_transport == "stdio":
                # stdio模式
                logger.info("🚀 启动stdio模式服务器")
                await self.mcp.run()
            else:
                # HTTP模式
                logger.info(f"🚀 启动HTTP服务器: {self.config.mcp_host}:{self.config.mcp_port}")
                await self.mcp.run(
                    transport="sse",
                    host=self.config.mcp_host,
                    port=self.config.mcp_port,
                )
        except Exception as e:
            logger.error(f"❌ 服务器运行失败: {e}")
            raise
