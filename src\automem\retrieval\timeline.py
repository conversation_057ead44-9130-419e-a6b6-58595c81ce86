"""
时间轴管理器

管理记忆的时间维度，提供基于时间的检索和分析功能。
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Set, Any, Optional, Tuple
from collections import defaultdict
from uuid import UUID

from ..config.settings import StorageConfig
from ..utils.logging import LoggerMixin
from ..core.memory import Memory, MemoryQuery, MemoryType, MemoryImportance
from ..core.storage import StorageEngine


class TimelineEvent:
    """时间轴事件"""
    
    def __init__(
        self,
        memory: Memory,
        event_type: str = "created",
        timestamp: Optional[datetime] = None
    ):
        self.memory = memory
        self.event_type = event_type  # created, updated, accessed
        self.timestamp = timestamp or memory.created_at
    
    def __repr__(self) -> str:
        return f"TimelineEvent({self.event_type}, {self.timestamp}, {self.memory.id})"


class TimePeriod:
    """时间段"""
    
    def __init__(self, start: datetime, end: datetime, label: str):
        self.start = start
        self.end = end
        self.label = label
        self.memories: List[Memory] = []
        self.events: List[TimelineEvent] = []
    
    def add_memory(self, memory: Memory) -> None:
        """添加记忆到时间段"""
        if self.start <= memory.created_at <= self.end:
            self.memories.append(memory)
    
    def add_event(self, event: TimelineEvent) -> None:
        """添加事件到时间段"""
        if self.start <= event.timestamp <= self.end:
            self.events.append(event)
    
    @property
    def memory_count(self) -> int:
        return len(self.memories)
    
    @property
    def event_count(self) -> int:
        return len(self.events)


class TimelineManager(LoggerMixin):
    """时间轴管理器"""
    
    def __init__(self, config: StorageConfig, storage: StorageEngine):
        self.config = config
        self.storage = storage
        
        # 时间段定义
        self.time_periods = self._initialize_time_periods()
        
        # 缓存
        self._timeline_cache: Optional[List[TimelineEvent]] = None
        self._cache_timestamp: Optional[datetime] = None
        self._cache_ttl = timedelta(minutes=10)
    
    def _initialize_time_periods(self) -> Dict[str, TimePeriod]:
        """初始化时间段"""
        now = datetime.now(timezone.utc)
        
        return {
            "今天": TimePeriod(
                start=now.replace(hour=0, minute=0, second=0, microsecond=0),
                end=now.replace(hour=23, minute=59, second=59, microsecond=999999),
                label="今天"
            ),
            "昨天": TimePeriod(
                start=(now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0),
                end=(now - timedelta(days=1)).replace(hour=23, minute=59, second=59, microsecond=999999),
                label="昨天"
            ),
            "本周": TimePeriod(
                start=now - timedelta(days=now.weekday()),
                end=now,
                label="本周"
            ),
            "上周": TimePeriod(
                start=now - timedelta(days=now.weekday() + 7),
                end=now - timedelta(days=now.weekday()),
                label="上周"
            ),
            "本月": TimePeriod(
                start=now.replace(day=1, hour=0, minute=0, second=0, microsecond=0),
                end=now,
                label="本月"
            ),
            "上月": TimePeriod(
                start=(now.replace(day=1) - timedelta(days=1)).replace(day=1),
                end=now.replace(day=1) - timedelta(microseconds=1),
                label="上月"
            ),
        }
    
    async def get_memories_by_time_period(self, period_name: str) -> List[Memory]:
        """获取指定时间段的记忆"""
        if period_name not in self.time_periods:
            raise ValueError(f"未知时间段: {period_name}")
        
        period = self.time_periods[period_name]
        
        # 构建查询
        query = MemoryQuery(
            created_after=period.start,
            created_before=period.end,
            sort_by="created_at",
            sort_order="desc",
            limit=1000  # 大量限制以获取所有记忆
        )
        
        memories = await self.storage.search_memories(query)
        
        self.logger.debug(f"时间段 '{period_name}' 包含 {len(memories)} 个记忆")
        return memories
    
    async def get_memories_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        limit: int = 100
    ) -> List[Memory]:
        """获取指定日期范围的记忆"""
        query = MemoryQuery(
            created_after=start_date,
            created_before=end_date,
            sort_by="created_at",
            sort_order="desc",
            limit=limit
        )
        
        memories = await self.storage.search_memories(query)
        
        self.logger.debug(f"日期范围 {start_date} - {end_date} 包含 {len(memories)} 个记忆")
        return memories
    
    async def get_timeline_events(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        event_types: Optional[List[str]] = None,
        limit: int = 100
    ) -> List[TimelineEvent]:
        """获取时间轴事件"""
        
        # 设置默认时间范围
        if not start_date:
            start_date = datetime.now(timezone.utc) - timedelta(days=30)
        if not end_date:
            end_date = datetime.now(timezone.utc)
        
        # 获取记忆
        memories = await self.get_memories_by_date_range(start_date, end_date, limit * 3)
        
        # 创建事件
        events = []
        for memory in memories:
            # 创建事件
            if not event_types or "created" in event_types:
                events.append(TimelineEvent(memory, "created", memory.created_at))
            
            # 更新事件
            if (not event_types or "updated" in event_types) and memory.updated_at != memory.created_at:
                events.append(TimelineEvent(memory, "updated", memory.updated_at))
            
            # 访问事件
            if (not event_types or "accessed" in event_types) and memory.accessed_at != memory.created_at:
                events.append(TimelineEvent(memory, "accessed", memory.accessed_at))
        
        # 按时间排序
        events.sort(key=lambda e: e.timestamp, reverse=True)
        
        return events[:limit]
    
    async def get_memory_activity_by_hour(
        self,
        date: Optional[datetime] = None
    ) -> Dict[int, int]:
        """获取按小时统计的记忆活动"""
        if not date:
            date = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        
        start_date = date
        end_date = date + timedelta(days=1)
        
        memories = await self.get_memories_by_date_range(start_date, end_date)
        
        # 按小时统计
        hourly_activity = defaultdict(int)
        for memory in memories:
            hour = memory.created_at.hour
            hourly_activity[hour] += 1
        
        # 填充所有小时
        result = {}
        for hour in range(24):
            result[hour] = hourly_activity[hour]
        
        return result
    
    async def get_memory_activity_by_day(
        self,
        start_date: Optional[datetime] = None,
        days: int = 30
    ) -> Dict[str, int]:
        """获取按天统计的记忆活动"""
        if not start_date:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        end_date = start_date + timedelta(days=days)
        memories = await self.get_memories_by_date_range(start_date, end_date, 10000)
        
        # 按天统计
        daily_activity = defaultdict(int)
        for memory in memories:
            date_key = memory.created_at.strftime("%Y-%m-%d")
            daily_activity[date_key] += 1
        
        # 填充所有日期
        result = {}
        current_date = start_date
        while current_date < end_date:
            date_key = current_date.strftime("%Y-%m-%d")
            result[date_key] = daily_activity[date_key]
            current_date += timedelta(days=1)
        
        return result
    
    async def get_recent_memories(
        self,
        hours: int = 24,
        limit: int = 10,
        importance_filter: Optional[List[MemoryImportance]] = None
    ) -> List[Memory]:
        """获取最近的记忆"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        query = MemoryQuery(
            created_after=cutoff_time,
            importance_levels=importance_filter,
            sort_by="created_at",
            sort_order="desc",
            limit=limit
        )
        
        memories = await self.storage.search_memories(query)
        
        self.logger.debug(f"最近 {hours} 小时内有 {len(memories)} 个记忆")
        return memories
    
    async def get_memory_trends(self, days: int = 30) -> Dict[str, Any]:
        """获取记忆趋势分析"""
        start_date = datetime.now(timezone.utc) - timedelta(days=days)
        memories = await self.get_memories_by_date_range(start_date, datetime.now(timezone.utc), 10000)
        
        if not memories:
            return {"message": "无记忆数据"}
        
        # 按类型统计
        type_counts = defaultdict(int)
        importance_counts = defaultdict(int)
        
        # 按周统计
        weekly_counts = defaultdict(int)
        
        for memory in memories:
            type_counts[memory.memory_type.value] += 1
            importance_counts[memory.importance.value] += 1
            
            # 计算周数
            week_start = memory.created_at - timedelta(days=memory.created_at.weekday())
            week_key = week_start.strftime("%Y-W%U")
            weekly_counts[week_key] += 1
        
        # 计算趋势
        total_memories = len(memories)
        avg_per_day = total_memories / days
        
        # 最近一周 vs 前一周的比较
        recent_week = datetime.now(timezone.utc) - timedelta(days=7)
        recent_memories = [m for m in memories if m.created_at >= recent_week]
        previous_week_start = recent_week - timedelta(days=7)
        previous_memories = [
            m for m in memories 
            if previous_week_start <= m.created_at < recent_week
        ]
        
        trend_direction = "stable"
        if len(recent_memories) > len(previous_memories) * 1.2:
            trend_direction = "increasing"
        elif len(recent_memories) < len(previous_memories) * 0.8:
            trend_direction = "decreasing"
        
        return {
            "total_memories": total_memories,
            "average_per_day": avg_per_day,
            "trend_direction": trend_direction,
            "recent_week_count": len(recent_memories),
            "previous_week_count": len(previous_memories),
            "type_distribution": dict(type_counts),
            "importance_distribution": dict(importance_counts),
            "weekly_counts": dict(weekly_counts),
        }
    
    async def find_memory_gaps(self, min_gap_hours: int = 6) -> List[Tuple[datetime, datetime]]:
        """查找记忆空白期"""
        # 获取最近30天的记忆
        start_date = datetime.now(timezone.utc) - timedelta(days=30)
        memories = await self.get_memories_by_date_range(start_date, datetime.now(timezone.utc), 10000)
        
        if len(memories) < 2:
            return []
        
        # 按时间排序
        memories.sort(key=lambda m: m.created_at)
        
        gaps = []
        for i in range(len(memories) - 1):
            current_time = memories[i].created_at
            next_time = memories[i + 1].created_at
            
            gap_duration = next_time - current_time
            if gap_duration.total_seconds() >= min_gap_hours * 3600:
                gaps.append((current_time, next_time))
        
        self.logger.debug(f"发现 {len(gaps)} 个记忆空白期")
        return gaps
    
    async def get_time_period_summary(self, period_name: str) -> Dict[str, Any]:
        """获取时间段摘要"""
        memories = await self.get_memories_by_time_period(period_name)
        
        if not memories:
            return {"period": period_name, "message": "无记忆数据"}
        
        # 统计信息
        type_counts = defaultdict(int)
        importance_counts = defaultdict(int)
        tag_counts = defaultdict(int)
        
        for memory in memories:
            type_counts[memory.memory_type.value] += 1
            importance_counts[memory.importance.value] += 1
            
            for tag in memory.tags:
                tag_counts[tag] += 1
        
        # 最重要的记忆
        important_memories = [
            m for m in memories 
            if m.importance in [MemoryImportance.CRITICAL, MemoryImportance.HIGH]
        ]
        
        return {
            "period": period_name,
            "total_memories": len(memories),
            "important_memories": len(important_memories),
            "type_distribution": dict(type_counts),
            "importance_distribution": dict(importance_counts),
            "top_tags": dict(sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
            "time_range": {
                "start": self.time_periods[period_name].start.isoformat(),
                "end": self.time_periods[period_name].end.isoformat(),
            }
        }
    
    async def cleanup_old_timeline_data(self, days: int = 90) -> int:
        """清理旧的时间轴数据"""
        # 这里主要是清理缓存，实际的记忆清理由存储引擎处理
        self._timeline_cache = None
        self._cache_timestamp = None
        
        self.logger.info("时间轴缓存已清理")
        return 0
