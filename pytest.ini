[tool:pytest]
# pytest 配置文件

# 测试发现
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 最小版本要求
minversion = 7.0

# 添加选项
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src/automem
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --durations=10

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试 (跳过除非明确指定)
    requires_model: 需要机器学习模型的测试
    requires_network: 需要网络连接的测试
    requires_gpu: 需要GPU的测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:chromadb.*
    ignore::UserWarning:sentence_transformers.*

# 异步测试配置
asyncio_mode = auto

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 测试超时 (秒)
timeout = 300

# 并行测试
# 需要安装 pytest-xdist: pip install pytest-xdist
# 使用 -n auto 或 -n <num_workers>
