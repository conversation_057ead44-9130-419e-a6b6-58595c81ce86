"""
测试存储引擎
"""

import pytest
from datetime import datetime, timezone, timedelta
from uuid import UUID

from src.automem.core.storage import SQLiteMemoryManager
from src.automem.core.memory import Memory, MemoryType, MemoryImportance, MemoryQuery
from src.automem.config import StorageConfig


class TestSQLiteMemoryManager:
    """测试SQLite存储管理器"""
    
    @pytest.mark.asyncio
    async def test_initialization(self, memory_manager):
        """测试初始化"""
        assert memory_manager is not None
        assert memory_manager.storage is not None
    
    @pytest.mark.asyncio
    async def test_store_memory(self, memory_manager, sample_memory):
        """测试存储记忆"""
        stored_memory = await memory_manager.store_memory(sample_memory)
        
        assert stored_memory.id == sample_memory.id
        assert stored_memory.content == sample_memory.content
        assert stored_memory.memory_type == sample_memory.memory_type
        assert stored_memory.importance == sample_memory.importance
    
    @pytest.mark.asyncio
    async def test_get_memory(self, memory_manager, sample_memory):
        """测试获取记忆"""
        # 先存储
        await memory_manager.store_memory(sample_memory)
        
        # 再获取
        retrieved_memory = await memory_manager.get_memory(sample_memory.id)
        
        assert retrieved_memory is not None
        assert retrieved_memory.id == sample_memory.id
        assert retrieved_memory.content == sample_memory.content
        assert retrieved_memory.memory_type == sample_memory.memory_type
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_memory(self, memory_manager):
        """测试获取不存在的记忆"""
        from uuid import uuid4
        
        nonexistent_id = uuid4()
        memory = await memory_manager.get_memory(nonexistent_id)
        
        assert memory is None
    
    @pytest.mark.asyncio
    async def test_update_memory(self, memory_manager, sample_memory):
        """测试更新记忆"""
        # 先存储
        await memory_manager.store_memory(sample_memory)
        
        # 更新内容
        sample_memory.update_content("更新后的内容")
        sample_memory.add_tag("新标签")
        
        # 保存更新
        updated_memory = await memory_manager.update_memory(sample_memory)
        
        assert updated_memory.content == "更新后的内容"
        assert "新标签" in updated_memory.tags
        
        # 验证数据库中的更新
        retrieved_memory = await memory_manager.get_memory(sample_memory.id)
        assert retrieved_memory.content == "更新后的内容"
        assert "新标签" in retrieved_memory.tags
    
    @pytest.mark.asyncio
    async def test_delete_memory(self, memory_manager, sample_memory):
        """测试删除记忆"""
        # 先存储
        await memory_manager.store_memory(sample_memory)
        
        # 确认存在
        retrieved_memory = await memory_manager.get_memory(sample_memory.id)
        assert retrieved_memory is not None
        
        # 删除
        success = await memory_manager.delete_memory(sample_memory.id)
        assert success is True
        
        # 确认已删除
        deleted_memory = await memory_manager.get_memory(sample_memory.id)
        assert deleted_memory is None
    
    @pytest.mark.asyncio
    async def test_search_memories_basic(self, memory_manager, sample_memories):
        """测试基本记忆搜索"""
        # 存储多个记忆
        for memory in sample_memories:
            await memory_manager.store_memory(memory)
        
        # 搜索所有记忆
        query = MemoryQuery(limit=10)
        results = await memory_manager.search_memories(query)
        
        assert len(results) == len(sample_memories)
    
    @pytest.mark.asyncio
    async def test_search_memories_by_content(self, memory_manager, sample_memories):
        """测试按内容搜索记忆"""
        # 存储多个记忆
        for memory in sample_memories:
            await memory_manager.store_memory(memory)
        
        # 搜索包含"Python"的记忆
        query = MemoryQuery(content="Python", limit=10)
        results = await memory_manager.search_memories(query)
        
        assert len(results) >= 1
        assert any("Python" in result.content for result in results)
    
    @pytest.mark.asyncio
    async def test_search_memories_by_type(self, memory_manager, sample_memories):
        """测试按类型搜索记忆"""
        # 存储多个记忆
        for memory in sample_memories:
            await memory_manager.store_memory(memory)
        
        # 搜索事实类型的记忆
        query = MemoryQuery(
            memory_types=[MemoryType.FACT],
            limit=10
        )
        results = await memory_manager.search_memories(query)
        
        assert len(results) >= 1
        assert all(result.memory_type == MemoryType.FACT for result in results)
    
    @pytest.mark.asyncio
    async def test_search_memories_by_importance(self, memory_manager, sample_memories):
        """测试按重要性搜索记忆"""
        # 存储多个记忆
        for memory in sample_memories:
            await memory_manager.store_memory(memory)
        
        # 搜索高重要性的记忆
        query = MemoryQuery(
            importance_levels=[MemoryImportance.HIGH],
            limit=10
        )
        results = await memory_manager.search_memories(query)
        
        assert len(results) >= 1
        assert all(result.importance == MemoryImportance.HIGH for result in results)
    
    @pytest.mark.asyncio
    async def test_search_memories_by_tags(self, memory_manager, sample_memories):
        """测试按标签搜索记忆"""
        # 存储多个记忆
        for memory in sample_memories:
            await memory_manager.store_memory(memory)
        
        # 搜索包含特定标签的记忆
        query = MemoryQuery(
            tags=["Python"],
            limit=10
        )
        results = await memory_manager.search_memories(query)
        
        assert len(results) >= 1
        assert all("Python" in result.tags for result in results)
    
    @pytest.mark.asyncio
    async def test_search_memories_by_time_range(self, memory_manager, sample_memories):
        """测试按时间范围搜索记忆"""
        # 存储多个记忆
        for memory in sample_memories:
            await memory_manager.store_memory(memory)
        
        # 搜索最近创建的记忆
        now = datetime.now(timezone.utc)
        one_hour_ago = now - timedelta(hours=1)
        
        query = MemoryQuery(
            created_after=one_hour_ago,
            limit=10
        )
        results = await memory_manager.search_memories(query)
        
        assert len(results) == len(sample_memories)
        assert all(result.created_at >= one_hour_ago for result in results)
    
    @pytest.mark.asyncio
    async def test_get_recent_memories(self, memory_manager, sample_memories):
        """测试获取最近记忆"""
        # 存储多个记忆
        for memory in sample_memories:
            await memory_manager.store_memory(memory)
        
        # 获取最近的记忆
        recent_memories = await memory_manager.get_recent_memories(hours=24, limit=3)
        
        assert len(recent_memories) <= 3
        assert len(recent_memories) <= len(sample_memories)
        
        # 验证按时间排序
        if len(recent_memories) > 1:
            for i in range(len(recent_memories) - 1):
                assert recent_memories[i].created_at >= recent_memories[i + 1].created_at
    
    @pytest.mark.asyncio
    async def test_get_memory_stats(self, memory_manager, sample_memories):
        """测试获取记忆统计"""
        # 存储多个记忆
        for memory in sample_memories:
            await memory_manager.store_memory(memory)
        
        # 获取统计信息
        stats = await memory_manager.get_memory_stats()
        
        assert "total_memories" in stats
        assert stats["total_memories"] == len(sample_memories)
        assert "by_type" in stats
        assert "by_importance" in stats
        assert "recent_week" in stats
    
    @pytest.mark.asyncio
    async def test_cleanup_old_memories(self, memory_manager, sample_memories):
        """测试清理旧记忆"""
        # 存储多个记忆
        for memory in sample_memories:
            await memory_manager.store_memory(memory)
        
        # 清理超过0天的记忆（应该清理所有）
        cleaned_count = await memory_manager.cleanup_old_memories(days=0)
        
        # 验证清理结果
        assert cleaned_count >= 0
        
        # 获取剩余记忆数量
        query = MemoryQuery(limit=100)
        remaining_memories = await memory_manager.search_memories(query)
        
        # 根据清理策略，可能还有一些记忆保留
        assert len(remaining_memories) <= len(sample_memories)
    
    @pytest.mark.asyncio
    async def test_backup_and_restore(self, memory_manager, sample_memories, temp_dir):
        """测试备份和恢复"""
        # 存储多个记忆
        for memory in sample_memories:
            await memory_manager.store_memory(memory)
        
        # 创建备份
        backup_path = temp_dir / "backup.db"
        success = await memory_manager.backup_database(backup_path)
        assert success is True
        assert backup_path.exists()
        
        # 清空数据库
        for memory in sample_memories:
            await memory_manager.delete_memory(memory.id)
        
        # 验证数据已清空
        query = MemoryQuery(limit=100)
        empty_results = await memory_manager.search_memories(query)
        assert len(empty_results) == 0
        
        # 恢复备份
        restore_success = await memory_manager.restore_database(backup_path)
        assert restore_success is True
        
        # 验证数据已恢复
        restored_results = await memory_manager.search_memories(query)
        assert len(restored_results) == len(sample_memories)
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, memory_manager, sample_memories):
        """测试并发操作"""
        import asyncio
        
        # 并发存储记忆
        store_tasks = [
            memory_manager.store_memory(memory)
            for memory in sample_memories
        ]
        
        stored_memories = await asyncio.gather(*store_tasks)
        assert len(stored_memories) == len(sample_memories)
        
        # 并发获取记忆
        get_tasks = [
            memory_manager.get_memory(memory.id)
            for memory in stored_memories
        ]
        
        retrieved_memories = await asyncio.gather(*get_tasks)
        assert len(retrieved_memories) == len(sample_memories)
        assert all(memory is not None for memory in retrieved_memories)
    
    @pytest.mark.asyncio
    async def test_transaction_rollback(self, memory_manager, sample_memory):
        """测试事务回滚"""
        # 这个测试需要模拟数据库错误来触发回滚
        # 在实际实现中，可能需要更复杂的设置
        
        # 存储一个记忆
        await memory_manager.store_memory(sample_memory)
        
        # 验证记忆存在
        retrieved_memory = await memory_manager.get_memory(sample_memory.id)
        assert retrieved_memory is not None
        
        # 尝试存储一个无效的记忆（如果有验证的话）
        # 这里只是示例，实际实现可能不同
        try:
            invalid_memory = Memory(
                content="",  # 空内容可能无效
                memory_type=MemoryType.CONVERSATION,
                importance=MemoryImportance.MEDIUM
            )
            await memory_manager.store_memory(invalid_memory)
        except Exception:
            # 预期的异常
            pass
        
        # 验证原始记忆仍然存在
        still_exists = await memory_manager.get_memory(sample_memory.id)
        assert still_exists is not None
