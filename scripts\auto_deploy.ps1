# AutoMem Windows 全自动化部署脚本
# PowerShell 版本

param(
    [string]$DeployType = "docker",
    [string]$Environment = "production",
    [switch]$Monitoring,
    [switch]$NoAutoStart,
    [switch]$NoBackup,
    [switch]$Clean,
    [string]$ConfigFile,
    [string]$DataDir,
    [int]$Port = 8000,
    [switch]$Help
)

# 颜色函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Blue "[INFO] $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "[SUCCESS] $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "[WARNING] $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "[ERROR] $message"
}

function Write-Step($message) {
    Write-ColorOutput Magenta "[STEP] $message"
}

# 显示横幅
function Show-Banner {
    Write-Host @"
    ___        __       __  __                 
   /   | __  __/ /_____/  |/  /__  ____ ___    
  / /| |/ / / / __/ __ \  /|_/ / _ \/ __ `__ \   
 / ___ / /_/ / /_/ /_/ / /  / /  __/ / / / / /   
/_/  |_\__,_/\__/\____/_/  /_/\___/_/ /_/ /_/    
                                                 
    智能记忆管理系统 - Windows 全自动化部署
    
"@
}

# 显示帮助
function Show-Help {
    Write-Host @"
AutoMem Windows 全自动化部署脚本

用法: .\auto_deploy.ps1 [参数]

部署类型:
  -DeployType docker      Docker 部署 (默认)
  -DeployType local       本地开发部署

环境选项:
  -Environment dev        开发环境
  -Environment staging    测试环境
  -Environment production 生产环境 (默认)

功能选项:
  -Monitoring            启用监控服务
  -NoAutoStart           不自动启动服务
  -NoBackup              不备份现有数据
  -Clean                 清理现有部署

其他选项:
  -ConfigFile FILE       指定配置文件
  -DataDir DIR           指定数据目录
  -Port PORT             指定服务端口 (默认: 8000)
  -Help                  显示帮助信息

示例:
  .\auto_deploy.ps1                           # 默认 Docker 生产部署
  .\auto_deploy.ps1 -DeployType docker -Monitoring  # Docker 部署 + 监控
  .\auto_deploy.ps1 -DeployType local -Environment dev  # 本地开发环境
  .\auto_deploy.ps1 -Clean                    # 清理现有部署

"@
}

# 检查系统要求
function Test-Requirements {
    Write-Step "检查系统要求..."
    
    # 检查 PowerShell 版本
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        Write-Error "需要 PowerShell 5.0 或更高版本"
        exit 1
    }
    
    # 检查部署类型要求
    switch ($DeployType) {
        "docker" {
            if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
                Write-Error "Docker 未安装"
                Write-Info "请先安装 Docker Desktop: https://docs.docker.com/desktop/windows/"
                exit 1
            }
            
            if (!(Get-Command docker-compose -ErrorAction SilentlyContinue)) {
                Write-Error "Docker Compose 未安装"
                exit 1
            }
        }
        "local" {
            if (!(Get-Command python -ErrorAction SilentlyContinue)) {
                Write-Error "Python 未安装"
                Write-Info "请先安装 Python: https://www.python.org/downloads/"
                exit 1
            }
        }
    }
    
    Write-Success "系统要求检查通过"
}

# 备份现有部署
function Backup-Existing {
    if ($NoBackup) {
        return
    }
    
    Write-Step "备份现有部署..."
    
    $backupDir = "backups\pre_deploy_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    
    # 备份配置文件
    if (Test-Path "config.yaml") {
        Copy-Item "config.yaml" $backupDir
        Write-Info "已备份配置文件"
    }
    
    # 备份数据目录
    if (Test-Path "data") {
        Copy-Item "data" $backupDir -Recurse
        Write-Info "已备份数据目录"
    }
    
    Write-Success "备份完成: $backupDir"
}

# 准备配置文件
function Initialize-Config {
    Write-Step "准备配置文件..."
    
    $configTemplate = "config.example.yaml"
    $configFile = if ($ConfigFile) { $ConfigFile } else { "config.yaml" }
    
    if (!(Test-Path $configFile)) {
        if (Test-Path $configTemplate) {
            Copy-Item $configTemplate $configFile
            Write-Info "已创建配置文件: $configFile"
        } else {
            Write-Error "配置模板文件不存在: $configTemplate"
            exit 1
        }
    }
    
    # 根据环境调整配置
    $content = Get-Content $configFile -Raw
    
    switch ($Environment) {
        "dev" {
            $content = $content -replace 'debug: false', 'debug: true'
            $content = $content -replace 'level: "INFO"', 'level: "DEBUG"'
        }
        "staging" {
            $content = $content -replace 'server_name: "AutoMem"', 'server_name: "AutoMem-Staging"'
        }
        "production" {
            $content = $content -replace 'debug: true', 'debug: false'
            $content = $content -replace 'level: "DEBUG"', 'level: "INFO"'
        }
    }
    
    # 设置数据目录
    if ($DataDir) {
        $content = $content -replace 'data_dir: "./data"', "data_dir: `"$DataDir`""
    }
    
    Set-Content $configFile $content
    
    Write-Success "配置文件准备完成"
}

# Docker 部署
function Deploy-Docker {
    Write-Step "执行 Docker 部署..."
    
    # 构建镜像
    Write-Info "构建 Docker 镜像..."
    docker build -t automem:latest .
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker 镜像构建失败"
        exit 1
    }
    
    # 准备 docker-compose 文件
    $composeFile = "docker-compose.yml"
    if ($Environment -eq "production") {
        $composeFile = "docker\docker-compose.prod.yml"
    }
    
    # 启动服务
    Write-Info "启动 Docker 服务..."
    if ($Monitoring) {
        docker-compose -f $composeFile --profile monitoring up -d
    } else {
        docker-compose -f $composeFile up -d
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker 服务启动失败"
        exit 1
    }
    
    # 等待服务启动
    Write-Info "等待服务启动..."
    Start-Sleep 10
    
    # 验证部署
    $status = docker-compose ps
    if ($status -match "Up") {
        Write-Success "Docker 部署成功"
        
        # 显示服务信息
        Write-Host ""
        Write-Info "服务状态:"
        docker-compose ps
        
        Write-Host ""
        Write-Info "访问地址:"
        Write-Host "  - AutoMem 服务: http://localhost:$Port"
        if ($Monitoring) {
            Write-Host "  - Grafana 监控: http://localhost:3000 (admin/admin)"
            Write-Host "  - Prometheus: http://localhost:9090"
        }
    } else {
        Write-Error "Docker 部署失败"
        docker-compose logs
        exit 1
    }
}

# 本地部署
function Deploy-Local {
    Write-Step "执行本地部署..."
    
    # 创建虚拟环境
    if (!(Test-Path "venv")) {
        python -m venv venv
        Write-Info "已创建虚拟环境"
    }
    
    # 激活虚拟环境并安装依赖
    & "venv\Scripts\Activate.ps1"
    pip install -r requirements.txt
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "依赖安装失败"
        exit 1
    }
    
    # 创建启动脚本
    $startScript = @"
@echo off
cd /d "%~dp0"
call venv\Scripts\activate.bat
python -m automem.cli serve --config config.yaml
"@
    
    Set-Content "start_automem.bat" $startScript
    
    Write-Success "本地部署完成"
    
    if (!$NoAutoStart) {
        Write-Info "启动 AutoMem 服务..."
        Start-Process "start_automem.bat" -WindowStyle Minimized
        Start-Sleep 5
        
        # 检查服务是否启动
        $process = Get-Process | Where-Object { $_.ProcessName -like "*python*" -and $_.CommandLine -like "*automem.cli*" }
        if ($process) {
            Write-Success "服务启动成功"
            Write-Host "  - AutoMem 服务: http://localhost:$Port"
        } else {
            Write-Error "服务启动失败"
            exit 1
        }
    } else {
        Write-Info "使用以下命令启动服务:"
        Write-Host "  .\start_automem.bat"
    }
}

# 部署后验证
function Test-Deployment {
    Write-Step "执行部署后验证..."
    
    # 等待服务完全启动
    Start-Sleep 5
    
    # 健康检查
    $healthUrl = "http://localhost:$Port/health"
    $maxAttempts = 30
    $attempt = 1
    
    while ($attempt -le $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri $healthUrl -UseBasicParsing -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                Write-Success "健康检查通过"
                break
            }
        } catch {
            # 继续尝试
        }
        
        Write-Info "等待服务启动... ($attempt/$maxAttempts)"
        Start-Sleep 2
        $attempt++
    }
    
    if ($attempt -gt $maxAttempts) {
        Write-Warning "健康检查超时，请手动验证服务状态"
    }
    
    # 显示部署摘要
    Write-Host ""
    Write-Success "🎉 AutoMem 部署完成！"
    Write-Host ""
    Write-Host "部署信息:"
    Write-Host "  - 部署类型: $DeployType"
    Write-Host "  - 环境: $Environment"
    Write-Host "  - 监控: $(if ($Monitoring) { '已启用' } else { '未启用' })"
    Write-Host "  - 配置文件: $(if ($ConfigFile) { $ConfigFile } else { 'config.yaml' })"
    Write-Host "  - 数据目录: $(if ($DataDir) { $DataDir } else { '.\data' })"
    Write-Host ""
    Write-Host "下一步:"
    Write-Host "  1. 查看使用指南: docs\usage.md"
    Write-Host "  2. 配置 MCP 客户端"
    Write-Host "  3. 开始使用 AutoMem！"
    Write-Host ""
}

# 清理函数
function Remove-Deployment {
    if ($Clean) {
        Write-Step "清理现有部署..."
        
        switch ($DeployType) {
            "docker" {
                docker-compose down -v 2>$null
                docker rmi automem:latest 2>$null
            }
            "local" {
                Get-Process | Where-Object { $_.ProcessName -like "*python*" -and $_.CommandLine -like "*automem.cli*" } | Stop-Process -Force
            }
        }
        
        Write-Success "清理完成"
    }
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Show-Banner
    
    try {
        Test-Requirements
        Remove-Deployment
        Backup-Existing
        Initialize-Config
        
        # 根据部署类型执行部署
        switch ($DeployType) {
            "docker" {
                Deploy-Docker
            }
            "local" {
                Deploy-Local
            }
            default {
                Write-Error "不支持的部署类型: $DeployType"
                exit 1
            }
        }
        
        Test-Deployment
    }
    catch {
        Write-Error "部署过程中发生错误: $($_.Exception.Message)"
        exit 1
    }
}

# 运行主函数
Main
