#!/bin/bash

# AutoMem 备份脚本
# 支持本地备份和远程备份

set -e

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DATA_DIR="${PROJECT_DIR}/data"
BACKUP_DIR="${PROJECT_DIR}/backups"
CONFIG_FILE="${PROJECT_DIR}/config.yaml"

# 默认配置
BACKUP_TYPE="full"
RETENTION_DAYS=30
COMPRESS=true
REMOTE_BACKUP=false
REMOTE_HOST=""
REMOTE_PATH=""
REMOTE_USER=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助
show_help() {
    cat << EOF
AutoMem 备份脚本

用法: $0 [选项]

选项:
  -t, --type TYPE           备份类型 (full|data|config) [默认: full]
  -d, --dir DIR            备份目录 [默认: ./backups]
  -r, --retention DAYS     保留天数 [默认: 30]
  -c, --compress           压缩备份文件 [默认: true]
  --remote-host HOST       远程主机
  --remote-path PATH       远程路径
  --remote-user USER       远程用户
  --no-compress            不压缩备份文件
  -h, --help               显示帮助信息

备份类型:
  full     - 完整备份 (数据 + 配置)
  data     - 仅备份数据
  config   - 仅备份配置

示例:
  $0                                    # 完整备份
  $0 -t data                           # 仅备份数据
  $0 -d /backup/automem               # 指定备份目录
  $0 --remote-host backup.example.com # 远程备份

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                BACKUP_TYPE="$2"
                shift 2
                ;;
            -d|--dir)
                BACKUP_DIR="$2"
                shift 2
                ;;
            -r|--retention)
                RETENTION_DAYS="$2"
                shift 2
                ;;
            -c|--compress)
                COMPRESS=true
                shift
                ;;
            --no-compress)
                COMPRESS=false
                shift
                ;;
            --remote-host)
                REMOTE_HOST="$2"
                REMOTE_BACKUP=true
                shift 2
                ;;
            --remote-path)
                REMOTE_PATH="$2"
                shift 2
                ;;
            --remote-user)
                REMOTE_USER="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证参数
validate_args() {
    if [[ ! "$BACKUP_TYPE" =~ ^(full|data|config)$ ]]; then
        log_error "无效的备份类型: $BACKUP_TYPE"
        exit 1
    fi
    
    if [[ ! -d "$DATA_DIR" && "$BACKUP_TYPE" != "config" ]]; then
        log_error "数据目录不存在: $DATA_DIR"
        exit 1
    fi
    
    if [[ ! -f "$CONFIG_FILE" && "$BACKUP_TYPE" != "data" ]]; then
        log_warning "配置文件不存在: $CONFIG_FILE"
    fi
    
    if [[ "$REMOTE_BACKUP" == true && -z "$REMOTE_HOST" ]]; then
        log_error "远程备份需要指定主机"
        exit 1
    fi
}

# 创建备份目录
create_backup_dir() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_name="automem_${BACKUP_TYPE}_${timestamp}"
    
    BACKUP_PATH="${BACKUP_DIR}/${backup_name}"
    
    mkdir -p "$BACKUP_PATH"
    log_info "创建备份目录: $BACKUP_PATH"
}

# 备份数据
backup_data() {
    if [[ "$BACKUP_TYPE" == "config" ]]; then
        return
    fi
    
    log_info "备份数据文件..."
    
    if [[ -d "$DATA_DIR" ]]; then
        cp -r "$DATA_DIR" "$BACKUP_PATH/"
        log_success "数据备份完成"
    else
        log_warning "数据目录不存在，跳过数据备份"
    fi
}

# 备份配置
backup_config() {
    if [[ "$BACKUP_TYPE" == "data" ]]; then
        return
    fi
    
    log_info "备份配置文件..."
    
    # 备份主配置文件
    if [[ -f "$CONFIG_FILE" ]]; then
        cp "$CONFIG_FILE" "$BACKUP_PATH/"
        log_success "配置文件备份完成"
    fi
    
    # 备份其他配置文件
    for config in "${PROJECT_DIR}"/*.yaml "${PROJECT_DIR}"/*.yml "${PROJECT_DIR}"/*.json; do
        if [[ -f "$config" && "$config" != "$CONFIG_FILE" ]]; then
            cp "$config" "$BACKUP_PATH/"
        fi
    done
}

# 创建备份元数据
create_metadata() {
    log_info "创建备份元数据..."
    
    cat > "$BACKUP_PATH/backup_info.json" << EOF
{
    "backup_type": "$BACKUP_TYPE",
    "timestamp": "$(date -Iseconds)",
    "hostname": "$(hostname)",
    "user": "$(whoami)",
    "data_dir": "$DATA_DIR",
    "config_file": "$CONFIG_FILE",
    "compressed": $COMPRESS,
    "remote_backup": $REMOTE_BACKUP
}
EOF
    
    # 创建文件清单
    find "$BACKUP_PATH" -type f -exec ls -la {} \; > "$BACKUP_PATH/file_list.txt"
    
    log_success "备份元数据创建完成"
}

# 压缩备份
compress_backup() {
    if [[ "$COMPRESS" != true ]]; then
        return
    fi
    
    log_info "压缩备份文件..."
    
    local backup_name=$(basename "$BACKUP_PATH")
    local compressed_file="${BACKUP_DIR}/${backup_name}.tar.gz"
    
    cd "$BACKUP_DIR"
    tar -czf "$compressed_file" "$backup_name"
    
    # 删除原始目录
    rm -rf "$BACKUP_PATH"
    BACKUP_PATH="$compressed_file"
    
    log_success "备份压缩完成: $compressed_file"
}

# 远程备份
remote_backup() {
    if [[ "$REMOTE_BACKUP" != true ]]; then
        return
    fi
    
    log_info "上传到远程服务器..."
    
    local remote_target="${REMOTE_USER:+$REMOTE_USER@}$REMOTE_HOST:${REMOTE_PATH:-/backup/automem/}"
    
    if command -v rsync &> /dev/null; then
        rsync -avz "$BACKUP_PATH" "$remote_target"
    else
        scp -r "$BACKUP_PATH" "$remote_target"
    fi
    
    log_success "远程备份完成"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的备份..."
    
    local count=0
    while IFS= read -r -d '' file; do
        rm -rf "$file"
        ((count++))
    done < <(find "$BACKUP_DIR" -maxdepth 1 -type f -name "automem_*" -mtime +$RETENTION_DAYS -print0)
    
    if [[ $count -gt 0 ]]; then
        log_success "清理了 $count 个旧备份文件"
    else
        log_info "没有需要清理的旧备份"
    fi
}

# 验证备份
verify_backup() {
    log_info "验证备份完整性..."
    
    if [[ "$COMPRESS" == true ]]; then
        # 验证压缩文件
        if tar -tzf "$BACKUP_PATH" >/dev/null 2>&1; then
            log_success "备份文件完整性验证通过"
        else
            log_error "备份文件损坏"
            exit 1
        fi
    else
        # 验证目录
        if [[ -d "$BACKUP_PATH" ]]; then
            log_success "备份目录验证通过"
        else
            log_error "备份目录不存在"
            exit 1
        fi
    fi
}

# 主函数
main() {
    echo "🗄️ AutoMem 备份工具"
    echo "==================="
    echo
    
    parse_args "$@"
    validate_args
    
    log_info "开始 $BACKUP_TYPE 备份..."
    
    create_backup_dir
    backup_data
    backup_config
    create_metadata
    compress_backup
    verify_backup
    remote_backup
    cleanup_old_backups
    
    log_success "备份完成: $BACKUP_PATH"
    
    # 显示备份信息
    if [[ "$COMPRESS" == true ]]; then
        local size=$(du -h "$BACKUP_PATH" | cut -f1)
        log_info "备份大小: $size"
    fi
}

# 错误处理
trap 'log_error "备份过程中发生错误"; exit 1' ERR

# 运行主函数
main "$@"
