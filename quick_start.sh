#!/bin/bash

# AutoMem 快速开始脚本
# 一键安装和启动 AutoMem

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}"
cat << 'EOF'
    ___        __       __  __                 
   /   | __  __/ /_____/  |/  /__  ____ ___    
  / /| |/ / / / __/ __ \  /|_/ / _ \/ __ `__ \   
 / ___ / /_/ / /_/ /_/ / /  / /  __/ / / / / /   
/_/  |_\__,_/\__/\____/_/  /_/\___/_/ /_/ /_/    
                                                 
    AutoMem 智能记忆管理系统
    快速开始 - 5分钟上手指南
    
EOF
echo -e "${NC}"

echo -e "${GREEN}🚀 欢迎使用 AutoMem！${NC}"
echo "这个脚本将帮助您快速安装和启动 AutoMem 智能记忆管理系统。"
echo

# 检查系统
echo -e "${BLUE}📋 检查系统要求...${NC}"

# 检查 Python
if ! command -v python3 &> /dev/null; then
    echo -e "${YELLOW}⚠️  Python 3 未安装，请先安装 Python 3.8+${NC}"
    exit 1
fi

python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "✅ Python 版本: $python_version"

# 检查 pip
if ! command -v pip3 &> /dev/null; then
    echo -e "${YELLOW}⚠️  pip3 未安装${NC}"
    exit 1
fi
echo "✅ pip3 已安装"

# 选择安装方式
echo
echo -e "${BLUE}🔧 选择安装方式:${NC}"
echo "1) 自动安装 (推荐)"
echo "2) Docker 安装"
echo "3) 手动安装"
echo

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo -e "${GREEN}🎯 执行自动安装...${NC}"
        if [[ -f "scripts/install.sh" ]]; then
            bash scripts/install.sh
        else
            echo "下载并执行安装脚本..."
            curl -sSL https://raw.githubusercontent.com/your-org/automem/main/scripts/install.sh | bash
        fi
        ;;
    2)
        echo -e "${GREEN}🐳 执行 Docker 安装...${NC}"
        if ! command -v docker &> /dev/null; then
            echo -e "${YELLOW}⚠️  Docker 未安装，请先安装 Docker${NC}"
            exit 1
        fi
        
        if [[ -f "docker-compose.yml" ]]; then
            docker-compose up -d
        else
            echo "克隆仓库并启动..."
            git clone https://github.com/your-org/automem.git
            cd automem
            docker-compose up -d
        fi
        ;;
    3)
        echo -e "${GREEN}🔨 执行手动安装...${NC}"
        
        # 创建虚拟环境
        echo "创建 Python 虚拟环境..."
        python3 -m venv venv
        source venv/bin/activate
        
        # 安装依赖
        echo "安装依赖包..."
        pip install -r requirements.txt
        
        # 初始化配置
        echo "初始化配置..."
        if [[ -f "config.example.yaml" ]]; then
            cp config.example.yaml config.yaml
        fi
        
        # 创建数据目录
        mkdir -p data
        
        echo "✅ 手动安装完成"
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo
echo -e "${GREEN}🎉 安装完成！${NC}"
echo

# 启动服务
echo -e "${BLUE}🚀 启动 AutoMem 服务...${NC}"

if [[ $choice == "2" ]]; then
    echo "Docker 服务已在后台运行"
    echo "访问地址: http://localhost:8000"
else
    echo "启动 MCP 服务器..."
    if [[ -f "automem" ]]; then
        ./automem serve --stdio &
    elif [[ -f "venv/bin/python" ]]; then
        venv/bin/python -m automem.cli serve --stdio &
    else
        python3 -m automem.cli serve --stdio &
    fi
    
    sleep 3
    echo "✅ 服务已启动"
fi

echo
echo -e "${GREEN}🎯 快速配置 Claude Desktop:${NC}"
echo
echo "1. 打开 Claude Desktop 配置文件:"
echo "   macOS: ~/Library/Application Support/Claude/claude_desktop_config.json"
echo "   Windows: %APPDATA%\\Claude\\claude_desktop_config.json"
echo
echo "2. 添加以下配置:"
cat << 'EOF'
{
  "mcpServers": {
    "automem": {
      "command": "/path/to/automem/automem",
      "args": ["serve", "--stdio"]
    }
  }
}
EOF
echo
echo "3. 重启 Claude Desktop"
echo
echo "4. 在 Claude 中测试:"
echo '   "请帮我存储一个记忆：今天学习了 AutoMem 的使用方法"'
echo

echo -e "${GREEN}📚 更多资源:${NC}"
echo "• 完整文档: docs/README.md"
echo "• 使用指南: docs/usage.md"
echo "• 配置指南: docs/configuration.md"
echo "• 问题反馈: https://github.com/your-org/automem/issues"
echo

echo -e "${GREEN}✨ 开始使用 AutoMem，让 AI 拥有持久记忆！${NC}"
