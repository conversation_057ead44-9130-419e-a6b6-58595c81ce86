apiVersion: apps/v1
kind: Deployment
metadata:
  name: automem-server
  namespace: automem
  labels:
    app: automem
    component: server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: automem
      component: server
  template:
    metadata:
      labels:
        app: automem
        component: server
    spec:
      containers:
      - name: automem
        image: automem:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: AUTOMEM_DATA_DIR
          value: "/data"
        - name: AUTOMEM_LOG_LEVEL
          value: "INFO"
        volumeMounts:
        - name: data
          mountPath: /data
        - name: config
          mountPath: /app/config.yaml
          subPath: config.yaml
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: automem-data
      - name: config
        configMap:
          name: automem-config

---
apiVersion: v1
kind: Service
metadata:
  name: automem-service
  namespace: automem
  labels:
    app: automem
    component: server
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: automem
    component: server

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: automem-data
  namespace: automem
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: automem-config
  namespace: automem
data:
  config.yaml: |
    server_name: "AutoMem"
    server_version: "0.1.0"
    debug: false
    
    storage:
      data_dir: "/data"
      max_memories: 100000
      backup_enabled: true
      backup_interval_hours: 24
    
    intelligence:
      embedding_model: "all-MiniLM-L6-v2"
      classification_threshold: 0.7
      importance_threshold: 0.5
      max_tags_per_memory: 10
      language_detection: true
    
    retrieval:
      similarity_threshold: 0.6
      max_results: 20
      context_window_size: 10
      time_decay_factor: 0.1
    
    logging:
      level: "INFO"
      enable_console: true
      enable_file: true
      file_path: "/data/logs/automem.log"
      max_file_size: "10MB"
      backup_count: 5

---
apiVersion: v1
kind: Namespace
metadata:
  name: automem
  labels:
    name: automem
