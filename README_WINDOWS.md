# AutoMem Windows 用户指南

🎯 **专为 Windows 用户设计的 AutoMem 安装和使用指南**

AutoMem 是一个智能记忆管理系统，基于 MCP (Model Context Protocol) 构建，让 AI 助手拥有持久记忆能力。

## 🚀 快速开始（5分钟上手）

### 方法一：一键安装（推荐）

1. **下载快速开始脚本**
   ```cmd
   # 在命令提示符中运行
   curl -o quick_start.bat https://raw.githubusercontent.com/your-org/automem/main/quick_start.bat
   quick_start.bat
   ```

2. **或者直接运行**（如果已下载项目）
   ```cmd
   quick_start.bat
   ```

### 方法二：PowerShell 自动部署

```powershell
# 以管理员身份运行 PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 运行自动部署脚本
.\scripts\auto_deploy.ps1
```

### 方法三：手动安装

```cmd
# 1. 克隆或下载项目
git clone https://github.com/your-org/automem.git
cd automem

# 2. 运行 Windows 启动脚本
start_automem_windows.bat
```

## 📋 系统要求

- **操作系统**: Windows 10/11 或 Windows Server 2019+
- **Python**: 3.8 或更高版本 ([下载地址](https://www.python.org/downloads/))
- **内存**: 至少 4GB RAM（推荐 8GB+）
- **存储**: 至少 2GB 可用空间
- **网络**: 互联网连接（用于下载依赖）

## 🎮 使用方法

### 启动服务

```cmd
# 方法一：使用专用启动脚本（推荐）
start_automem_windows.bat

# 方法二：手动启动
venv\Scripts\activate
python -m automem.cli serve --stdio

# 方法三：指定配置文件
python -m automem.cli serve --config config.yaml --stdio
```

### 配置 Claude Desktop

1. **找到配置文件**
   ```
   %APPDATA%\Claude\claude_desktop_config.json
   ```

2. **添加 AutoMem 配置**
   ```json
   {
     "mcpServers": {
       "automem": {
         "command": "C:\\path\\to\\automem\\venv\\Scripts\\python.exe",
         "args": ["-m", "automem.cli", "serve", "--stdio"],
         "env": {
           "AUTOMEM_CONFIG": "C:\\path\\to\\automem\\config.yaml"
         }
       }
     }
   }
   ```

3. **重启 Claude Desktop**

### 测试安装

在 Claude 中输入：
```
请帮我存储一个记忆：今天成功在Windows上安装了AutoMem智能记忆系统
```

## 🔧 配置说明

### 基本配置 (config.yaml)

```yaml
# 服务器配置
server_name: "AutoMem-Windows"
debug: false

# 存储配置
storage:
  data_dir: "data"
  max_memories: 50000

# 智能处理
intelligence:
  embedding_model: "all-MiniLM-L6-v2"
  auto_classification: true
  auto_tag_enabled: true

# 日志配置
logging:
  level: "INFO"
  enable_console: true
  enable_file: true
  file_path: "data/logs/automem.log"
```

### Windows 特定优化

```yaml
# Windows 路径配置
storage:
  data_dir: "C:\\AutoMem\\data"
  
# 性能优化
intelligence:
  embedding_model: "all-MiniLM-L6-v2"  # 轻量级模型
  
# 日志配置
logging:
  file_path: "C:\\AutoMem\\logs\\automem.log"
  max_file_size: "50MB"
```

## 🛠️ 管理工具

### 命令行工具

```cmd
# 查看帮助
python -m automem.cli --help

# 检查状态
python -m automem.cli status

# 查看配置
python -m automem.cli config --show

# 运行测试
python -m automem.cli test

# 备份数据
python -m automem.cli backup --output backup.zip

# 清理数据
python -m automem.cli cleanup --days 30 --dry-run
```

### Windows 服务安装

```powershell
# 以管理员身份运行 PowerShell
.\install_service.ps1

# 管理服务
sc start AutoMem
sc stop AutoMem
sc query AutoMem
```

## 🔍 故障排除

### 常见问题

1. **Python 未找到**
   ```
   解决方案: 
   - 重新安装 Python 并勾选 "Add Python to PATH"
   - 或使用完整路径: C:\Python39\python.exe
   ```

2. **依赖安装失败**
   ```cmd
   # 使用国内镜像
   pip install -r requirements-windows.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

3. **权限错误**
   ```cmd
   # 以管理员身份运行命令提示符
   # 或使用用户安装
   pip install --user -r requirements-windows.txt
   ```

4. **端口被占用**
   ```cmd
   # 查看端口占用
   netstat -ano | findstr :8000
   
   # 结束占用进程
   taskkill /PID <进程ID> /F
   ```

### 日志查看

```cmd
# 查看应用日志
type data\logs\automem.log

# 实时监控日志
powershell Get-Content data\logs\automem.log -Wait -Tail 50
```

### 性能监控

```cmd
# 查看 AutoMem 进程
tasklist | findstr python

# 查看资源使用
wmic process where "name='python.exe'" get ProcessId,PageFileUsage,WorkingSetSize
```

## 📊 性能优化

### 内存优化

```yaml
# config.yaml
storage:
  max_memories: 30000  # 减少最大记忆数

intelligence:
  embedding_model: "all-MiniLM-L6-v2"  # 使用轻量模型
```

### 存储优化

```yaml
storage:
  sqlite_pragma:
    journal_mode: "WAL"
    synchronous: "NORMAL"
    cache_size: 5000
```

### 日志优化

```yaml
logging:
  level: "WARNING"  # 减少日志输出
  max_file_size: "20MB"
  backup_count: 3
```

## 🔄 更新和维护

### 更新 AutoMem

```cmd
# 1. 停止服务 (Ctrl+C 或关闭窗口)

# 2. 更新代码
git pull origin main

# 3. 更新依赖
venv\Scripts\activate
pip install -r requirements-windows.txt --upgrade

# 4. 重启服务
start_automem_windows.bat
```

### 备份数据

```cmd
# 手动备份
xcopy data backup\data_%date% /E /I

# 使用内置工具
python -m automem.cli backup --output backup\automem_%date%.zip
```

### 清理维护

```cmd
# 清理旧日志
del data\logs\*.log.1
del data\logs\*.log.2

# 清理缓存
rmdir /s venv\Lib\site-packages\__pycache__

# 重建虚拟环境（如果需要）
rmdir /s venv
python -m venv venv
venv\Scripts\activate
pip install -r requirements-windows.txt
```

## 🎯 高级功能

### 自定义配置

```yaml
# 高级配置示例
intelligence:
  # 自定义分类规则
  classification_rules:
    - pattern: "会议|讨论"
      category: "工作"
    - pattern: "学习|教程"
      category: "学习"
      
  # 自定义标签规则
  tag_rules:
    - pattern: "Python|编程"
      tags: ["技术", "编程"]
```

### 插件扩展

```python
# 自定义插件示例 (plugins/my_plugin.py)
from automem.plugins import BasePlugin

class MyPlugin(BasePlugin):
    def process_memory(self, memory):
        # 自定义处理逻辑
        return memory
```

## 📞 获取帮助

### 在线资源

- **完整文档**: [docs/README.md](docs/README.md)
- **Windows 安装指南**: [docs/windows_installation.md](docs/windows_installation.md)
- **使用指南**: [docs/usage.md](docs/usage.md)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/automem/issues)

### 社区支持

- **GitHub 讨论**: [GitHub Discussions](https://github.com/your-org/automem/discussions)
- **QQ 群**: 123456789
- **微信群**: 扫描二维码加入

### 技术支持

- **邮箱**: <EMAIL>
- **在线文档**: https://automem.readthedocs.io/

## 🎉 开始使用

恭喜！您已成功在 Windows 上安装了 AutoMem。

**下一步**:
1. ✅ 配置 Claude Desktop 或其他 MCP 客户端
2. ✅ 阅读 [使用指南](docs/usage.md) 了解更多功能
3. ✅ 开始享受智能记忆功能

**测试命令**:
```
在 Claude 中输入: "请帮我存储一个记忆：AutoMem在Windows上运行完美！"
```

享受您的智能记忆助手！🧠✨

---

**AutoMem** - 让 AI 拥有真正的记忆力 | Windows 专用版
