"""
决策引擎

智能决定信息的存储价值、重要性级别和处理策略。
"""

import re
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Set, Any, Optional, Tuple
from enum import Enum

from ..config.settings import IntelligenceConfig
from ..utils.logging import LoggerMixin
from ..core.memory import Memory, MemoryType, MemoryImportance


class DecisionType(str, Enum):
    """决策类型"""
    STORE = "store"          # 存储
    IGNORE = "ignore"        # 忽略
    MERGE = "merge"          # 合并
    UPDATE = "update"        # 更新
    ARCHIVE = "archive"      # 归档


class DecisionResult:
    """决策结果"""
    
    def __init__(
        self,
        decision: DecisionType,
        confidence: float,
        importance: MemoryImportance,
        memory_type: MemoryType,
        reasoning: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.decision = decision
        self.confidence = confidence
        self.importance = importance
        self.memory_type = memory_type
        self.reasoning = reasoning
        self.metadata = metadata or {}


class DecisionEngine(LoggerMixin):
    """决策引擎"""
    
    def __init__(self, config: IntelligenceConfig):
        self.config = config
        
        # 决策规则
        self.decision_rules = self._initialize_decision_rules()
        
        # 决策统计
        self.decision_stats: Dict[str, int] = {
            "store": 0,
            "ignore": 0,
            "merge": 0,
            "update": 0,
            "archive": 0,
        }
        
        # 学习到的模式
        self.learned_patterns: Dict[str, List[str]] = {}
    
    def _initialize_decision_rules(self) -> Dict[str, Dict[str, Any]]:
        """初始化决策规则"""
        return {
            # 高价值内容规则
            "high_value": {
                "patterns": [
                    r"重要|关键|核心|critical|important|key",
                    r"决定|决策|选择|decision|choice",
                    r"解决方案|方法|solution|method",
                    r"经验|教训|lesson|experience",
                    r"总结|结论|summary|conclusion",
                ],
                "keywords": [
                    "项目", "任务", "工作", "学习", "技能", "知识",
                    "project", "task", "work", "learning", "skill", "knowledge"
                ],
                "importance": MemoryImportance.HIGH,
                "store_probability": 0.9,
            },
            
            # 中等价值内容规则
            "medium_value": {
                "patterns": [
                    r"想法|思考|idea|thought",
                    r"计划|安排|plan|schedule",
                    r"讨论|交流|discussion|communication",
                    r"信息|资料|information|data",
                ],
                "keywords": [
                    "会议", "沟通", "协作", "分享", "建议",
                    "meeting", "communication", "collaboration", "share", "suggestion"
                ],
                "importance": MemoryImportance.MEDIUM,
                "store_probability": 0.7,
            },
            
            # 低价值内容规则
            "low_value": {
                "patterns": [
                    r"闲聊|随便|casual|chat",
                    r"测试|test",
                    r"hello|hi|你好|再见|goodbye",
                ],
                "keywords": [
                    "天气", "吃饭", "休息", "无聊",
                    "weather", "food", "rest", "boring"
                ],
                "importance": MemoryImportance.LOW,
                "store_probability": 0.3,
            },
            
            # 应该忽略的内容
            "ignore": {
                "patterns": [
                    r"^.{1,3}$",  # 太短的内容
                    r"^\s*$",     # 空白内容
                    r"^[.!?]+$",  # 只有标点符号
                ],
                "keywords": [
                    "spam", "垃圾", "广告", "advertisement"
                ],
                "importance": MemoryImportance.MINIMAL,
                "store_probability": 0.1,
            },
        }
    
    async def make_decision(
        self, 
        content: str, 
        context: Optional[Dict[str, Any]] = None,
        existing_memories: Optional[List[Memory]] = None
    ) -> DecisionResult:
        """做出存储决策"""
        
        # 预处理内容
        processed_content = self._preprocess_content(content)
        
        # 计算基础得分
        base_score = await self._calculate_base_score(processed_content)
        
        # 分析内容类型
        memory_type = await self._determine_memory_type(processed_content, context)
        
        # 计算重要性
        importance = await self._determine_importance(processed_content, base_score, context)
        
        # 检查是否需要合并或更新
        merge_candidate = await self._check_merge_opportunity(
            processed_content, existing_memories
        )
        
        # 做出最终决策
        decision, confidence, reasoning = await self._make_final_decision(
            processed_content, base_score, importance, merge_candidate, context
        )
        
        # 更新统计
        self.decision_stats[decision.value] += 1
        
        # 创建决策结果
        result = DecisionResult(
            decision=decision,
            confidence=confidence,
            importance=importance,
            memory_type=memory_type,
            reasoning=reasoning,
            metadata={
                "base_score": base_score,
                "content_length": len(content),
                "processed_length": len(processed_content),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )
        
        self.logger.debug(f"决策结果: {decision.value} (置信度: {confidence:.2f})")
        return result
    
    def _preprocess_content(self, content: str) -> str:
        """预处理内容"""
        # 去除多余空白
        processed = re.sub(r'\s+', ' ', content.strip())
        
        # 去除特殊字符（保留基本标点）
        processed = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()[\]{}"\'-]', '', processed)
        
        return processed
    
    async def _calculate_base_score(self, content: str) -> float:
        """计算基础得分"""
        score = 0.0
        content_lower = content.lower()
        
        # 遍历所有规则计算得分
        for rule_type, rules in self.decision_rules.items():
            rule_score = 0.0
            
            # 模式匹配得分
            patterns = rules.get("patterns", [])
            for pattern in patterns:
                if re.search(pattern, content_lower, re.IGNORECASE):
                    rule_score += 1.0
            
            # 关键词匹配得分
            keywords = rules.get("keywords", [])
            for keyword in keywords:
                if keyword.lower() in content_lower:
                    rule_score += 0.5
            
            # 应用规则权重
            if rule_score > 0:
                store_prob = rules.get("store_probability", 0.5)
                weighted_score = rule_score * store_prob
                
                if rule_type == "ignore":
                    score -= weighted_score  # 忽略规则减分
                else:
                    score += weighted_score
        
        # 长度因子
        length_factor = min(len(content) / 100.0, 1.0)  # 长度越长得分越高，最多1分
        score += length_factor * 0.2
        
        # 标准化到0-1范围
        return max(0.0, min(score / 5.0, 1.0))
    
    async def _determine_memory_type(
        self, 
        content: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> MemoryType:
        """确定记忆类型"""
        content_lower = content.lower()
        
        # 类型判断规则
        type_patterns = {
            MemoryType.FACT: [
                r"是|就是|定义|概念|事实|fact|definition|concept",
                r"\d+|数据|统计|data|statistics",
                r"地址|电话|邮箱|网址|address|phone|email|url",
            ],
            MemoryType.PROCEDURE: [
                r"步骤|流程|方法|如何|怎么|step|process|method|how to",
                r"安装|配置|设置|install|configure|setup",
                r"操作|执行|运行|operate|execute|run",
            ],
            MemoryType.EPISODIC: [
                r"今天|昨天|明天|上次|下次|today|yesterday|tomorrow",
                r"会议|见面|聊天|meeting|chat|talk",
                r"经历|体验|感受|experience|feeling",
            ],
            MemoryType.SEMANTIC: [
                r"知识|理论|原理|概念|knowledge|theory|principle|concept",
                r"学习|研究|分析|learning|research|analysis",
                r"理解|认识|了解|understand|realize|know",
            ],
        }
        
        # 计算每种类型的匹配得分
        type_scores = {}
        for memory_type, patterns in type_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, content_lower, re.IGNORECASE):
                    score += 1
            type_scores[memory_type] = score
        
        # 返回得分最高的类型，默认为对话类型
        if type_scores:
            best_type = max(type_scores.items(), key=lambda x: x[1])
            if best_type[1] > 0:
                return best_type[0]
        
        return MemoryType.CONVERSATION
    
    async def _determine_importance(
        self, 
        content: str, 
        base_score: float, 
        context: Optional[Dict[str, Any]] = None
    ) -> MemoryImportance:
        """确定重要性级别"""
        
        # 基于得分的基础重要性
        if base_score >= 0.8:
            base_importance = MemoryImportance.HIGH
        elif base_score >= 0.6:
            base_importance = MemoryImportance.MEDIUM
        elif base_score >= 0.3:
            base_importance = MemoryImportance.LOW
        else:
            base_importance = MemoryImportance.MINIMAL
        
        # 上下文调整
        if context:
            # 如果是紧急或重要的上下文
            if context.get("urgent", False) or context.get("important", False):
                if base_importance.value in ["low", "minimal"]:
                    base_importance = MemoryImportance.MEDIUM
                elif base_importance == MemoryImportance.MEDIUM:
                    base_importance = MemoryImportance.HIGH
            
            # 如果是工作相关
            if context.get("work_related", False):
                if base_importance == MemoryImportance.LOW:
                    base_importance = MemoryImportance.MEDIUM
        
        return base_importance
    
    async def _check_merge_opportunity(
        self, 
        content: str, 
        existing_memories: Optional[List[Memory]] = None
    ) -> Optional[Memory]:
        """检查是否有合并机会"""
        if not existing_memories:
            return None
        
        content_lower = content.lower()
        
        # 寻找相似的记忆
        for memory in existing_memories:
            memory_content = memory.content.lower()
            
            # 简单的相似度检查
            common_words = set(content_lower.split()) & set(memory_content.split())
            if len(common_words) >= 3:  # 至少3个共同词汇
                similarity = len(common_words) / len(set(content_lower.split()) | set(memory_content.split()))
                if similarity >= 0.3:  # 30%相似度
                    return memory
        
        return None
    
    async def _make_final_decision(
        self,
        content: str,
        base_score: float,
        importance: MemoryImportance,
        merge_candidate: Optional[Memory],
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[DecisionType, float, str]:
        """做出最终决策"""
        
        # 如果有合并候选
        if merge_candidate:
            confidence = 0.8
            reasoning = f"发现相似记忆 {merge_candidate.id}，建议合并"
            return DecisionType.MERGE, confidence, reasoning
        
        # 基于得分和重要性决策
        if base_score < 0.2:
            confidence = 0.9
            reasoning = "内容得分过低，建议忽略"
            return DecisionType.IGNORE, confidence, reasoning
        
        if importance in [MemoryImportance.CRITICAL, MemoryImportance.HIGH]:
            confidence = 0.9
            reasoning = f"高重要性内容 ({importance.value})，建议存储"
            return DecisionType.STORE, confidence, reasoning
        
        if importance == MemoryImportance.MEDIUM and base_score >= 0.5:
            confidence = 0.7
            reasoning = "中等重要性且得分较高，建议存储"
            return DecisionType.STORE, confidence, reasoning
        
        if importance == MemoryImportance.LOW and base_score >= 0.4:
            confidence = 0.6
            reasoning = "低重要性但有一定价值，建议存储"
            return DecisionType.STORE, confidence, reasoning
        
        # 默认忽略
        confidence = 0.5
        reasoning = "未达到存储阈值，建议忽略"
        return DecisionType.IGNORE, confidence, reasoning
    
    async def should_store_memory(self, content: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """简化的存储判断接口"""
        decision = await self.make_decision(content, context)
        return decision.decision in [DecisionType.STORE, DecisionType.MERGE, DecisionType.UPDATE]
    
    async def get_decision_stats(self) -> Dict[str, Any]:
        """获取决策统计信息"""
        total_decisions = sum(self.decision_stats.values())
        
        stats = {
            "total_decisions": total_decisions,
            "decision_distribution": dict(self.decision_stats),
            "decision_percentages": {},
        }
        
        if total_decisions > 0:
            for decision, count in self.decision_stats.items():
                stats["decision_percentages"][decision] = (count / total_decisions) * 100
        
        return stats
    
    async def update_decision_threshold(self, threshold: float) -> None:
        """更新决策阈值"""
        self.config.importance_threshold = max(0.0, min(threshold, 1.0))
        self.logger.info(f"决策阈值已更新为: {threshold}")
    
    async def reset_stats(self) -> None:
        """重置统计信息"""
        self.decision_stats = {key: 0 for key in self.decision_stats}
        self.learned_patterns.clear()
        self.logger.info("决策统计信息已重置")
