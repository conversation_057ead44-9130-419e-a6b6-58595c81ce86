# AutoMem 基础依赖

# MCP Framework
mcp[cli]>=1.12.0

# Vector Database & Embeddings
chromadb>=0.4.0
sentence-transformers>=2.2.0

# Data Processing
pydantic>=2.0.0
pydantic-settings>=2.0.0
sqlalchemy>=2.0.0
alembic>=1.12.0
aiosqlite>=0.19.0

# Text Processing
nltk>=3.8.0
jieba>=0.42.0

# Utilities
structlog>=23.0.0
rich>=13.0.0
typer>=0.9.0
python-dateutil>=2.8.0
aiofiles>=23.0.0
click>=8.0.0

# Async & Network
aiohttp>=3.8.0
asyncio-mqtt>=0.13.0

# Monitoring & Metrics
prometheus-client>=0.17.0
psutil>=5.9.0

# Configuration
toml>=0.10.2
python-dotenv>=1.0.0

# Security
cryptography>=41.0.0
bcrypt>=4.0.0

# Language Detection
langdetect>=1.0.9
textstat>=0.7.0

# Time & Timezone
pytz>=2023.3

# Optional: Advanced ML
scikit-learn>=1.3.0
numpy>=1.24.0

# Development dependencies (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
pytest-mock>=3.11.0
httpx>=0.24.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.0.0
pyyaml>=6.0.0

# Documentation (optional)
mkdocs>=1.5.0
mkdocs-material>=9.2.0
mkdocs-mermaid2-plugin>=1.1.0

# Performance Profiling (optional)
memory-profiler>=0.61.0
line-profiler>=4.1.0
py-spy>=0.3.14

# Deployment (optional)
gunicorn>=21.2.0
uvicorn>=0.23.0
docker>=6.1.0

# Data Analysis (optional)
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Advanced ML (optional)
torch>=2.0.0
transformers>=4.30.0
datasets>=2.14.0
