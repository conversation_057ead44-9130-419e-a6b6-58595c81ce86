# AutoMem 部署指南

本指南将帮助您快速部署 AutoMem 智能记忆管理系统，支持多种部署方式，从简单的本地安装到生产级的容器化部署。

## 🚀 一键自动部署

### 方法一：自动安装脚本（推荐）

```bash
# Linux/macOS 一键安装
curl -sSL https://raw.githubusercontent.com/your-org/automem/main/scripts/install.sh | bash

# Windows PowerShell 一键安装
iwr -useb https://raw.githubusercontent.com/your-org/automem/main/scripts/install.ps1 | iex
```

安装脚本会自动：
- 检查系统要求
- 创建 Python 虚拟环境
- 安装所有依赖
- 初始化配置文件
- 创建数据目录
- 设置 CLI 工具
- 运行安装测试

### 方法二：Docker 一键部署

```bash
# 克隆仓库
git clone https://github.com/your-org/automem.git
cd automem

# 一键启动（包含所有服务）
docker-compose up -d

# 验证部署
docker-compose ps
```

## 🐳 Docker 部署（生产推荐）

### 基础 Docker 部署

```bash
# 1. 构建镜像
docker build -t automem:latest .

# 2. 创建数据卷
docker volume create automem_data

# 3. 启动容器
docker run -d \
  --name automem \
  -p 8000:8000 \
  -v automem_data:/data \
  -v $(pwd)/config.yaml:/app/config.yaml:ro \
  --restart unless-stopped \
  automem:latest

# 4. 查看日志
docker logs -f automem
```

### Docker Compose 部署（推荐）

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  automem:
    build: .
    container_name: automem-server
    ports:
      - "8000:8000"
    volumes:
      - automem_data:/data
      - ./config.yaml:/app/config.yaml:ro
    environment:
      - AUTOMEM_DATA_DIR=/data
      - AUTOMEM_LOG_LEVEL=INFO
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  automem_data:
    driver: local
```

启动服务：

```bash
# 启动所有服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f automem

# 停止服务
docker-compose down
```

### 生产环境 Docker 部署

```bash
# 使用生产配置
docker-compose -f docker/docker-compose.prod.yml up -d

# 包含监控服务
docker-compose -f docker/docker-compose.yml --profile monitoring up -d
```

## ☸️ Kubernetes 部署

### 快速部署

```bash
# 1. 创建命名空间
kubectl create namespace automem

# 2. 应用所有配置
kubectl apply -f k8s/

# 3. 查看部署状态
kubectl get pods -n automem

# 4. 查看服务
kubectl get svc -n automem

# 5. 查看日志
kubectl logs -f deployment/automem-server -n automem
```

### 自定义部署

1. **修改配置**：
   ```bash
   # 编辑配置文件
   kubectl edit configmap automem-config -n automem
   ```

2. **扩展副本**：
   ```bash
   # 扩展到3个副本
   kubectl scale deployment automem-server --replicas=3 -n automem
   ```

3. **更新镜像**：
   ```bash
   # 更新到新版本
   kubectl set image deployment/automem-server automem=automem:v0.2.0 -n automem
   ```

## 🖥️ 系统服务部署

### Linux systemd 服务

```bash
# 1. 复制服务文件
sudo cp scripts/automem.service /etc/systemd/system/

# 2. 创建用户和目录
sudo useradd -r -s /bin/false automem
sudo mkdir -p /opt/automem /var/lib/automem /var/log/automem
sudo chown automem:automem /var/lib/automem /var/log/automem

# 3. 安装 AutoMem
sudo cp -r . /opt/automem/
sudo chown -R automem:automem /opt/automem

# 4. 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable automem
sudo systemctl start automem

# 5. 查看状态
sudo systemctl status automem
```

### Windows 服务

```powershell
# 使用 NSSM 创建 Windows 服务
# 1. 下载并安装 NSSM
# 2. 创建服务
nssm install AutoMem "C:\path\to\automem\venv\Scripts\python.exe" "-m automem.cli serve"
nssm set AutoMem AppDirectory "C:\path\to\automem"
nssm start AutoMem
```

## 🔧 配置管理

### 环境变量配置

```bash
# 基本配置
export AUTOMEM_DATA_DIR="/data/automem"
export AUTOMEM_LOG_LEVEL="INFO"
export AUTOMEM_CONFIG_FILE="/etc/automem/config.yaml"

# 高级配置
export AUTOMEM_EMBEDDING_MODEL="all-MiniLM-L6-v2"
export AUTOMEM_MAX_MEMORIES="100000"
export AUTOMEM_BACKUP_ENABLED="true"
```

### 配置文件模板

```yaml
# /etc/automem/config.yaml
server_name: "生产环境AutoMem"
debug: false

storage:
  data_dir: "/var/lib/automem"
  max_memories: 100000
  backup_enabled: true
  backup_interval_hours: 24

intelligence:
  embedding_model: "all-MiniLM-L6-v2"
  auto_classification: true
  auto_tag_enabled: true

logging:
  level: "INFO"
  enable_file: true
  file_path: "/var/log/automem/automem.log"
  max_file_size: "100MB"
  backup_count: 10

monitoring:
  prometheus_enabled: true
  prometheus_port: 9090
  health_check_enabled: true
```

## 📊 监控部署

### Prometheus + Grafana

```bash
# 启动监控栈
docker-compose -f docker/docker-compose.yml --profile monitoring up -d

# 访问服务
# Grafana: http://localhost:3000 (admin/admin)
# Prometheus: http://localhost:9090
```

### 自定义监控

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'automem'
    static_configs:
      - targets: ['automem:8000']
    metrics_path: '/metrics'
```

## 🔒 安全配置

### SSL/TLS 配置

```yaml
# config.yaml
security:
  ssl_enabled: true
  ssl_cert_path: "/etc/ssl/certs/automem.crt"
  ssl_key_path: "/etc/ssl/private/automem.key"
```

### 防火墙配置

```bash
# Ubuntu/Debian
sudo ufw allow 8000/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

## 🚦 健康检查

### 内置健康检查

```bash
# HTTP 健康检查
curl http://localhost:8000/health

# CLI 健康检查
./automem status --detailed
```

### 自定义健康检查脚本

```bash
#!/bin/bash
# health_check.sh

# 检查服务状态
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ AutoMem 服务正常"
    exit 0
else
    echo "❌ AutoMem 服务异常"
    exit 1
fi
```

## 📈 性能优化

### 资源配置

```yaml
# Docker 资源限制
services:
  automem:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
```

### 数据库优化

```yaml
# config.yaml
storage:
  # SQLite 优化
  sqlite_pragma:
    journal_mode: "WAL"
    synchronous: "NORMAL"
    cache_size: 10000
    
  # 向量数据库优化
  vector_db_config:
    batch_size: 1000
    index_type: "HNSW"
```

## 🔄 备份与恢复

### 自动备份

```bash
# 设置定时备份
crontab -e

# 每天凌晨2点备份
0 2 * * * /opt/automem/scripts/backup.sh --compress --retention 30
```

### 手动备份

```bash
# 创建备份
./scripts/backup.sh --type full --compress

# 恢复备份
./scripts/restore.sh backup_20240115_020000.tar.gz
```

## 🔧 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   journalctl -u automem -f
   
   # 检查配置
   ./automem config --validate
   ```

2. **内存使用过高**
   ```bash
   # 检查内存使用
   ./automem status --detailed
   
   # 清理缓存
   ./automem cleanup --dry-run
   ```

3. **搜索性能慢**
   ```bash
   # 重建索引
   ./automem rebuild-index
   
   # 优化数据库
   ./automem optimize
   ```

### 日志分析

```bash
# 查看错误日志
grep ERROR /var/log/automem/automem.log

# 查看性能日志
grep "slow_query" /var/log/automem/automem.log

# 实时监控
tail -f /var/log/automem/automem.log | grep -E "(ERROR|WARNING)"
```

## 📞 获取支持

如果在部署过程中遇到问题：

1. 查看 [故障排除文档](troubleshooting.md)
2. 检查 [GitHub Issues](https://github.com/your-org/automem/issues)
3. 在 [讨论区](https://github.com/your-org/automem/discussions) 提问
4. 联系技术支持：<EMAIL>

---

**恭喜！** 您已成功部署 AutoMem 智能记忆管理系统。现在可以开始使用了！

下一步：查看 [使用指南](usage.md) 了解如何使用系统。
