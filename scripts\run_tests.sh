#!/bin/bash

# AutoMem 测试运行脚本
# 提供不同类型的测试运行选项

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助
show_help() {
    cat << EOF
AutoMem 测试运行脚本

用法: $0 [选项] [测试类型]

测试类型:
  unit         - 运行单元测试
  integration  - 运行集成测试
  e2e          - 运行端到端测试
  all          - 运行所有测试 (默认)
  fast         - 运行快速测试 (排除慢速测试)
  slow         - 仅运行慢速测试
  coverage     - 运行测试并生成覆盖率报告

选项:
  -v, --verbose     详细输出
  -q, --quiet       静默模式
  -f, --fail-fast   遇到失败立即停止
  -x, --exitfirst   第一个失败后退出
  -k EXPRESSION     按表达式过滤测试
  --parallel        并行运行测试
  --no-cov          不生成覆盖率报告
  --html            生成HTML报告
  --junit           生成JUnit XML报告
  -h, --help        显示帮助信息

示例:
  $0                          # 运行所有测试
  $0 unit                     # 仅运行单元测试
  $0 -v integration          # 详细模式运行集成测试
  $0 -k "test_memory"         # 运行包含"test_memory"的测试
  $0 --parallel fast          # 并行运行快速测试

EOF
}

# 默认配置
TEST_TYPE="all"
VERBOSE=""
QUIET=""
FAIL_FAST=""
EXIT_FIRST=""
FILTER=""
PARALLEL=""
NO_COV=""
HTML_REPORT=""
JUNIT_REPORT=""

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--verbose)
                VERBOSE="-v"
                shift
                ;;
            -q|--quiet)
                QUIET="-q"
                shift
                ;;
            -f|--fail-fast)
                FAIL_FAST="--tb=short"
                shift
                ;;
            -x|--exitfirst)
                EXIT_FIRST="-x"
                shift
                ;;
            -k)
                FILTER="-k $2"
                shift 2
                ;;
            --parallel)
                PARALLEL="-n auto"
                shift
                ;;
            --no-cov)
                NO_COV="--no-cov"
                shift
                ;;
            --html)
                HTML_REPORT="--html=reports/test_report.html --self-contained-html"
                shift
                ;;
            --junit)
                JUNIT_REPORT="--junit-xml=reports/junit.xml"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            unit|integration|e2e|all|fast|slow|coverage)
                TEST_TYPE="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查依赖
check_dependencies() {
    log_info "检查测试依赖..."
    
    # 检查pytest
    if ! python -m pytest --version >/dev/null 2>&1; then
        log_error "pytest 未安装"
        log_info "请运行: pip install pytest pytest-asyncio pytest-cov"
        exit 1
    fi
    
    # 检查覆盖率工具
    if [[ -z "$NO_COV" ]] && ! python -c "import coverage" >/dev/null 2>&1; then
        log_warning "coverage 未安装，将跳过覆盖率报告"
        NO_COV="--no-cov"
    fi
    
    # 检查并行测试工具
    if [[ -n "$PARALLEL" ]] && ! python -c "import xdist" >/dev/null 2>&1; then
        log_warning "pytest-xdist 未安装，将使用串行模式"
        PARALLEL=""
    fi
    
    log_success "依赖检查完成"
}

# 准备测试环境
prepare_environment() {
    log_info "准备测试环境..."
    
    # 切换到项目目录
    cd "$PROJECT_DIR"
    
    # 创建报告目录
    mkdir -p reports
    
    # 设置Python路径
    export PYTHONPATH="$PROJECT_DIR/src:$PYTHONPATH"
    
    # 设置测试环境变量
    export AUTOMEM_TEST_MODE=true
    export AUTOMEM_LOG_LEVEL=WARNING
    
    log_success "测试环境准备完成"
}

# 构建pytest命令
build_pytest_command() {
    local cmd="python -m pytest"
    
    # 添加基本选项
    cmd="$cmd $VERBOSE $QUIET $FAIL_FAST $EXIT_FIRST $FILTER $PARALLEL $NO_COV"
    
    # 添加报告选项
    cmd="$cmd $HTML_REPORT $JUNIT_REPORT"
    
    # 根据测试类型添加标记
    case $TEST_TYPE in
        unit)
            cmd="$cmd -m unit"
            ;;
        integration)
            cmd="$cmd -m integration"
            ;;
        e2e)
            cmd="$cmd -m e2e"
            ;;
        fast)
            cmd="$cmd -m 'not slow'"
            ;;
        slow)
            cmd="$cmd -m slow"
            ;;
        coverage)
            cmd="$cmd --cov-report=html --cov-report=term"
            ;;
        all)
            # 运行所有测试，但排除需要特殊环境的测试
            cmd="$cmd -m 'not requires_gpu and not requires_network'"
            ;;
    esac
    
    echo "$cmd"
}

# 运行测试
run_tests() {
    local pytest_cmd=$(build_pytest_command)
    
    log_info "运行测试类型: $TEST_TYPE"
    log_info "执行命令: $pytest_cmd"
    echo
    
    # 执行测试
    if eval "$pytest_cmd"; then
        log_success "测试执行完成"
        return 0
    else
        log_error "测试执行失败"
        return 1
    fi
}

# 生成测试报告
generate_reports() {
    if [[ -n "$HTML_REPORT" && -f "reports/test_report.html" ]]; then
        log_success "HTML测试报告已生成: reports/test_report.html"
    fi
    
    if [[ -n "$JUNIT_REPORT" && -f "reports/junit.xml" ]]; then
        log_success "JUnit XML报告已生成: reports/junit.xml"
    fi
    
    if [[ -z "$NO_COV" && -d "htmlcov" ]]; then
        log_success "覆盖率报告已生成: htmlcov/index.html"
    fi
}

# 清理函数
cleanup() {
    # 清理临时文件
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null || true
}

# 主函数
main() {
    echo "🧪 AutoMem 测试运行器"
    echo "====================="
    echo
    
    parse_args "$@"
    check_dependencies
    prepare_environment
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 运行测试
    if run_tests; then
        generate_reports
        log_success "所有测试完成！"
        exit 0
    else
        log_error "测试失败！"
        exit 1
    fi
}

# 运行主函数
main "$@"
