"""
自主决策系统

整合所有智能组件，实现完全自主的记忆管理。
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Set, Any, Optional, Tuple
from uuid import UUID
from collections import defaultdict

from ..config.settings import IntelligenceConfig, RetrievalConfig, StorageConfig
from ..utils.logging import LoggerMixin
from ..core.memory import Memory, MemoryType, MemoryImportance
from ..core.storage import StorageEngine
from ..core.vector_db import VectorDatabase
from ..core.priority import MemoryPriorityManager, MemoryPriority
from ..core.lifecycle import MemoryLifecycleManager, LifecycleStage
from .classifier import AutoClassifier
from .tagger import TagGenerator
from .decision import DecisionEngine, DecisionType, DecisionResult
from .importance import ImportanceAnalyzer


class AutonomousAction:
    """自主行动"""
    
    def __init__(
        self,
        action_type: str,
        target_memory_id: Optional[UUID],
        parameters: Dict[str, Any],
        confidence: float,
        reasoning: str,
        timestamp: Optional[datetime] = None
    ):
        self.action_type = action_type
        self.target_memory_id = target_memory_id
        self.parameters = parameters
        self.confidence = confidence
        self.reasoning = reasoning
        self.timestamp = timestamp or datetime.now(timezone.utc)


class AutonomousMemorySystem(LoggerMixin):
    """自主记忆系统"""
    
    def __init__(
        self,
        intelligence_config: IntelligenceConfig,
        retrieval_config: RetrievalConfig,
        storage_config: StorageConfig,
        storage: StorageEngine,
        vector_db: VectorDatabase
    ):
        self.intelligence_config = intelligence_config
        self.retrieval_config = retrieval_config
        self.storage_config = storage_config
        self.storage = storage
        self.vector_db = vector_db
        
        # 初始化智能组件
        self.classifier = AutoClassifier(intelligence_config)
        self.tagger = TagGenerator(intelligence_config)
        self.decision_engine = DecisionEngine(intelligence_config)
        self.importance_analyzer = ImportanceAnalyzer(intelligence_config)
        self.priority_manager = MemoryPriorityManager(storage_config, storage)
        self.lifecycle_manager = MemoryLifecycleManager(
            storage_config, storage, self.priority_manager
        )
        
        # 自主决策配置
        self.autonomous_config = {
            "auto_processing_enabled": True,
            "auto_classification_enabled": True,
            "auto_tagging_enabled": True,
            "auto_priority_enabled": True,
            "auto_lifecycle_enabled": True,
            "auto_cleanup_enabled": True,
            "processing_interval_minutes": 30,
            "maintenance_interval_hours": 24,
        }
        
        # 行动历史
        self.action_history: List[AutonomousAction] = []
        
        # 统计信息
        self.autonomous_stats = {
            "total_actions": 0,
            "action_types": defaultdict(int),
            "successful_actions": 0,
            "failed_actions": 0,
        }
    
    async def process_new_memory(
        self,
        content: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Optional[Memory], List[AutonomousAction]]:
        """自主处理新记忆"""
        
        actions = []
        
        try:
            # 1. 决策是否存储
            decision = await self.decision_engine.make_decision(content, context)
            
            if decision.decision == DecisionType.IGNORE:
                action = AutonomousAction(
                    action_type="ignore",
                    target_memory_id=None,
                    parameters={"reason": decision.reasoning},
                    confidence=decision.confidence,
                    reasoning=decision.reasoning
                )
                actions.append(action)
                return False, None, actions
            
            # 2. 创建记忆对象
            memory = Memory(
                content=content,
                memory_type=decision.memory_type,
                importance=decision.importance,
                context=context or {},
            )
            
            # 3. 自动分类
            if self.autonomous_config["auto_classification_enabled"]:
                categories = await self.classifier.classify_memory(memory)
                memory.categories.update(categories)
                
                if categories:
                    action = AutonomousAction(
                        action_type="classify",
                        target_memory_id=memory.id,
                        parameters={"categories": categories},
                        confidence=0.8,
                        reasoning=f"自动分类为: {', '.join(categories)}"
                    )
                    actions.append(action)
            
            # 4. 自动标签
            if self.autonomous_config["auto_tagging_enabled"]:
                tags = await self.tagger.generate_tags(memory)
                memory.tags.update(tags)
                
                if tags:
                    action = AutonomousAction(
                        action_type="tag",
                        target_memory_id=memory.id,
                        parameters={"tags": tags},
                        confidence=0.7,
                        reasoning=f"自动生成标签: {', '.join(tags)}"
                    )
                    actions.append(action)
            
            # 5. 重要性分析
            importance, importance_score, importance_details = await self.importance_analyzer.analyze_importance(
                memory, context
            )
            memory.importance = importance
            memory.relevance_score = importance_score
            
            action = AutonomousAction(
                action_type="analyze_importance",
                target_memory_id=memory.id,
                parameters={
                    "importance": importance.value,
                    "score": importance_score,
                    "details": importance_details
                },
                confidence=0.9,
                reasoning=f"重要性分析: {importance.value} (得分: {importance_score:.2f})"
            )
            actions.append(action)
            
            # 6. 存储记忆
            stored_memory = await self.storage.store_memory(memory)
            
            # 7. 添加到向量数据库
            await self.vector_db.add_memory(stored_memory)
            
            # 8. 设置优先级
            if self.autonomous_config["auto_priority_enabled"]:
                priority, priority_score, priority_rules = await self.priority_manager.calculate_priority(
                    stored_memory
                )
                
                action = AutonomousAction(
                    action_type="set_priority",
                    target_memory_id=stored_memory.id,
                    parameters={
                        "priority": priority.value,
                        "score": priority_score,
                        "applied_rules": priority_rules
                    },
                    confidence=0.8,
                    reasoning=f"设置优先级: {priority.value} (得分: {priority_score:.2f})"
                )
                actions.append(action)
            
            # 9. 设置生命周期阶段
            if self.autonomous_config["auto_lifecycle_enabled"]:
                lifecycle_stage = await self.lifecycle_manager.update_memory_lifecycle(
                    stored_memory, "auto_creation"
                )
                
                action = AutonomousAction(
                    action_type="set_lifecycle",
                    target_memory_id=stored_memory.id,
                    parameters={"stage": lifecycle_stage.value},
                    confidence=0.9,
                    reasoning=f"设置生命周期阶段: {lifecycle_stage.value}"
                )
                actions.append(action)
            
            # 记录所有行动
            for action in actions:
                self.action_history.append(action)
                self.autonomous_stats["total_actions"] += 1
                self.autonomous_stats["action_types"][action.action_type] += 1
                self.autonomous_stats["successful_actions"] += 1
            
            self.logger.info(f"自主处理新记忆完成: {stored_memory.id} ({len(actions)} 个行动)")
            return True, stored_memory, actions
            
        except Exception as e:
            self.logger.error(f"自主处理记忆失败: {e}")
            
            # 记录失败行动
            error_action = AutonomousAction(
                action_type="error",
                target_memory_id=None,
                parameters={"error": str(e)},
                confidence=0.0,
                reasoning=f"处理失败: {e}"
            )
            actions.append(error_action)
            self.action_history.append(error_action)
            self.autonomous_stats["total_actions"] += 1
            self.autonomous_stats["failed_actions"] += 1
            
            return False, None, actions
    
    async def autonomous_maintenance(self) -> Dict[str, Any]:
        """自主维护"""
        
        maintenance_results = {
            "actions_performed": [],
            "total_actions": 0,
            "errors": [],
        }
        
        try:
            # 1. 生命周期维护
            if self.autonomous_config["auto_lifecycle_enabled"]:
                lifecycle_results = await self.lifecycle_manager.auto_lifecycle_maintenance()
                
                action = AutonomousAction(
                    action_type="lifecycle_maintenance",
                    target_memory_id=None,
                    parameters=lifecycle_results,
                    confidence=0.9,
                    reasoning=f"生命周期维护: 处理了 {lifecycle_results['processed_memories']} 个记忆"
                )
                
                self.action_history.append(action)
                maintenance_results["actions_performed"].append(action)
                maintenance_results["total_actions"] += 1
            
            # 2. 优先级重新评估
            if self.autonomous_config["auto_priority_enabled"]:
                # 获取需要重新评估的记忆
                from ..core.memory import MemoryQuery
                query = MemoryQuery(limit=100)
                memories = await self.storage.search_memories(query)
                
                priority_updates = 0
                for memory in memories[:50]:  # 限制数量
                    old_priority_str = memory.metadata.get("priority")
                    new_priority, _, _ = await self.priority_manager.calculate_priority(memory)
                    
                    if old_priority_str != new_priority.value:
                        memory.metadata["priority"] = new_priority.value
                        await self.storage.update_memory(memory)
                        priority_updates += 1
                
                if priority_updates > 0:
                    action = AutonomousAction(
                        action_type="priority_reevaluation",
                        target_memory_id=None,
                        parameters={"updated_count": priority_updates},
                        confidence=0.8,
                        reasoning=f"重新评估了 {priority_updates} 个记忆的优先级"
                    )
                    
                    self.action_history.append(action)
                    maintenance_results["actions_performed"].append(action)
                    maintenance_results["total_actions"] += 1
            
            # 3. 自动清理
            if self.autonomous_config["auto_cleanup_enabled"]:
                # 清理低频标签
                cleaned_tags = await self.tagger.cleanup_rare_tags(min_usage=2)
                
                if cleaned_tags > 0:
                    action = AutonomousAction(
                        action_type="cleanup_tags",
                        target_memory_id=None,
                        parameters={"cleaned_count": cleaned_tags},
                        confidence=0.7,
                        reasoning=f"清理了 {cleaned_tags} 个低频标签"
                    )
                    
                    self.action_history.append(action)
                    maintenance_results["actions_performed"].append(action)
                    maintenance_results["total_actions"] += 1
                
                # 清理旧记忆
                cleaned_memories = await self.storage.cleanup_old_memories(days=90)
                
                if cleaned_memories > 0:
                    action = AutonomousAction(
                        action_type="cleanup_memories",
                        target_memory_id=None,
                        parameters={"cleaned_count": cleaned_memories},
                        confidence=0.8,
                        reasoning=f"清理了 {cleaned_memories} 个旧记忆"
                    )
                    
                    self.action_history.append(action)
                    maintenance_results["actions_performed"].append(action)
                    maintenance_results["total_actions"] += 1
            
            # 更新统计
            self.autonomous_stats["total_actions"] += maintenance_results["total_actions"]
            self.autonomous_stats["successful_actions"] += maintenance_results["total_actions"]
            
            self.logger.info(f"自主维护完成: 执行了 {maintenance_results['total_actions']} 个行动")
            
        except Exception as e:
            self.logger.error(f"自主维护失败: {e}")
            maintenance_results["errors"].append(str(e))
            self.autonomous_stats["failed_actions"] += 1
        
        return maintenance_results
    
    async def get_autonomous_insights(self) -> Dict[str, Any]:
        """获取自主系统洞察"""
        
        # 最近的行动
        recent_actions = sorted(
            self.action_history,
            key=lambda a: a.timestamp,
            reverse=True
        )[:20]
        
        # 行动类型分布
        action_type_distribution = {}
        total_actions = sum(self.autonomous_stats["action_types"].values())
        
        if total_actions > 0:
            for action_type, count in self.autonomous_stats["action_types"].items():
                action_type_distribution[action_type] = {
                    "count": count,
                    "percentage": (count / total_actions) * 100
                }
        
        # 成功率
        success_rate = 0.0
        if self.autonomous_stats["total_actions"] > 0:
            success_rate = (
                self.autonomous_stats["successful_actions"] / 
                self.autonomous_stats["total_actions"]
            ) * 100
        
        return {
            "total_actions": self.autonomous_stats["total_actions"],
            "successful_actions": self.autonomous_stats["successful_actions"],
            "failed_actions": self.autonomous_stats["failed_actions"],
            "success_rate": success_rate,
            "action_type_distribution": action_type_distribution,
            "recent_actions": [
                {
                    "action_type": action.action_type,
                    "target_memory_id": str(action.target_memory_id) if action.target_memory_id else None,
                    "confidence": action.confidence,
                    "reasoning": action.reasoning,
                    "timestamp": action.timestamp.isoformat(),
                }
                for action in recent_actions
            ],
            "autonomous_config": dict(self.autonomous_config),
        }
    
    async def update_autonomous_config(self, **kwargs) -> None:
        """更新自主配置"""
        for key, value in kwargs.items():
            if key in self.autonomous_config:
                self.autonomous_config[key] = value
                self.logger.info(f"自主配置已更新: {key} = {value}")
    
    async def reset_statistics(self) -> None:
        """重置统计信息"""
        self.autonomous_stats = {
            "total_actions": 0,
            "action_types": defaultdict(int),
            "successful_actions": 0,
            "failed_actions": 0,
        }
        self.action_history.clear()
        self.logger.info("自主系统统计信息已重置")
