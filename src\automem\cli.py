"""
AutoMem命令行接口

提供启动服务器、配置管理等命令行功能。
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

from .config import AutoMemConfig
from .server import AutoMemServer
from .utils.logging import setup_logging

# 创建CLI应用
app = typer.Typer(
    name="automem",
    help="AutoMem - 智能记忆增强MCP服务器",
    add_completion=False,
)

# 控制台输出
console = Console()


@app.command()
def serve(
    config: Optional[Path] = typer.Option(
        None,
        "--config",
        "-c",
        help="配置文件路径",
        exists=True,
        file_okay=True,
        dir_okay=False,
    ),
    stdio: bool = typer.Option(
        False,
        "--stdio",
        help="使用stdio传输协议",
    ),
    host: str = typer.Option(
        "localhost",
        "--host",
        "-h",
        help="服务器主机地址",
    ),
    port: int = typer.Option(
        8000,
        "--port",
        "-p",
        help="服务器端口",
    ),
    dev: bool = typer.Option(
        False,
        "--dev",
        help="开发模式（启用调试）",
    ),
    log_level: str = typer.Option(
        "INFO",
        "--log-level",
        help="日志级别",
    ),
):
    """启动AutoMem MCP服务器"""
    
    # 加载配置
    if config:
        try:
            app_config = AutoMemConfig.load_from_file(config)
            console.print(f"✅ 已加载配置文件: {config}", style="green")
        except Exception as e:
            console.print(f"❌ 配置文件加载失败: {e}", style="red")
            raise typer.Exit(1)
    else:
        app_config = AutoMemConfig()
    
    # 覆盖命令行参数
    if dev:
        app_config.debug = True
        app_config.logging.level = "DEBUG"
    
    if log_level:
        app_config.logging.level = log_level.upper()
    
    if stdio:
        app_config.mcp_transport = "stdio"
    else:
        app_config.mcp_host = host
        app_config.mcp_port = port
    
    # 设置日志
    setup_logging(app_config.logging)
    
    # 显示启动信息
    _show_startup_info(app_config)
    
    # 启动服务器
    try:
        server = AutoMemServer(app_config)
        asyncio.run(server.run())
    except KeyboardInterrupt:
        console.print("\n👋 服务器已停止", style="yellow")
    except Exception as e:
        console.print(f"❌ 服务器启动失败: {e}", style="red")
        raise typer.Exit(1)


@app.command()
def config(
    show: bool = typer.Option(
        False,
        "--show",
        help="显示当前配置",
    ),
    init: bool = typer.Option(
        False,
        "--init",
        help="初始化配置文件",
    ),
    output: Optional[Path] = typer.Option(
        None,
        "--output",
        "-o",
        help="配置文件输出路径",
    ),
):
    """配置管理"""
    
    if init:
        # 初始化配置文件
        config_path = output or Path("config.yaml")
        app_config = AutoMemConfig()
        
        try:
            app_config.save_to_file(config_path)
            console.print(f"✅ 配置文件已创建: {config_path}", style="green")
        except Exception as e:
            console.print(f"❌ 配置文件创建失败: {e}", style="red")
            raise typer.Exit(1)
    
    elif show:
        # 显示当前配置
        app_config = AutoMemConfig()
        _show_config(app_config)
    
    else:
        console.print("请指定操作: --show 或 --init", style="yellow")


@app.command()
def status(
    config: Optional[Path] = typer.Option(
        None,
        "--config",
        "-c",
        help="配置文件路径",
    ),
    detailed: bool = typer.Option(
        False,
        "--detailed",
        "-d",
        help="显示详细状态信息",
    ),
):
    """显示服务器状态"""
    console.print("🔍 检查AutoMem服务器状态...", style="blue")

    # 加载配置
    if config and config.exists():
        app_config = AutoMemConfig.load_from_file(config)
    else:
        app_config = AutoMemConfig()

    # 检查数据目录
    data_dir = app_config.storage.data_dir
    data_dir_exists = data_dir.exists()

    # 检查数据库文件
    db_file = data_dir / "memories.db"
    db_exists = db_file.exists()

    # 检查向量数据库
    vector_db_dir = data_dir / "chroma_db"
    vector_db_exists = vector_db_dir.exists()

    # 创建状态表
    status_table = Table(title="📊 系统状态", show_header=True, header_style="bold blue")
    status_table.add_column("组件", style="cyan")
    status_table.add_column("状态", style="green")
    status_table.add_column("详情", style="yellow")

    # 基础状态
    status_table.add_row(
        "配置文件",
        "✅ 有效" if config and config.exists() else "⚠️ 默认",
        str(config) if config else "使用内置默认配置"
    )

    status_table.add_row(
        "数据目录",
        "✅ 存在" if data_dir_exists else "❌ 不存在",
        str(data_dir)
    )

    status_table.add_row(
        "SQLite数据库",
        "✅ 存在" if db_exists else "❌ 不存在",
        str(db_file) if db_exists else "未初始化"
    )

    status_table.add_row(
        "向量数据库",
        "✅ 存在" if vector_db_exists else "❌ 不存在",
        str(vector_db_dir) if vector_db_exists else "未初始化"
    )

    console.print(status_table)

    # 详细信息
    if detailed and data_dir_exists:
        console.print("\n📁 数据目录详情:")

        # 列出数据目录内容
        files_table = Table()
        files_table.add_column("文件/目录", style="cyan")
        files_table.add_column("大小", style="green")
        files_table.add_column("修改时间", style="yellow")

        for item in data_dir.iterdir():
            if item.is_file():
                size = f"{item.stat().st_size / 1024:.1f} KB"
                from datetime import datetime
                mtime = datetime.fromtimestamp(item.stat().st_mtime).strftime("%Y-%m-%d %H:%M")
                files_table.add_row(item.name, size, mtime)
            elif item.is_dir():
                files_table.add_row(f"{item.name}/", "目录", "")

        console.print(files_table)

    # 建议
    if not data_dir_exists:
        console.print("\n💡 建议: 运行 'automem serve' 来初始化数据目录", style="yellow")


@app.command()
def version():
    """显示版本信息"""
    from . import __version__, __author__, __description__
    
    panel = Panel(
        f"[bold blue]AutoMem[/bold blue]\n"
        f"版本: [green]{__version__}[/green]\n"
        f"作者: {__author__}\n"
        f"描述: {__description__}",
        title="版本信息",
        border_style="blue",
    )
    console.print(panel)


@app.command()
def test(
    config: Optional[Path] = typer.Option(
        None,
        "--config",
        "-c",
        help="配置文件路径",
    ),
    component: Optional[str] = typer.Option(
        None,
        "--component",
        help="测试特定组件",
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="详细输出",
    ),
):
    """运行系统测试"""
    from rich.progress import Progress, SpinnerColumn, TextColumn

    console.print("🧪 运行AutoMem系统测试...", style="blue")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:

        # 配置测试
        task1 = progress.add_task("测试配置加载...", total=None)
        try:
            if config and config.exists():
                app_config = AutoMemConfig.load_from_file(config)
            else:
                app_config = AutoMemConfig()
            progress.update(task1, description="✅ 配置加载成功")
        except Exception as e:
            progress.update(task1, description=f"❌ 配置加载失败: {e}")
            return

        # 数据目录测试
        task2 = progress.add_task("测试数据目录...", total=None)
        try:
            data_dir = app_config.storage.data_dir
            data_dir.mkdir(parents=True, exist_ok=True)
            progress.update(task2, description="✅ 数据目录正常")
        except Exception as e:
            progress.update(task2, description=f"❌ 数据目录错误: {e}")
            return

        # 依赖测试
        task3 = progress.add_task("测试依赖包...", total=None)
        try:
            import chromadb
            import sentence_transformers
            import aiosqlite
            progress.update(task3, description="✅ 依赖包完整")
        except ImportError as e:
            progress.update(task3, description=f"❌ 缺少依赖: {e}")
            return

        # 组件测试
        if component:
            task4 = progress.add_task(f"测试组件 {component}...", total=None)
            # TODO: 实现具体组件测试
            progress.update(task4, description=f"⚠️ 组件 {component} 测试功能开发中")

    console.print("\n✅ 系统测试完成", style="green")


@app.command()
def install():
    """安装依赖包"""
    console.print("📦 安装AutoMem依赖包...", style="blue")

    import subprocess

    # 基础依赖
    base_deps = [
        "mcp[cli]>=1.12.0",
        "pydantic>=2.0.0",
        "pydantic-settings>=2.0.0",
        "typer>=0.9.0",
        "rich>=13.0.0",
        "structlog>=23.0.0",
        "aiosqlite>=0.19.0",
        "aiofiles>=23.0.0",
        "python-dateutil>=2.8.0",
    ]

    # 可选依赖
    optional_deps = [
        "chromadb>=0.4.0",
        "sentence-transformers>=2.2.0",
        "nltk>=3.8.0",
        "jieba>=0.42.0",
        "scikit-learn>=1.3.0",
        "numpy>=1.24.0",
    ]

    try:
        # 安装基础依赖
        console.print("安装基础依赖...", style="yellow")
        for dep in base_deps:
            console.print(f"  安装 {dep}")
            subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True, capture_output=True)

        # 安装可选依赖
        console.print("安装可选依赖...", style="yellow")
        for dep in optional_deps:
            console.print(f"  安装 {dep}")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True, capture_output=True)
            except subprocess.CalledProcessError:
                console.print(f"  ⚠️ 可选依赖 {dep} 安装失败", style="yellow")

        console.print("✅ 依赖安装完成", style="green")

    except subprocess.CalledProcessError as e:
        console.print(f"❌ 依赖安装失败: {e}", style="red")
        raise typer.Exit(1)


def _show_startup_info(config: AutoMemConfig) -> None:
    """显示启动信息"""
    
    # 创建信息表格
    table = Table(title="AutoMem 服务器配置", show_header=True, header_style="bold blue")
    table.add_column("配置项", style="cyan", no_wrap=True)
    table.add_column("值", style="green")
    
    # 基本信息
    table.add_row("服务器名称", config.server_name)
    table.add_row("版本", config.server_version)
    table.add_row("调试模式", "✅ 启用" if config.debug else "❌ 禁用")
    
    # 传输配置
    if config.mcp_transport == "stdio":
        table.add_row("传输协议", "stdio")
    else:
        table.add_row("传输协议", f"HTTP ({config.mcp_host}:{config.mcp_port})")
    
    # 存储配置
    table.add_row("数据目录", str(config.storage.data_dir))
    table.add_row("最大记忆数", str(config.storage.max_memories))
    
    # 智能配置
    table.add_row("嵌入模型", config.intelligence.embedding_model)
    table.add_row("自动标签", "✅ 启用" if config.intelligence.auto_tag_enabled else "❌ 禁用")
    
    # 日志配置
    table.add_row("日志级别", config.logging.level)
    if config.logging.file:
        table.add_row("日志文件", str(config.logging.file))
    
    console.print(table)
    console.print()
    
    # 启动消息
    startup_text = Text()
    startup_text.append("🚀 ", style="bold yellow")
    startup_text.append("AutoMem MCP服务器启动中...", style="bold white")
    
    panel = Panel(
        startup_text,
        border_style="green",
        padding=(1, 2),
    )
    console.print(panel)


def _show_config(config: AutoMemConfig) -> None:
    """显示配置详情"""
    
    # 存储配置
    storage_table = Table(title="存储配置", show_header=True, header_style="bold cyan")
    storage_table.add_column("配置项", style="cyan")
    storage_table.add_column("值", style="white")
    
    storage_table.add_row("数据目录", str(config.storage.data_dir))
    storage_table.add_row("最大记忆数", str(config.storage.max_memories))
    storage_table.add_row("清理间隔", config.storage.cleanup_interval)
    storage_table.add_row("备份启用", "✅" if config.storage.backup_enabled else "❌")
    
    console.print(storage_table)
    console.print()
    
    # 智能配置
    intelligence_table = Table(title="智能配置", show_header=True, header_style="bold magenta")
    intelligence_table.add_column("配置项", style="magenta")
    intelligence_table.add_column("值", style="white")
    
    intelligence_table.add_row("嵌入模型", config.intelligence.embedding_model)
    intelligence_table.add_row("分类阈值", str(config.intelligence.classification_threshold))
    intelligence_table.add_row("自动标签", "✅" if config.intelligence.auto_tag_enabled else "❌")
    intelligence_table.add_row("最大标签数", str(config.intelligence.max_tags_per_memory))
    
    console.print(intelligence_table)
    console.print()
    
    # 检索配置
    retrieval_table = Table(title="检索配置", show_header=True, header_style="bold yellow")
    retrieval_table.add_column("配置项", style="yellow")
    retrieval_table.add_column("值", style="white")
    
    retrieval_table.add_row("最大结果数", str(config.retrieval.max_results))
    retrieval_table.add_row("相似度阈值", str(config.retrieval.similarity_threshold))
    retrieval_table.add_row("时间衰减因子", str(config.retrieval.time_decay_factor))
    retrieval_table.add_row("语义搜索", "✅" if config.retrieval.semantic_search_enabled else "❌")
    
    console.print(retrieval_table)


def main() -> None:
    """主入口函数"""
    try:
        app()
    except Exception as e:
        console.print(f"❌ 程序执行失败: {e}", style="red")
        sys.exit(1)


if __name__ == "__main__":
    main()
