"""
元数据管理器

管理记忆的元数据信息，包括统计、索引和关系。
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Set, Any, Optional, Tuple
from uuid import UUID
from collections import defaultdict, Counter

from ..utils.logging import LoggerMixin
from .memory import Memory, MemoryType, MemoryImportance
from .storage import StorageEngine


class MetadataManager(LoggerMixin):
    """元数据管理器"""
    
    def __init__(self, storage: StorageEngine):
        self.storage = storage
        
        # 缓存的统计信息
        self._stats_cache: Dict[str, Any] = {}
        self._cache_timestamp: Optional[datetime] = None
        self._cache_ttl = timedelta(minutes=5)  # 缓存5分钟
    
    async def update_memory_metadata(self, memory: Memory) -> None:
        """更新记忆元数据"""
        # 计算记忆年龄
        age_days = memory.age_in_days()
        
        # 更新元数据
        memory.metadata.update({
            "age_days": age_days,
            "is_recent": memory.is_recent(),
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "tag_count": len(memory.tags),
            "category_count": len(memory.categories),
            "relation_count": len(memory.related_memories),
        })
        
        # 计算重要性评分
        importance_score = await self._calculate_importance_score(memory)
        memory.metadata["importance_score"] = importance_score
        
        self.logger.debug(f"更新记忆元数据: {memory.id}")
    
    async def _calculate_importance_score(self, memory: Memory) -> float:
        """计算记忆重要性评分"""
        score = 0.0
        
        # 基础重要性权重
        importance_weights = {
            MemoryImportance.CRITICAL: 1.0,
            MemoryImportance.HIGH: 0.8,
            MemoryImportance.MEDIUM: 0.6,
            MemoryImportance.LOW: 0.4,
            MemoryImportance.MINIMAL: 0.2,
        }
        score += importance_weights.get(memory.importance, 0.5)
        
        # 访问频率权重
        if memory.access_count > 0:
            access_weight = min(memory.access_count / 10.0, 0.3)
            score += access_weight
        
        # 关联数量权重
        if memory.related_memories:
            relation_weight = min(len(memory.related_memories) / 5.0, 0.2)
            score += relation_weight
        
        # 标签数量权重
        if memory.tags:
            tag_weight = min(len(memory.tags) / 3.0, 0.1)
            score += tag_weight
        
        # 时间衰减
        age_days = memory.age_in_days()
        if age_days > 0:
            time_decay = max(0.1, 1.0 - (age_days / 365.0))  # 一年内线性衰减
            score *= time_decay
        
        return min(score, 1.0)
    
    async def get_memory_statistics(self, force_refresh: bool = False) -> Dict[str, Any]:
        """获取记忆统计信息"""
        # 检查缓存
        if not force_refresh and self._is_cache_valid():
            return self._stats_cache
        
        # 重新计算统计信息
        stats = await self._calculate_statistics()
        
        # 更新缓存
        self._stats_cache = stats
        self._cache_timestamp = datetime.now(timezone.utc)
        
        return stats
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not self._cache_timestamp:
            return False
        
        return datetime.now(timezone.utc) - self._cache_timestamp < self._cache_ttl
    
    async def _calculate_statistics(self) -> Dict[str, Any]:
        """计算统计信息"""
        # 获取基础统计
        basic_stats = await self.storage.get_memory_stats()
        
        # 获取所有记忆进行详细分析
        from .memory import MemoryQuery
        all_memories = await self.storage.search_memories(
            MemoryQuery(limit=10000)  # 获取大量记忆用于统计
        )
        
        # 计算详细统计
        detailed_stats = self._analyze_memories(all_memories)
        
        # 合并统计信息
        return {
            **basic_stats,
            **detailed_stats,
            "calculated_at": datetime.now(timezone.utc).isoformat(),
        }
    
    def _analyze_memories(self, memories: List[Memory]) -> Dict[str, Any]:
        """分析记忆列表"""
        if not memories:
            return {}
        
        # 标签统计
        tag_counter = Counter()
        category_counter = Counter()
        
        # 时间分布统计
        time_buckets = defaultdict(int)
        
        # 重要性分布
        importance_distribution = defaultdict(int)
        
        # 访问模式
        access_patterns = {
            "high_access": 0,  # 访问次数 > 10
            "medium_access": 0,  # 访问次数 5-10
            "low_access": 0,  # 访问次数 1-4
            "never_accessed": 0,  # 访问次数 = 0
        }
        
        # 关联度统计
        relation_stats = {
            "highly_connected": 0,  # 关联数 > 5
            "moderately_connected": 0,  # 关联数 2-5
            "lightly_connected": 0,  # 关联数 1
            "isolated": 0,  # 无关联
        }
        
        for memory in memories:
            # 标签统计
            for tag in memory.tags:
                tag_counter[tag] += 1
            
            # 分类统计
            for category in memory.categories:
                category_counter[category] += 1
            
            # 时间分布
            age_days = memory.age_in_days()
            if age_days <= 1:
                time_buckets["today"] += 1
            elif age_days <= 7:
                time_buckets["this_week"] += 1
            elif age_days <= 30:
                time_buckets["this_month"] += 1
            elif age_days <= 365:
                time_buckets["this_year"] += 1
            else:
                time_buckets["older"] += 1
            
            # 重要性分布
            importance_distribution[memory.importance.value] += 1
            
            # 访问模式
            if memory.access_count > 10:
                access_patterns["high_access"] += 1
            elif memory.access_count >= 5:
                access_patterns["medium_access"] += 1
            elif memory.access_count >= 1:
                access_patterns["low_access"] += 1
            else:
                access_patterns["never_accessed"] += 1
            
            # 关联度
            relation_count = len(memory.related_memories)
            if relation_count > 5:
                relation_stats["highly_connected"] += 1
            elif relation_count >= 2:
                relation_stats["moderately_connected"] += 1
            elif relation_count == 1:
                relation_stats["lightly_connected"] += 1
            else:
                relation_stats["isolated"] += 1
        
        return {
            "detailed_analysis": {
                "top_tags": dict(tag_counter.most_common(10)),
                "top_categories": dict(category_counter.most_common(10)),
                "time_distribution": dict(time_buckets),
                "importance_distribution": dict(importance_distribution),
                "access_patterns": access_patterns,
                "relation_statistics": relation_stats,
            },
            "summary": {
                "total_tags": len(tag_counter),
                "total_categories": len(category_counter),
                "avg_tags_per_memory": sum(len(m.tags) for m in memories) / len(memories),
                "avg_relations_per_memory": sum(len(m.related_memories) for m in memories) / len(memories),
                "avg_access_count": sum(m.access_count for m in memories) / len(memories),
            }
        }
    
    async def get_tag_suggestions(self, content: str, limit: int = 5) -> List[str]:
        """基于内容获取标签建议"""
        # 获取现有标签统计
        stats = await self.get_memory_statistics()
        top_tags = stats.get("detailed_analysis", {}).get("top_tags", {})
        
        # 简单的关键词匹配算法
        content_lower = content.lower()
        suggestions = []
        
        for tag, count in top_tags.items():
            if tag.lower() in content_lower:
                suggestions.append(tag)
        
        # 如果建议不足，添加一些通用标签
        if len(suggestions) < limit:
            common_tags = ["重要", "工作", "学习", "个人", "项目"]
            for tag in common_tags:
                if tag not in suggestions and len(suggestions) < limit:
                    suggestions.append(tag)
        
        return suggestions[:limit]
    
    async def find_potential_relations(self, memory: Memory, limit: int = 5) -> List[Tuple[UUID, float]]:
        """查找潜在的记忆关联"""
        # 基于标签相似性查找
        tag_matches = []
        
        if memory.tags:
            # 查找具有相同标签的记忆
            from .memory import MemoryQuery
            
            query = MemoryQuery(
                tags=list(memory.tags),
                limit=limit * 2  # 获取更多候选
            )
            
            similar_memories = await self.storage.search_memories(query)
            
            for similar_memory in similar_memories:
                if similar_memory.id == memory.id:
                    continue
                
                # 计算标签相似度
                common_tags = memory.tags.intersection(similar_memory.tags)
                if common_tags:
                    similarity = len(common_tags) / len(memory.tags.union(similar_memory.tags))
                    tag_matches.append((similar_memory.id, similarity))
        
        # 按相似度排序并返回前N个
        tag_matches.sort(key=lambda x: x[1], reverse=True)
        return tag_matches[:limit]
    
    async def cleanup_metadata(self) -> None:
        """清理元数据缓存"""
        self._stats_cache.clear()
        self._cache_timestamp = None
        self.logger.info("元数据缓存已清理")
