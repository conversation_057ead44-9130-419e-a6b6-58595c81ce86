"""
测试自主决策系统
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from src.automem.intelligence.autonomous import AutonomousMemorySystem, AutonomousAction
from src.automem.core.memory import Memory, MemoryType, MemoryImportance
from src.automem.intelligence.decision import DecisionType, DecisionResult


class TestAutonomousMemorySystem:
    """测试自主记忆系统"""
    
    @pytest.mark.asyncio
    async def test_process_new_memory_store(self, autonomous_system):
        """测试处理新记忆 - 存储决策"""
        content = "这是一个重要的技术讨论，关于Python编程的最佳实践。"
        context = {"session_id": "test_session", "source": "conversation"}
        
        # 模拟决策引擎返回存储决策
        with patch.object(autonomous_system.decision_engine, 'make_decision') as mock_decision:
            mock_decision.return_value = DecisionResult(
                decision=DecisionType.STORE,
                confidence=0.8,
                reasoning="内容具有技术价值",
                memory_type=MemoryType.CONVERSATION,
                importance=MemoryImportance.MEDIUM
            )
            
            # 模拟其他组件
            with patch.object(autonomous_system.classifier, 'classify_memory') as mock_classify, \
                 patch.object(autonomous_system.tagger, 'generate_tags') as mock_tags, \
                 patch.object(autonomous_system.importance_analyzer, 'analyze_importance') as mock_importance:
                
                mock_classify.return_value = {"技术", "编程"}
                mock_tags.return_value = {"Python", "最佳实践", "讨论"}
                mock_importance.return_value = (MemoryImportance.MEDIUM, 0.7, {"技术内容": 0.7})
                
                # 处理记忆
                success, memory, actions = await autonomous_system.process_new_memory(content, context)
                
                assert success is True
                assert memory is not None
                assert memory.content == content
                assert len(actions) > 0
                
                # 验证行动类型
                action_types = [action.action_type for action in actions]
                assert "classify" in action_types
                assert "tag" in action_types
                assert "analyze_importance" in action_types
    
    @pytest.mark.asyncio
    async def test_process_new_memory_ignore(self, autonomous_system):
        """测试处理新记忆 - 忽略决策"""
        content = "嗯，好的。"
        context = {"session_id": "test_session"}
        
        # 模拟决策引擎返回忽略决策
        with patch.object(autonomous_system.decision_engine, 'make_decision') as mock_decision:
            mock_decision.return_value = DecisionResult(
                decision=DecisionType.IGNORE,
                confidence=0.9,
                reasoning="内容过于简短，缺乏信息价值",
                memory_type=MemoryType.CONVERSATION,
                importance=MemoryImportance.MINIMAL
            )
            
            # 处理记忆
            success, memory, actions = await autonomous_system.process_new_memory(content, context)
            
            assert success is False
            assert memory is None
            assert len(actions) == 1
            assert actions[0].action_type == "ignore"
            assert "简短" in actions[0].reasoning
    
    @pytest.mark.asyncio
    async def test_autonomous_maintenance(self, autonomous_system):
        """测试自主维护"""
        # 模拟各种维护组件
        with patch.object(autonomous_system.lifecycle_manager, 'auto_lifecycle_maintenance') as mock_lifecycle, \
             patch.object(autonomous_system.priority_manager, 'calculate_priority') as mock_priority, \
             patch.object(autonomous_system.tagger, 'cleanup_rare_tags') as mock_cleanup_tags, \
             patch.object(autonomous_system.storage, 'cleanup_old_memories') as mock_cleanup_memories, \
             patch.object(autonomous_system.storage, 'search_memories') as mock_search:
            
            # 设置模拟返回值
            mock_lifecycle.return_value = {"processed_memories": 10, "transitions": 3}
            mock_priority.return_value = (MemoryImportance.MEDIUM, 0.6, ["rule1"])
            mock_cleanup_tags.return_value = 5
            mock_cleanup_memories.return_value = 2
            mock_search.return_value = []  # 空的记忆列表
            
            # 执行维护
            results = await autonomous_system.autonomous_maintenance()
            
            assert "actions_performed" in results
            assert "total_actions" in results
            assert results["total_actions"] > 0
            
            # 验证维护行动
            action_types = [action.action_type for action in results["actions_performed"]]
            assert "lifecycle_maintenance" in action_types
    
    @pytest.mark.asyncio
    async def test_get_autonomous_insights(self, autonomous_system):
        """测试获取自主系统洞察"""
        # 添加一些行动历史
        action1 = AutonomousAction(
            action_type="store",
            target_memory_id=None,
            parameters={"content": "test"},
            confidence=0.8,
            reasoning="测试存储"
        )
        
        action2 = AutonomousAction(
            action_type="classify",
            target_memory_id=None,
            parameters={"categories": ["test"]},
            confidence=0.7,
            reasoning="测试分类"
        )
        
        autonomous_system.action_history = [action1, action2]
        autonomous_system.autonomous_stats["total_actions"] = 2
        autonomous_system.autonomous_stats["successful_actions"] = 2
        autonomous_system.autonomous_stats["action_types"]["store"] = 1
        autonomous_system.autonomous_stats["action_types"]["classify"] = 1
        
        # 获取洞察
        insights = await autonomous_system.get_autonomous_insights()
        
        assert "total_actions" in insights
        assert insights["total_actions"] == 2
        assert "success_rate" in insights
        assert insights["success_rate"] == 100.0
        assert "action_type_distribution" in insights
        assert "recent_actions" in insights
        assert len(insights["recent_actions"]) == 2
    
    @pytest.mark.asyncio
    async def test_update_autonomous_config(self, autonomous_system):
        """测试更新自主配置"""
        # 更新配置
        await autonomous_system.update_autonomous_config(
            auto_processing_enabled=False,
            processing_interval_minutes=60
        )
        
        assert autonomous_system.autonomous_config["auto_processing_enabled"] is False
        assert autonomous_system.autonomous_config["processing_interval_minutes"] == 60
    
    @pytest.mark.asyncio
    async def test_reset_statistics(self, autonomous_system):
        """测试重置统计信息"""
        # 添加一些统计数据
        autonomous_system.autonomous_stats["total_actions"] = 10
        autonomous_system.autonomous_stats["successful_actions"] = 8
        autonomous_system.action_history = [Mock(), Mock(), Mock()]
        
        # 重置统计
        await autonomous_system.reset_statistics()
        
        assert autonomous_system.autonomous_stats["total_actions"] == 0
        assert autonomous_system.autonomous_stats["successful_actions"] == 0
        assert len(autonomous_system.action_history) == 0
    
    @pytest.mark.asyncio
    async def test_error_handling(self, autonomous_system):
        """测试错误处理"""
        content = "测试内容"
        
        # 模拟决策引擎抛出异常
        with patch.object(autonomous_system.decision_engine, 'make_decision') as mock_decision:
            mock_decision.side_effect = Exception("模拟错误")
            
            # 处理记忆
            success, memory, actions = await autonomous_system.process_new_memory(content)
            
            assert success is False
            assert memory is None
            assert len(actions) == 1
            assert actions[0].action_type == "error"
            assert "模拟错误" in actions[0].reasoning
    
    @pytest.mark.asyncio
    async def test_concurrent_processing(self, autonomous_system):
        """测试并发处理"""
        import asyncio
        
        contents = [
            "第一个测试内容",
            "第二个测试内容", 
            "第三个测试内容"
        ]
        
        # 模拟决策引擎
        with patch.object(autonomous_system.decision_engine, 'make_decision') as mock_decision:
            mock_decision.return_value = DecisionResult(
                decision=DecisionType.STORE,
                confidence=0.8,
                reasoning="测试内容",
                memory_type=MemoryType.CONVERSATION,
                importance=MemoryImportance.MEDIUM
            )
            
            # 模拟其他组件
            with patch.object(autonomous_system.classifier, 'classify_memory') as mock_classify, \
                 patch.object(autonomous_system.tagger, 'generate_tags') as mock_tags, \
                 patch.object(autonomous_system.importance_analyzer, 'analyze_importance') as mock_importance:
                
                mock_classify.return_value = {"测试"}
                mock_tags.return_value = {"测试", "内容"}
                mock_importance.return_value = (MemoryImportance.MEDIUM, 0.6, {})
                
                # 并发处理多个记忆
                tasks = [
                    autonomous_system.process_new_memory(content)
                    for content in contents
                ]
                
                results = await asyncio.gather(*tasks)
                
                # 验证所有处理都成功
                assert len(results) == 3
                for success, memory, actions in results:
                    assert success is True
                    assert memory is not None
                    assert len(actions) > 0


class TestAutonomousAction:
    """测试自主行动"""
    
    def test_action_creation(self):
        """测试行动创建"""
        from uuid import uuid4
        
        memory_id = uuid4()
        action = AutonomousAction(
            action_type="store",
            target_memory_id=memory_id,
            parameters={"content": "test content"},
            confidence=0.85,
            reasoning="测试存储行动"
        )
        
        assert action.action_type == "store"
        assert action.target_memory_id == memory_id
        assert action.parameters["content"] == "test content"
        assert action.confidence == 0.85
        assert action.reasoning == "测试存储行动"
        assert action.timestamp is not None
    
    def test_action_without_memory_id(self):
        """测试不带记忆ID的行动"""
        action = AutonomousAction(
            action_type="maintenance",
            target_memory_id=None,
            parameters={"type": "cleanup"},
            confidence=0.9,
            reasoning="系统维护"
        )
        
        assert action.action_type == "maintenance"
        assert action.target_memory_id is None
        assert action.parameters["type"] == "cleanup"
        assert action.confidence == 0.9
