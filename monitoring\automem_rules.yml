# AutoMem Prometheus 告警规则

groups:
  - name: automem.rules
    rules:
      # 服务可用性告警
      - alert: AutoMemDown
        expr: up{job="automem"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "AutoMem 服务不可用"
          description: "AutoMem 服务已停止响应超过 1 分钟"

      # 高内存使用率告警
      - alert: AutoMemHighMemoryUsage
        expr: (process_resident_memory_bytes{job="automem"} / 1024 / 1024 / 1024) > 1.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "AutoMem 内存使用率过高"
          description: "AutoMem 内存使用超过 1.5GB，当前使用: {{ $value }}GB"

      # 高CPU使用率告警
      - alert: AutoMemHighCPUUsage
        expr: rate(process_cpu_seconds_total{job="automem"}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "AutoMem CPU使用率过高"
          description: "AutoMem CPU使用率超过 80%，当前使用: {{ $value }}%"

      # 错误率告警
      - alert: AutoMemHighErrorRate
        expr: rate(automem_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "AutoMem 错误率过高"
          description: "AutoMem 错误率超过 0.1/s，当前错误率: {{ $value }}/s"

      # 响应时间告警
      - alert: AutoMemSlowResponse
        expr: histogram_quantile(0.95, rate(automem_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "AutoMem 响应时间过慢"
          description: "AutoMem 95%分位响应时间超过 2 秒，当前: {{ $value }}s"

      # 存储空间告警
      - alert: AutoMemLowDiskSpace
        expr: (automem_disk_free_bytes / automem_disk_total_bytes) * 100 < 10
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "AutoMem 存储空间不足"
          description: "AutoMem 可用存储空间低于 10%，剩余: {{ $value }}%"

      # 记忆数量告警
      - alert: AutoMemTooManyMemories
        expr: automem_memories_total > 90000
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "AutoMem 记忆数量接近上限"
          description: "AutoMem 记忆数量接近上限，当前: {{ $value }}"

      # 向量数据库连接告警
      - alert: AutoMemVectorDBDown
        expr: automem_vector_db_connected == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "AutoMem 向量数据库连接失败"
          description: "AutoMem 无法连接到向量数据库"

  - name: system.rules
    rules:
      # 系统负载告警
      - alert: HighSystemLoad
        expr: node_load1 > 4
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "系统负载过高"
          description: "系统 1 分钟负载超过 4，当前: {{ $value }}"

      # 磁盘空间告警
      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 10
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "根分区可用空间低于 10%，剩余: {{ $value }}%"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "系统内存使用率超过 90%，当前: {{ $value }}%"
