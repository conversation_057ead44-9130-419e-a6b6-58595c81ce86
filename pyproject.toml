[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "automem"
version = "0.1.0"
description = "智能记忆增强MCP服务器 - 让AI拥有持久记忆"
authors = [
    {name = "AutoMem Team", email = "<EMAIL>"},
]
maintainers = [
    {name = "AutoMem Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
keywords = [
    "mcp",
    "memory",
    "ai",
    "artificial-intelligence",
    "machine-learning",
    "nlp",
    "semantic-search",
    "vector-database",
    "context-management",
    "intelligent-assistant"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Distributed Computing",
    "Typing :: Typed"
]

dependencies = [
    # MCP Framework
    "mcp[cli]>=1.12.0",

    # Core Dependencies
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "typer>=0.9.0",
    "rich>=13.0.0",
    "aiohttp>=3.8.0",
    "aiofiles>=23.0.0",
    "aiosqlite>=0.19.0",

    # Vector Database & Embeddings
    "chromadb>=0.4.0",
    "sentence-transformers>=2.2.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",

    # Text Processing
    "jieba>=0.42.0",

    # Utilities
    "structlog>=23.0.0",
    "python-dateutil>=2.8.0",
    "PyYAML>=6.0",
]

[project.urls]
Homepage = "https://github.com/your-org/automem"
Documentation = "https://automem.readthedocs.io/"
Repository = "https://github.com/your-org/automem"
"Bug Reports" = "https://github.com/your-org/automem/issues"
Changelog = "https://github.com/your-org/automem/blob/main/CHANGELOG.md"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.2.0",
    "mkdocs-mermaid2-plugin>=1.1.0",
]
monitoring = [
    "prometheus-client>=0.17.0",
    "psutil>=5.9.0",
]
security = [
    "cryptography>=41.0.0",
    "bcrypt>=4.0.0",
]
performance = [
    "memory-profiler>=0.61.0",
    "line-profiler>=4.1.0",
]
ml = [
    "torch>=2.0.0",
    "transformers>=4.30.0",
    "datasets>=2.14.0",
]

[project.scripts]
automem = "automem.cli:app"

[tool.hatch.build.targets.wheel]
packages = ["src/automem"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/README.md",
    "/LICENSE",
]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["automem"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "chromadb.*",
    "sentence_transformers.*",
    "jieba.*",
    "nltk.*",
    "spacy.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=automem",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src/automem"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
