"""
端到端集成测试

测试完整的记忆存储、检索和管理流程。
"""

import pytest
import asyncio
from pathlib import Path
from datetime import datetime, timezone

from src.automem.config import AutoMemConfig
from src.automem.server import AutoMemServer, AutoMemServerContext
from src.automem.core.memory import Memory, MemoryType, MemoryImportance


class TestEndToEndIntegration:
    """端到端集成测试"""
    
    @pytest.fixture
    async def server_context(self, test_config):
        """创建服务器上下文"""
        context = AutoMemServerContext(test_config)
        
        # 初始化组件
        from src.automem.core.storage import SQLiteMemoryManager
        from src.automem.core.vector_db import VectorDatabase
        from src.automem.intelligence.autonomous import AutonomousMemorySystem
        
        context.memory_manager = SQLiteMemoryManager(test_config.storage)
        await context.memory_manager.initialize()
        
        context.vector_db = VectorDatabase(test_config.storage, test_config.intelligence)
        await context.vector_db.initialize()
        
        context.autonomous_system = AutonomousMemorySystem(
            test_config.intelligence,
            test_config.retrieval,
            test_config.storage,
            context.memory_manager.storage,
            context.vector_db
        )
        
        yield context
        
        # 清理
        await context.memory_manager.close()
        await context.vector_db.close()
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_complete_memory_lifecycle(self, server_context):
        """测试完整的记忆生命周期"""
        autonomous_system = server_context.autonomous_system
        
        # 1. 存储记忆
        content = "Python是一种高级编程语言，广泛用于Web开发、数据科学和人工智能。"
        context = {
            "session_id": "test_session_001",
            "conversation_id": "conv_001",
            "source": "integration_test"
        }
        
        success, memory, actions = await autonomous_system.process_new_memory(content, context)
        
        assert success is True
        assert memory is not None
        assert memory.content == content
        assert len(actions) > 0
        
        memory_id = memory.id
        
        # 2. 检索记忆
        from src.automem.retrieval.semantic_search import SemanticSearchEngine
        search_engine = SemanticSearchEngine(
            server_context.config.retrieval,
            server_context.vector_db,
            server_context.memory_manager.storage
        )
        
        search_results = await search_engine.search(
            query="Python编程语言",
            limit=5,
            similarity_threshold=0.3
        )
        
        assert len(search_results) >= 1
        found_memory = None
        for result in search_results:
            if result.memory.id == memory_id:
                found_memory = result.memory
                break
        
        assert found_memory is not None
        assert found_memory.content == content
        
        # 3. 更新记忆
        found_memory.add_tag("编程语言")
        found_memory.add_category("技术")
        updated_memory = await server_context.memory_manager.storage.update_memory(found_memory)
        
        assert "编程语言" in updated_memory.tags
        assert "技术" in updated_memory.categories
        
        # 4. 获取上下文
        from src.automem.retrieval.context_retriever import ContextRetriever, ConversationContext
        context_retriever = ContextRetriever(
            server_context.config.retrieval,
            server_context.memory_manager.storage,
            search_engine
        )
        
        conv_context = ConversationContext(
            session_id="test_session_001",
            conversation_id="conv_001",
            topics=["Python"]
        )
        
        contextual_memories = await context_retriever.retrieve_context(
            query="编程",
            context=conv_context,
            limit=5
        )
        
        assert len(contextual_memories) >= 1
        
        # 5. 生命周期管理
        lifecycle_manager = autonomous_system.lifecycle_manager
        new_stage = await lifecycle_manager.update_memory_lifecycle(
            updated_memory, "test_transition"
        )
        
        assert new_stage is not None
        
        # 6. 优先级管理
        priority_manager = autonomous_system.priority_manager
        priority, score, rules = await priority_manager.calculate_priority(updated_memory)
        
        assert priority is not None
        assert score >= 0.0
        assert isinstance(rules, list)
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_batch_memory_processing(self, server_context):
        """测试批量记忆处理"""
        autonomous_system = server_context.autonomous_system
        
        # 准备多个记忆内容
        memory_contents = [
            "机器学习是人工智能的一个重要分支。",
            "深度学习使用神经网络来模拟人脑的学习过程。",
            "自然语言处理帮助计算机理解和生成人类语言。",
            "计算机视觉让机器能够识别和理解图像。",
            "强化学习通过奖励机制来训练智能体。"
        ]
        
        # 批量处理记忆
        stored_memories = []
        for i, content in enumerate(memory_contents):
            context = {
                "session_id": f"batch_session_{i}",
                "source": "batch_test"
            }
            
            success, memory, actions = await autonomous_system.process_new_memory(content, context)
            
            if success and memory:
                stored_memories.append(memory)
        
        assert len(stored_memories) == len(memory_contents)
        
        # 验证所有记忆都已存储
        from src.automem.core.memory import MemoryQuery
        query = MemoryQuery(limit=10)
        all_memories = await server_context.memory_manager.storage.search_memories(query)
        
        assert len(all_memories) >= len(stored_memories)
        
        # 测试批量搜索
        from src.automem.retrieval.semantic_search import SemanticSearchEngine
        search_engine = SemanticSearchEngine(
            server_context.config.retrieval,
            server_context.vector_db,
            server_context.memory_manager.storage
        )
        
        search_results = await search_engine.search(
            query="人工智能机器学习",
            limit=10,
            similarity_threshold=0.3
        )
        
        assert len(search_results) >= 2  # 应该找到相关的记忆
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, server_context):
        """测试并发操作"""
        autonomous_system = server_context.autonomous_system
        
        # 准备并发任务
        async def store_memory_task(content, session_id):
            context = {"session_id": session_id, "source": "concurrent_test"}
            return await autonomous_system.process_new_memory(content, context)
        
        # 创建并发任务
        tasks = []
        for i in range(5):
            content = f"并发测试记忆 {i}: 这是第{i}个并发存储的记忆。"
            session_id = f"concurrent_session_{i}"
            task = store_memory_task(content, session_id)
            tasks.append(task)
        
        # 执行并发任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证结果
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == 5
        
        for success, memory, actions in successful_results:
            assert success is True
            assert memory is not None
            assert len(actions) > 0
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_memory_search_and_ranking(self, server_context):
        """测试记忆搜索和排序"""
        autonomous_system = server_context.autonomous_system
        
        # 存储不同重要性的记忆
        test_memories = [
            ("Python是世界上最流行的编程语言之一。", MemoryImportance.HIGH),
            ("今天天气不错。", MemoryImportance.LOW),
            ("机器学习算法可以从数据中学习模式。", MemoryImportance.HIGH),
            ("我吃了午饭。", MemoryImportance.MINIMAL),
            ("深度学习是机器学习的一个子领域。", MemoryImportance.MEDIUM)
        ]
        
        stored_memories = []
        for content, importance in test_memories:
            memory = Memory(
                content=content,
                memory_type=MemoryType.FACT,
                importance=importance,
                source="ranking_test"
            )
            
            stored_memory = await server_context.memory_manager.storage.store_memory(memory)
            await server_context.vector_db.add_memory(stored_memory)
            stored_memories.append(stored_memory)
        
        # 搜索技术相关内容
        from src.automem.retrieval.semantic_search import SemanticSearchEngine
        search_engine = SemanticSearchEngine(
            server_context.config.retrieval,
            server_context.vector_db,
            server_context.memory_manager.storage
        )
        
        search_results = await search_engine.search(
            query="编程 机器学习",
            limit=5,
            similarity_threshold=0.1
        )
        
        # 验证搜索结果
        assert len(search_results) >= 2
        
        # 验证高重要性记忆排在前面
        high_importance_found = False
        for result in search_results[:2]:  # 检查前两个结果
            if result.memory.importance == MemoryImportance.HIGH:
                high_importance_found = True
                break
        
        assert high_importance_found
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_system_maintenance(self, server_context):
        """测试系统维护功能"""
        autonomous_system = server_context.autonomous_system
        
        # 存储一些测试记忆
        for i in range(10):
            content = f"维护测试记忆 {i}"
            context = {"session_id": f"maintenance_session_{i}", "source": "maintenance_test"}
            
            await autonomous_system.process_new_memory(content, context)
        
        # 执行自主维护
        maintenance_results = await autonomous_system.autonomous_maintenance()
        
        assert "actions_performed" in maintenance_results
        assert "total_actions" in maintenance_results
        assert maintenance_results["total_actions"] >= 0
        
        # 获取系统洞察
        insights = await autonomous_system.get_autonomous_insights()
        
        assert "total_actions" in insights
        assert "success_rate" in insights
        assert "recent_actions" in insights
        assert insights["total_actions"] > 0
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_error_recovery(self, server_context):
        """测试错误恢复"""
        autonomous_system = server_context.autonomous_system
        
        # 测试无效输入的处理
        invalid_contents = [
            "",  # 空内容
            " " * 1000,  # 过长空白
            None,  # None值
        ]
        
        for content in invalid_contents:
            try:
                if content is None:
                    # 跳过None测试，因为它会在参数验证阶段失败
                    continue
                
                success, memory, actions = await autonomous_system.process_new_memory(content)
                
                # 系统应该能够处理这些情况
                if not success:
                    assert memory is None
                    assert len(actions) > 0
                    assert any(action.action_type in ["ignore", "error"] for action in actions)
                
            except Exception as e:
                # 如果抛出异常，应该是预期的验证错误
                assert "content" in str(e).lower() or "invalid" in str(e).lower()
    
    @pytest.mark.integration
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_performance_under_load(self, server_context):
        """测试负载下的性能"""
        autonomous_system = server_context.autonomous_system
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 存储大量记忆
        num_memories = 50
        tasks = []
        
        for i in range(num_memories):
            content = f"性能测试记忆 {i}: 这是用于测试系统性能的记忆内容。包含一些技术关键词如Python、机器学习、数据科学等。"
            context = {
                "session_id": f"perf_session_{i % 10}",  # 10个不同的会话
                "source": "performance_test"
            }
            
            task = autonomous_system.process_new_memory(content, context)
            tasks.append(task)
        
        # 执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 记录结束时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 验证性能
        successful_results = [r for r in results if not isinstance(r, Exception)]
        success_rate = len(successful_results) / len(results)
        
        assert success_rate >= 0.8  # 至少80%成功率
        assert duration < 60  # 应该在60秒内完成
        
        # 验证搜索性能
        search_start = datetime.now()
        
        from src.automem.retrieval.semantic_search import SemanticSearchEngine
        search_engine = SemanticSearchEngine(
            server_context.config.retrieval,
            server_context.vector_db,
            server_context.memory_manager.storage
        )
        
        search_results = await search_engine.search(
            query="Python 机器学习",
            limit=10,
            similarity_threshold=0.3
        )
        
        search_end = datetime.now()
        search_duration = (search_end - search_start).total_seconds()
        
        assert len(search_results) > 0
        assert search_duration < 5  # 搜索应该在5秒内完成
