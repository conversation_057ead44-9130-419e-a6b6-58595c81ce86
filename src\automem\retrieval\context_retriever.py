"""
上下文检索器

基于会话主题和上下文信息智能检索相关记忆。
"""

import asyncio
from typing import Dict, List, Set, Any, Optional, Tuple
from uuid import UUID
from datetime import datetime, timezone, timedelta
from collections import defaultdict, Counter

from ..config.settings import RetrievalConfig
from ..utils.logging import LoggerMixin
from ..core.memory import Memory, MemoryQuery, MemoryType, MemoryImportance
from ..core.storage import StorageEngine
from .semantic_search import SemanticSearchEngine, SearchResult


class ContextualMemory:
    """上下文记忆"""
    
    def __init__(
        self,
        memory: Memory,
        context_score: float,
        context_reasons: List[str],
        relationship_type: str = "related"
    ):
        self.memory = memory
        self.context_score = context_score
        self.context_reasons = context_reasons
        self.relationship_type = relationship_type  # related, similar, sequential, causal


class ConversationContext:
    """对话上下文"""
    
    def __init__(
        self,
        session_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        topics: Optional[List[str]] = None,
        participants: Optional[List[str]] = None,
        time_window: Optional[Tuple[datetime, datetime]] = None,
        context_metadata: Optional[Dict[str, Any]] = None
    ):
        self.session_id = session_id
        self.conversation_id = conversation_id
        self.topics = topics or []
        self.participants = participants or []
        self.time_window = time_window
        self.context_metadata = context_metadata or {}


class ContextRetriever(LoggerMixin):
    """上下文检索器"""
    
    def __init__(
        self,
        config: RetrievalConfig,
        storage: StorageEngine,
        semantic_search: SemanticSearchEngine
    ):
        self.config = config
        self.storage = storage
        self.semantic_search = semantic_search
        
        # 上下文权重配置
        self.context_weights = {
            "session_relevance": 0.25,      # 会话相关性
            "topic_relevance": 0.25,        # 主题相关性
            "temporal_relevance": 0.20,     # 时间相关性
            "semantic_relevance": 0.15,     # 语义相关性
            "relationship_strength": 0.15,  # 关系强度
        }
        
        # 检索统计
        self.retrieval_stats = {
            "total_retrievals": 0,
            "successful_retrievals": 0,
            "average_context_memories": 0.0,
        }
    
    async def retrieve_context(
        self,
        query: str,
        context: ConversationContext,
        limit: int = None,
        include_related: bool = True,
        time_window_hours: int = 24
    ) -> List[ContextualMemory]:
        """检索上下文相关的记忆"""
        
        limit = limit or self.config.context_window_size
        
        self.logger.debug(f"检索上下文: '{query}' (限制: {limit})")
        
        # 更新统计
        self.retrieval_stats["total_retrievals"] += 1
        
        try:
            # 收集候选记忆
            candidates = await self._collect_candidate_memories(
                query, context, time_window_hours
            )
            
            if not candidates:
                self.logger.debug("未找到候选记忆")
                return []
            
            # 计算上下文得分
            contextual_memories = []
            for memory in candidates:
                context_score, reasons = await self._calculate_context_score(
                    memory, query, context
                )
                
                if context_score >= 0.3:  # 最低上下文相关性阈值
                    relationship_type = await self._determine_relationship_type(
                        memory, query, context
                    )
                    
                    contextual_memory = ContextualMemory(
                        memory=memory,
                        context_score=context_score,
                        context_reasons=reasons,
                        relationship_type=relationship_type
                    )
                    
                    contextual_memories.append(contextual_memory)
            
            # 按上下文得分排序
            contextual_memories.sort(key=lambda x: x.context_score, reverse=True)
            
            # 限制结果数量
            final_results = contextual_memories[:limit]
            
            # 如果需要，添加相关记忆
            if include_related and final_results:
                related_memories = await self._find_related_memories(
                    final_results, limit // 2
                )
                final_results.extend(related_memories)
            
            # 更新统计
            if final_results:
                self.retrieval_stats["successful_retrievals"] += 1
                self.retrieval_stats["average_context_memories"] = (
                    (self.retrieval_stats["average_context_memories"] * 
                     (self.retrieval_stats["successful_retrievals"] - 1) + 
                     len(final_results)) / self.retrieval_stats["successful_retrievals"]
                )
            
            self.logger.debug(f"上下文检索完成，返回 {len(final_results)} 个记忆")
            return final_results
            
        except Exception as e:
            self.logger.error(f"上下文检索失败: {e}")
            return []
    
    async def _collect_candidate_memories(
        self,
        query: str,
        context: ConversationContext,
        time_window_hours: int
    ) -> List[Memory]:
        """收集候选记忆"""
        candidates = set()
        
        # 1. 基于会话的记忆
        if context.session_id:
            session_memories = await self._get_session_memories(
                context.session_id, time_window_hours
            )
            candidates.update(session_memories)
        
        # 2. 基于对话的记忆
        if context.conversation_id:
            conversation_memories = await self._get_conversation_memories(
                context.conversation_id
            )
            candidates.update(conversation_memories)
        
        # 3. 基于主题的记忆
        if context.topics:
            topic_memories = await self._get_topic_memories(context.topics)
            candidates.update(topic_memories)
        
        # 4. 语义相似的记忆
        semantic_results = await self.semantic_search.search(
            query=query,
            limit=self.config.max_results,
            similarity_threshold=0.5
        )
        semantic_memories = [r.memory for r in semantic_results]
        candidates.update(semantic_memories)
        
        # 5. 时间窗口内的记忆
        if context.time_window:
            time_memories = await self._get_time_window_memories(context.time_window)
            candidates.update(time_memories)
        
        return list(candidates)
    
    async def _get_session_memories(
        self, 
        session_id: str, 
        time_window_hours: int
    ) -> List[Memory]:
        """获取会话相关记忆"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=time_window_hours)
        
        query = MemoryQuery(
            session_id=session_id,
            created_after=cutoff_time,
            sort_by="created_at",
            sort_order="desc",
            limit=50
        )
        
        return await self.storage.search_memories(query)
    
    async def _get_conversation_memories(self, conversation_id: str) -> List[Memory]:
        """获取对话相关记忆"""
        query = MemoryQuery(
            conversation_id=conversation_id,
            sort_by="created_at",
            sort_order="desc",
            limit=30
        )
        
        return await self.storage.search_memories(query)
    
    async def _get_topic_memories(self, topics: List[str]) -> List[Memory]:
        """获取主题相关记忆"""
        # 使用标签搜索
        query = MemoryQuery(
            tags=topics,
            sort_by="relevance_score",
            sort_order="desc",
            limit=40
        )
        
        return await self.storage.search_memories(query)
    
    async def _get_time_window_memories(
        self, 
        time_window: Tuple[datetime, datetime]
    ) -> List[Memory]:
        """获取时间窗口内的记忆"""
        start_time, end_time = time_window
        
        query = MemoryQuery(
            created_after=start_time,
            created_before=end_time,
            sort_by="created_at",
            sort_order="desc",
            limit=30
        )
        
        return await self.storage.search_memories(query)
    
    async def _calculate_context_score(
        self,
        memory: Memory,
        query: str,
        context: ConversationContext
    ) -> Tuple[float, List[str]]:
        """计算上下文得分"""
        score = 0.0
        reasons = []
        
        # 会话相关性
        session_score = 0.0
        if context.session_id and memory.session_id == context.session_id:
            session_score = 1.0
            reasons.append("同一会话")
        elif memory.session_id:
            session_score = 0.3
            reasons.append("相关会话")
        
        score += session_score * self.context_weights["session_relevance"]
        
        # 主题相关性
        topic_score = 0.0
        if context.topics and memory.tags:
            common_topics = set(context.topics) & memory.tags
            if common_topics:
                topic_score = len(common_topics) / len(context.topics)
                reasons.append(f"主题匹配: {', '.join(list(common_topics)[:2])}")
        
        score += topic_score * self.context_weights["topic_relevance"]
        
        # 时间相关性
        temporal_score = await self._calculate_temporal_relevance(memory, context)
        if temporal_score > 0.5:
            reasons.append("时间相关")
        
        score += temporal_score * self.context_weights["temporal_relevance"]
        
        # 语义相关性
        semantic_score = await self._calculate_semantic_relevance(memory, query)
        if semantic_score > 0.6:
            reasons.append("语义相关")
        
        score += semantic_score * self.context_weights["semantic_relevance"]
        
        # 关系强度
        relationship_score = await self._calculate_relationship_strength(memory, context)
        if relationship_score > 0.5:
            reasons.append("强关联")
        
        score += relationship_score * self.context_weights["relationship_strength"]
        
        return min(score, 1.0), reasons
    
    async def _calculate_temporal_relevance(
        self,
        memory: Memory,
        context: ConversationContext
    ) -> float:
        """计算时间相关性"""
        now = datetime.now(timezone.utc)
        
        # 基于记忆年龄的相关性
        age_hours = (now - memory.created_at).total_seconds() / 3600
        
        if age_hours <= 1:
            base_score = 1.0
        elif age_hours <= 6:
            base_score = 0.8
        elif age_hours <= 24:
            base_score = 0.6
        elif age_hours <= 168:  # 一周
            base_score = 0.4
        else:
            base_score = 0.2
        
        # 如果有明确的时间窗口
        if context.time_window:
            start_time, end_time = context.time_window
            if start_time <= memory.created_at <= end_time:
                base_score += 0.3
        
        return min(base_score, 1.0)
    
    async def _calculate_semantic_relevance(self, memory: Memory, query: str) -> float:
        """计算语义相关性"""
        # 简单的关键词匹配
        query_words = set(query.lower().split())
        memory_words = set(memory.content.lower().split())
        
        if not query_words:
            return 0.0
        
        common_words = query_words & memory_words
        return len(common_words) / len(query_words)
    
    async def _calculate_relationship_strength(
        self,
        memory: Memory,
        context: ConversationContext
    ) -> float:
        """计算关系强度"""
        score = 0.0
        
        # 基于访问频率
        if memory.access_count > 5:
            score += 0.3
        elif memory.access_count > 2:
            score += 0.2
        
        # 基于重要性
        importance_weights = {
            MemoryImportance.CRITICAL: 1.0,
            MemoryImportance.HIGH: 0.8,
            MemoryImportance.MEDIUM: 0.6,
            MemoryImportance.LOW: 0.4,
            MemoryImportance.MINIMAL: 0.2,
        }
        score += importance_weights.get(memory.importance, 0.5) * 0.4
        
        # 基于关联记忆数量
        if memory.related_memories:
            relation_score = min(len(memory.related_memories) / 5.0, 0.3)
            score += relation_score
        
        return min(score, 1.0)
    
    async def _determine_relationship_type(
        self,
        memory: Memory,
        query: str,
        context: ConversationContext
    ) -> str:
        """确定关系类型"""
        
        # 如果是同一会话，可能是顺序关系
        if context.session_id and memory.session_id == context.session_id:
            return "sequential"
        
        # 如果有共同主题，是相关关系
        if context.topics and memory.tags:
            common_topics = set(context.topics) & memory.tags
            if common_topics:
                return "topical"
        
        # 如果语义相似度高，是相似关系
        semantic_score = await self._calculate_semantic_relevance(memory, query)
        if semantic_score > 0.7:
            return "similar"
        
        # 默认为相关关系
        return "related"
    
    async def _find_related_memories(
        self,
        contextual_memories: List[ContextualMemory],
        limit: int
    ) -> List[ContextualMemory]:
        """查找相关记忆"""
        related_memories = []
        
        for contextual_memory in contextual_memories[:3]:  # 只对前3个记忆查找相关
            memory = contextual_memory.memory
            
            # 查找直接关联的记忆
            for related_id in memory.related_memories:
                related_memory = await self.storage.get_memory(related_id)
                if related_memory:
                    related_contextual = ContextualMemory(
                        memory=related_memory,
                        context_score=contextual_memory.context_score * 0.7,
                        context_reasons=["关联记忆"],
                        relationship_type="linked"
                    )
                    related_memories.append(related_contextual)
                    
                    if len(related_memories) >= limit:
                        break
            
            if len(related_memories) >= limit:
                break
        
        return related_memories[:limit]
    
    async def get_retrieval_stats(self) -> Dict[str, Any]:
        """获取检索统计信息"""
        return dict(self.retrieval_stats)
    
    async def reset_stats(self) -> None:
        """重置检索统计"""
        self.retrieval_stats = {
            "total_retrievals": 0,
            "successful_retrievals": 0,
            "average_context_memories": 0.0,
        }
        self.logger.info("上下文检索统计已重置")
