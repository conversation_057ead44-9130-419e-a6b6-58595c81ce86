#!/usr/bin/env python3
"""
AutoMem GUI 可执行文件构建脚本
使用 PyInstaller 构建 Windows 可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查 PyInstaller 是否安装"""
    try:
        import PyInstaller
        return True
    except ImportError:
        print("PyInstaller 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            return True
        except subprocess.CalledProcessError:
            print("PyInstaller 安装失败")
            return False

def create_spec_file():
    """创建 PyInstaller 规格文件"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['start_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.example.yaml', '.'),
        ('requirements-windows.txt', '.'),
        ('requirements-gui.txt', '.'),
        ('docs', 'docs'),
        ('assets', 'assets'),
    ],
    hiddenimports=[
        'customtkinter',
        'psutil',
        'yaml',
        'PIL',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AutoMem_Manager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''
    
    with open('automem_gui.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("已创建 PyInstaller 规格文件")

def build_executable():
    """构建可执行文件"""
    try:
        print("正在构建可执行文件...")
        
        # 运行 PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "automem_gui.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 可执行文件构建成功！")
            print("📁 输出目录: dist/")
            
            # 检查输出文件
            exe_path = Path("dist/AutoMem_Manager.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / 1024 / 1024
                print(f"📦 文件大小: {size_mb:.1f} MB")
                print(f"🎯 可执行文件: {exe_path.absolute()}")
            
            return True
        else:
            print("❌ 构建失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_installer():
    """创建安装包（可选）"""
    try:
        # 检查是否有 NSIS 或 Inno Setup
        print("正在创建安装包...")
        
        # 创建安装脚本目录
        installer_dir = Path("installer")
        installer_dir.mkdir(exist_ok=True)
        
        # 复制必要文件
        dist_dir = Path("dist")
        if dist_dir.exists():
            shutil.copytree(dist_dir, installer_dir / "dist", dirs_exist_ok=True)
        
        # 创建简单的批处理安装脚本
        install_script = '''@echo off
echo AutoMem 管理器安装程序
echo.

set INSTALL_DIR=%PROGRAMFILES%\\AutoMem

echo 正在创建安装目录...
mkdir "%INSTALL_DIR%" 2>nul

echo 正在复制文件...
xcopy "dist\\*" "%INSTALL_DIR%\\" /E /I /Y

echo 正在创建桌面快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\AutoMem 管理器.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\AutoMem_Manager.exe'; $Shortcut.Save()"

echo 正在创建开始菜单快捷方式...
mkdir "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\AutoMem" 2>nul
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\AutoMem\\AutoMem 管理器.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\AutoMem_Manager.exe'; $Shortcut.Save()"

echo.
echo ✅ 安装完成！
echo 📁 安装目录: %INSTALL_DIR%
echo 🖥️ 桌面快捷方式已创建
echo 📋 开始菜单快捷方式已创建
echo.
pause
'''
        
        with open(installer_dir / "install.bat", 'w', encoding='gbk') as f:
            f.write(install_script)
        
        # 创建卸载脚本
        uninstall_script = '''@echo off
echo AutoMem 管理器卸载程序
echo.

set INSTALL_DIR=%PROGRAMFILES%\\AutoMem

echo 正在删除文件...
rmdir /s /q "%INSTALL_DIR%" 2>nul

echo 正在删除快捷方式...
del "%USERPROFILE%\\Desktop\\AutoMem 管理器.lnk" 2>nul
rmdir /s /q "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\AutoMem" 2>nul

echo.
echo ✅ 卸载完成！
echo.
pause
'''
        
        with open(installer_dir / "uninstall.bat", 'w', encoding='gbk') as f:
            f.write(uninstall_script)
        
        print("✅ 安装包创建完成！")
        print(f"📁 安装包目录: {installer_dir.absolute()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建安装包失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 AutoMem GUI 可执行文件构建工具")
    print("=" * 50)
    
    # 检查 PyInstaller
    if not check_pyinstaller():
        print("❌ PyInstaller 检查失败")
        return
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if build_executable():
        print("\n🎉 构建成功！")
        
        # 询问是否创建安装包
        try:
            choice = input("\n是否创建安装包？(y/N): ").lower().strip()
            if choice in ['y', 'yes']:
                create_installer()
        except KeyboardInterrupt:
            print("\n👋 构建完成")
    else:
        print("\n❌ 构建失败")
    
    print("\n📋 构建完成！")
    print("📁 检查 dist/ 目录获取可执行文件")
    print("📁 检查 installer/ 目录获取安装包")

if __name__ == "__main__":
    main()
