"""
语义搜索引擎

基于向量相似度的语义搜索功能。
"""

import asyncio
from typing import Dict, List, Set, Any, Optional, Tuple
from uuid import UUID
from datetime import datetime, timezone, timedelta

from ..config.settings import RetrievalConfig
from ..utils.logging import LoggerMixin
from ..core.memory import Memory, MemoryQuery, MemoryType, MemoryImportance
from ..core.vector_db import VectorDatabase
from ..core.storage import StorageEngine


class SearchResult:
    """搜索结果"""
    
    def __init__(
        self,
        memory: Memory,
        similarity_score: float,
        relevance_score: float,
        rank: int,
        match_reasons: List[str]
    ):
        self.memory = memory
        self.similarity_score = similarity_score
        self.relevance_score = relevance_score
        self.rank = rank
        self.match_reasons = match_reasons
    
    @property
    def combined_score(self) -> float:
        """综合得分"""
        return (self.similarity_score * 0.7 + self.relevance_score * 0.3)


class SemanticSearchEngine(LoggerMixin):
    """语义搜索引擎"""
    
    def __init__(
        self, 
        config: RetrievalConfig, 
        vector_db: VectorDatabase, 
        storage: StorageEngine
    ):
        self.config = config
        self.vector_db = vector_db
        self.storage = storage
        
        # 搜索统计
        self.search_stats = {
            "total_searches": 0,
            "successful_searches": 0,
            "average_results": 0.0,
            "average_similarity": 0.0,
        }
    
    async def search(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = None,
        similarity_threshold: float = None,
        include_context: bool = True,
    ) -> List[SearchResult]:
        """执行语义搜索"""
        
        # 使用配置的默认值
        limit = limit or self.config.max_results
        similarity_threshold = similarity_threshold or self.config.similarity_threshold
        
        self.logger.debug(f"语义搜索: '{query}' (限制: {limit}, 阈值: {similarity_threshold})")
        
        # 更新搜索统计
        self.search_stats["total_searches"] += 1
        
        try:
            # 向量搜索
            vector_results = await self.vector_db.search_similar(
                query=query,
                limit=limit * 2,  # 获取更多候选结果
                similarity_threshold=similarity_threshold * 0.8,  # 降低阈值以获取更多候选
                filters=self._convert_filters(filters),
            )
            
            if not vector_results:
                self.logger.debug("向量搜索无结果")
                return []
            
            # 获取完整的记忆对象
            memories = []
            for memory_id, similarity in vector_results:
                memory = await self.storage.get_memory(memory_id)
                if memory:
                    memories.append((memory, similarity))
            
            # 计算相关性得分并排序
            search_results = []
            for i, (memory, similarity) in enumerate(memories):
                if similarity >= similarity_threshold:
                    relevance_score = await self._calculate_relevance_score(
                        memory, query, filters, include_context
                    )
                    
                    match_reasons = await self._analyze_match_reasons(
                        memory, query, similarity, relevance_score
                    )
                    
                    result = SearchResult(
                        memory=memory,
                        similarity_score=similarity,
                        relevance_score=relevance_score,
                        rank=i + 1,
                        match_reasons=match_reasons
                    )
                    
                    search_results.append(result)
            
            # 按综合得分排序
            search_results.sort(key=lambda x: x.combined_score, reverse=True)
            
            # 限制结果数量
            final_results = search_results[:limit]
            
            # 更新统计信息
            if final_results:
                self.search_stats["successful_searches"] += 1
                self.search_stats["average_results"] = (
                    (self.search_stats["average_results"] * (self.search_stats["successful_searches"] - 1) + 
                     len(final_results)) / self.search_stats["successful_searches"]
                )
                avg_similarity = sum(r.similarity_score for r in final_results) / len(final_results)
                self.search_stats["average_similarity"] = (
                    (self.search_stats["average_similarity"] * (self.search_stats["successful_searches"] - 1) + 
                     avg_similarity) / self.search_stats["successful_searches"]
                )
            
            self.logger.debug(f"搜索完成，返回 {len(final_results)} 个结果")
            return final_results
            
        except Exception as e:
            self.logger.error(f"语义搜索失败: {e}")
            return []
    
    async def _calculate_relevance_score(
        self,
        memory: Memory,
        query: str,
        filters: Optional[Dict[str, Any]],
        include_context: bool
    ) -> float:
        """计算相关性得分"""
        score = 0.0
        
        # 基础文本匹配得分
        query_lower = query.lower()
        content_lower = memory.content.lower()
        
        # 关键词匹配
        query_words = set(query_lower.split())
        content_words = set(content_lower.split())
        common_words = query_words.intersection(content_words)
        
        if query_words:
            keyword_score = len(common_words) / len(query_words)
            score += keyword_score * 0.3
        
        # 重要性得分
        importance_weights = {
            MemoryImportance.CRITICAL: 1.0,
            MemoryImportance.HIGH: 0.8,
            MemoryImportance.MEDIUM: 0.6,
            MemoryImportance.LOW: 0.4,
            MemoryImportance.MINIMAL: 0.2,
        }
        score += importance_weights.get(memory.importance, 0.5) * 0.2
        
        # 时间相关性得分
        time_score = await self._calculate_time_relevance(memory)
        score += time_score * self.config.time_decay_factor
        
        # 访问频率得分
        if memory.access_count > 0:
            access_score = min(memory.access_count / 10.0, 1.0)
            score += access_score * 0.1
        
        # 标签匹配得分
        if memory.tags:
            tag_matches = sum(1 for tag in memory.tags if tag.lower() in query_lower)
            if tag_matches > 0:
                tag_score = min(tag_matches / len(memory.tags), 1.0)
                score += tag_score * 0.2
        
        # 上下文相关性
        if include_context and filters:
            context_score = await self._calculate_context_relevance(memory, filters)
            score += context_score * 0.2
        
        return min(score, 1.0)
    
    async def _calculate_time_relevance(self, memory: Memory) -> float:
        """计算时间相关性"""
        now = datetime.now(timezone.utc)
        age = now - memory.created_at
        age_days = age.total_seconds() / 86400
        
        # 时间衰减函数：越新的记忆得分越高
        if age_days <= 1:
            return 1.0
        elif age_days <= 7:
            return 0.8
        elif age_days <= 30:
            return 0.6
        elif age_days <= 90:
            return 0.4
        elif age_days <= 365:
            return 0.2
        else:
            return 0.1
    
    async def _calculate_context_relevance(
        self, 
        memory: Memory, 
        filters: Dict[str, Any]
    ) -> float:
        """计算上下文相关性"""
        score = 0.0
        
        # 会话相关性
        if filters.get("session_id") and memory.session_id == filters["session_id"]:
            score += 0.5
        
        # 对话相关性
        if filters.get("conversation_id") and memory.conversation_id == filters["conversation_id"]:
            score += 0.3
        
        # 类型相关性
        if filters.get("memory_types") and memory.memory_type in filters["memory_types"]:
            score += 0.2
        
        # 分类相关性
        filter_categories = filters.get("categories", [])
        if filter_categories and memory.categories:
            common_categories = set(filter_categories).intersection(memory.categories)
            if common_categories:
                score += len(common_categories) / len(filter_categories) * 0.3
        
        return min(score, 1.0)
    
    async def _analyze_match_reasons(
        self,
        memory: Memory,
        query: str,
        similarity_score: float,
        relevance_score: float
    ) -> List[str]:
        """分析匹配原因"""
        reasons = []
        
        # 相似度匹配
        if similarity_score >= 0.8:
            reasons.append("高语义相似度")
        elif similarity_score >= 0.6:
            reasons.append("中等语义相似度")
        
        # 关键词匹配
        query_words = set(query.lower().split())
        content_words = set(memory.content.lower().split())
        common_words = query_words.intersection(content_words)
        
        if common_words:
            reasons.append(f"关键词匹配: {', '.join(list(common_words)[:3])}")
        
        # 标签匹配
        if memory.tags:
            matching_tags = [tag for tag in memory.tags if tag.lower() in query.lower()]
            if matching_tags:
                reasons.append(f"标签匹配: {', '.join(matching_tags[:2])}")
        
        # 重要性
        if memory.importance in [MemoryImportance.CRITICAL, MemoryImportance.HIGH]:
            reasons.append(f"高重要性 ({memory.importance.value})")
        
        # 时间相关性
        if memory.is_recent(hours=24):
            reasons.append("最近记忆")
        
        # 访问频率
        if memory.access_count > 5:
            reasons.append("高访问频率")
        
        return reasons
    
    def _convert_filters(self, filters: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """转换过滤器格式为向量数据库格式"""
        if not filters:
            return None
        
        vector_filters = {}
        
        # 记忆类型过滤
        if "memory_types" in filters:
            types = [t.value if hasattr(t, 'value') else str(t) for t in filters["memory_types"]]
            vector_filters["memory_type"] = types
        
        # 重要性过滤
        if "importance_levels" in filters:
            levels = [l.value if hasattr(l, 'value') else str(l) for l in filters["importance_levels"]]
            vector_filters["importance"] = levels
        
        # 会话过滤
        if "session_id" in filters:
            vector_filters["session_id"] = filters["session_id"]
        
        # 对话过滤
        if "conversation_id" in filters:
            vector_filters["conversation_id"] = filters["conversation_id"]
        
        return vector_filters if vector_filters else None
    
    async def search_by_memory(
        self,
        reference_memory: Memory,
        limit: int = None,
        exclude_self: bool = True
    ) -> List[SearchResult]:
        """基于参考记忆搜索相似记忆"""
        
        # 使用记忆内容作为查询
        query = reference_memory.content
        
        # 添加标签信息
        if reference_memory.tags:
            query += " " + " ".join(reference_memory.tags)
        
        # 执行搜索
        results = await self.search(
            query=query,
            filters={
                "memory_types": [reference_memory.memory_type],
            },
            limit=limit,
        )
        
        # 排除自身
        if exclude_self:
            results = [r for r in results if r.memory.id != reference_memory.id]
        
        return results
    
    async def get_search_stats(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        return dict(self.search_stats)
    
    async def reset_stats(self) -> None:
        """重置搜索统计"""
        self.search_stats = {
            "total_searches": 0,
            "successful_searches": 0,
            "average_results": 0.0,
            "average_similarity": 0.0,
        }
        self.logger.info("搜索统计已重置")
