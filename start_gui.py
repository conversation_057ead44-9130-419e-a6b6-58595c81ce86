#!/usr/bin/env python3
"""
AutoMem GUI 启动器
自动检查依赖并启动 GUI 管理器
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_and_install_dependencies():
    """检查并安装GUI依赖"""
    try:
        # 检查基础依赖
        import customtkinter
        import psutil
        import yaml
        return True
    except ImportError as e:
        # 显示安装提示
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        result = messagebox.askyesno(
            "缺少依赖",
            f"GUI管理器需要额外的依赖包。\n\n缺少: {e}\n\n是否自动安装？"
        )
        
        if result:
            try:
                # 安装GUI依赖
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", 
                    "-r", "requirements-gui.txt"
                ])
                messagebox.showinfo("成功", "依赖安装完成，正在启动GUI...")
                return True
            except subprocess.CalledProcessError:
                messagebox.showerror(
                    "安装失败", 
                    "自动安装失败。请手动运行：\npip install -r requirements-gui.txt"
                )
                return False
        else:
            messagebox.showinfo(
                "取消", 
                "请手动安装依赖：\npip install -r requirements-gui.txt"
            )
            return False

def main():
    """主函数"""
    print("AutoMem GUI 管理器启动中...")
    
    # 检查依赖
    if not check_and_install_dependencies():
        print("依赖检查失败，退出...")
        return
    
    # 启动GUI管理器
    try:
        from automem_manager import AutoMemManager
        
        print("正在启动GUI界面...")
        app = AutoMemManager()
        app.root.mainloop()
        
    except Exception as e:
        # 如果GUI启动失败，显示错误信息
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动失败", f"GUI启动失败：\n{e}")
        print(f"GUI启动失败: {e}")

if __name__ == "__main__":
    main()
