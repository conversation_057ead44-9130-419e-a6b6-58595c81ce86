"""
存储引擎实现

提供记忆数据的持久化存储功能。
"""

import json
import sqlite3
import asyncio
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from uuid import UUID
import aiosqlite

from ..config.settings import StorageConfig
from ..utils.logging import LoggerMixin
from .memory import Memory, MemoryQuery, MemoryManager


class StorageEngine(LoggerMixin):
    """存储引擎"""
    
    def __init__(self, config: StorageConfig):
        self.config = config
        self.db_path = config.data_dir / "memories.db"
        self._ensure_data_dir()
    
    def _ensure_data_dir(self) -> None:
        """确保数据目录存在"""
        self.config.data_dir.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"数据目录: {self.config.data_dir}")
    
    async def initialize(self) -> None:
        """初始化存储引擎"""
        await self._create_tables()
        self.logger.info("存储引擎初始化完成")
    
    async def _create_tables(self) -> None:
        """创建数据库表"""
        async with aiosqlite.connect(self.db_path) as db:
            # 记忆主表
            await db.execute("""
                CREATE TABLE IF NOT EXISTS memories (
                    id TEXT PRIMARY KEY,
                    content TEXT NOT NULL,
                    summary TEXT,
                    memory_type TEXT NOT NULL,
                    importance TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    accessed_at TEXT NOT NULL,
                    session_id TEXT,
                    conversation_id TEXT,
                    source TEXT,
                    access_count INTEGER DEFAULT 0,
                    relevance_score REAL DEFAULT 0.0,
                    context TEXT,  -- JSON
                    metadata TEXT  -- JSON
                )
            """)
            
            # 标签表
            await db.execute("""
                CREATE TABLE IF NOT EXISTS memory_tags (
                    memory_id TEXT,
                    tag TEXT,
                    PRIMARY KEY (memory_id, tag),
                    FOREIGN KEY (memory_id) REFERENCES memories (id) ON DELETE CASCADE
                )
            """)
            
            # 分类表
            await db.execute("""
                CREATE TABLE IF NOT EXISTS memory_categories (
                    memory_id TEXT,
                    category TEXT,
                    PRIMARY KEY (memory_id, category),
                    FOREIGN KEY (memory_id) REFERENCES memories (id) ON DELETE CASCADE
                )
            """)
            
            # 关联表
            await db.execute("""
                CREATE TABLE IF NOT EXISTS memory_relations (
                    memory_id TEXT,
                    related_memory_id TEXT,
                    relation_type TEXT DEFAULT 'related',
                    strength REAL DEFAULT 1.0,
                    PRIMARY KEY (memory_id, related_memory_id),
                    FOREIGN KEY (memory_id) REFERENCES memories (id) ON DELETE CASCADE,
                    FOREIGN KEY (related_memory_id) REFERENCES memories (id) ON DELETE CASCADE
                )
            """)
            
            # 创建索引
            await db.execute("CREATE INDEX IF NOT EXISTS idx_memories_created_at ON memories (created_at)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_memories_updated_at ON memories (updated_at)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_memories_accessed_at ON memories (accessed_at)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_memories_type ON memories (memory_type)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_memories_importance ON memories (importance)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_memories_session ON memories (session_id)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_memories_conversation ON memories (conversation_id)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_tags_tag ON memory_tags (tag)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_categories_category ON memory_categories (category)")
            
            await db.commit()
    
    async def store_memory(self, memory: Memory) -> Memory:
        """存储记忆"""
        async with aiosqlite.connect(self.db_path) as db:
            # 插入主记录
            await db.execute("""
                INSERT OR REPLACE INTO memories (
                    id, content, summary, memory_type, importance,
                    created_at, updated_at, accessed_at,
                    session_id, conversation_id, source,
                    access_count, relevance_score, context, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                str(memory.id),
                memory.content,
                memory.summary,
                memory.memory_type.value,
                memory.importance.value,
                memory.created_at.isoformat(),
                memory.updated_at.isoformat(),
                memory.accessed_at.isoformat(),
                memory.session_id,
                memory.conversation_id,
                memory.source,
                memory.access_count,
                memory.relevance_score,
                json.dumps(memory.context),
                json.dumps(memory.metadata),
            ))
            
            # 删除旧的标签和分类
            await db.execute("DELETE FROM memory_tags WHERE memory_id = ?", (str(memory.id),))
            await db.execute("DELETE FROM memory_categories WHERE memory_id = ?", (str(memory.id),))
            await db.execute("DELETE FROM memory_relations WHERE memory_id = ?", (str(memory.id),))
            
            # 插入标签
            for tag in memory.tags:
                await db.execute(
                    "INSERT INTO memory_tags (memory_id, tag) VALUES (?, ?)",
                    (str(memory.id), tag)
                )
            
            # 插入分类
            for category in memory.categories:
                await db.execute(
                    "INSERT INTO memory_categories (memory_id, category) VALUES (?, ?)",
                    (str(memory.id), category)
                )
            
            # 插入关联
            for related_id in memory.related_memories:
                await db.execute(
                    "INSERT INTO memory_relations (memory_id, related_memory_id) VALUES (?, ?)",
                    (str(memory.id), str(related_id))
                )
            
            await db.commit()
        
        self.logger.debug(f"存储记忆: {memory.id}")
        return memory

    async def get_memory(self, memory_id: UUID) -> Optional[Memory]:
        """获取记忆"""
        async with aiosqlite.connect(self.db_path) as db:
            # 获取主记录
            async with db.execute(
                "SELECT * FROM memories WHERE id = ?",
                (str(memory_id),)
            ) as cursor:
                row = await cursor.fetchone()
                if not row:
                    return None

            # 构建记忆对象
            memory_data = dict(row)

            # 获取标签
            async with db.execute(
                "SELECT tag FROM memory_tags WHERE memory_id = ?",
                (str(memory_id),)
            ) as cursor:
                tags = {row[0] for row in await cursor.fetchall()}

            # 获取分类
            async with db.execute(
                "SELECT category FROM memory_categories WHERE memory_id = ?",
                (str(memory_id),)
            ) as cursor:
                categories = {row[0] for row in await cursor.fetchall()}

            # 获取关联
            async with db.execute(
                "SELECT related_memory_id FROM memory_relations WHERE memory_id = ?",
                (str(memory_id),)
            ) as cursor:
                related_memories = {UUID(row[0]) for row in await cursor.fetchall()}

            # 构建Memory对象
            memory = self._row_to_memory(memory_data, tags, categories, related_memories)

            # 更新访问信息
            memory.update_access()
            await self._update_access_info(memory)

            return memory

    async def update_memory(self, memory: Memory) -> Memory:
        """更新记忆"""
        memory.updated_at = datetime.now(timezone.utc)
        return await self.store_memory(memory)

    async def delete_memory(self, memory_id: UUID) -> bool:
        """删除记忆"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(
                "DELETE FROM memories WHERE id = ?",
                (str(memory_id),)
            )
            await db.commit()

            deleted = cursor.rowcount > 0
            if deleted:
                self.logger.debug(f"删除记忆: {memory_id}")

            return deleted

    async def search_memories(self, query: MemoryQuery) -> List[Memory]:
        """搜索记忆"""
        conditions = []
        params = []

        # 构建查询条件
        if query.content:
            conditions.append("(content LIKE ? OR summary LIKE ?)")
            search_term = f"%{query.content}%"
            params.extend([search_term, search_term])

        if query.memory_types:
            placeholders = ",".join("?" * len(query.memory_types))
            conditions.append(f"memory_type IN ({placeholders})")
            params.extend([mt.value for mt in query.memory_types])

        if query.importance_levels:
            placeholders = ",".join("?" * len(query.importance_levels))
            conditions.append(f"importance IN ({placeholders})")
            params.extend([il.value for il in query.importance_levels])

        if query.created_after:
            conditions.append("created_at >= ?")
            params.append(query.created_after.isoformat())

        if query.created_before:
            conditions.append("created_at <= ?")
            params.append(query.created_before.isoformat())

        if query.session_id:
            conditions.append("session_id = ?")
            params.append(query.session_id)

        if query.conversation_id:
            conditions.append("conversation_id = ?")
            params.append(query.conversation_id)

        # 构建SQL查询
        base_query = "SELECT DISTINCT m.* FROM memories m"

        # 处理标签查询
        if query.tags:
            base_query += " JOIN memory_tags mt ON m.id = mt.memory_id"
            placeholders = ",".join("?" * len(query.tags))
            conditions.append(f"mt.tag IN ({placeholders})")
            params.extend(query.tags)

        # 处理分类查询
        if query.categories:
            base_query += " JOIN memory_categories mc ON m.id = mc.memory_id"
            placeholders = ",".join("?" * len(query.categories))
            conditions.append(f"mc.category IN ({placeholders})")
            params.extend(query.categories)

        # 添加WHERE子句
        if conditions:
            base_query += " WHERE " + " AND ".join(conditions)

        # 添加排序
        base_query += f" ORDER BY {query.sort_by} {query.sort_order.upper()}"

        # 添加限制
        base_query += f" LIMIT {query.limit} OFFSET {query.offset}"

        # 执行查询
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            async with db.execute(base_query, params) as cursor:
                rows = await cursor.fetchall()

        # 转换为Memory对象
        memories = []
        for row in rows:
            memory = await self._load_full_memory(UUID(row['id']))
            if memory:
                memories.append(memory)

        return memories

    async def get_recent_memories(self, hours: int = 24, limit: int = 10) -> List[Memory]:
        """获取近期记忆"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)

        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            async with db.execute("""
                SELECT * FROM memories
                WHERE created_at >= ?
                ORDER BY created_at DESC
                LIMIT ?
            """, (cutoff_time.isoformat(), limit)) as cursor:
                rows = await cursor.fetchall()

        memories = []
        for row in rows:
            memory = await self._load_full_memory(UUID(row['id']))
            if memory:
                memories.append(memory)

        return memories

    async def get_related_memories(self, memory_id: UUID, limit: int = 5) -> List[Memory]:
        """获取相关记忆"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            async with db.execute("""
                SELECT m.* FROM memories m
                JOIN memory_relations mr ON m.id = mr.related_memory_id
                WHERE mr.memory_id = ?
                ORDER BY mr.strength DESC
                LIMIT ?
            """, (str(memory_id), limit)) as cursor:
                rows = await cursor.fetchall()

        memories = []
        for row in rows:
            memory = await self._load_full_memory(UUID(row['id']))
            if memory:
                memories.append(memory)

        return memories

    async def cleanup_old_memories(self, days: int = 30) -> int:
        """清理旧记忆"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=days)

        async with aiosqlite.connect(self.db_path) as db:
            # 只删除低重要性的旧记忆
            cursor = await db.execute("""
                DELETE FROM memories
                WHERE created_at < ?
                AND importance IN ('low', 'minimal')
                AND access_count < 5
            """, (cutoff_time.isoformat(),))

            await db.commit()
            deleted_count = cursor.rowcount

        self.logger.info(f"清理了 {deleted_count} 个旧记忆")
        return deleted_count

    async def get_memory_stats(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        async with aiosqlite.connect(self.db_path) as db:
            # 总数统计
            async with db.execute("SELECT COUNT(*) FROM memories") as cursor:
                total_count = (await cursor.fetchone())[0]

            # 按类型统计
            async with db.execute("""
                SELECT memory_type, COUNT(*)
                FROM memories
                GROUP BY memory_type
            """) as cursor:
                type_stats = dict(await cursor.fetchall())

            # 按重要性统计
            async with db.execute("""
                SELECT importance, COUNT(*)
                FROM memories
                GROUP BY importance
            """) as cursor:
                importance_stats = dict(await cursor.fetchall())

            # 最近活动
            recent_cutoff = (datetime.now(timezone.utc) - timedelta(days=7)).isoformat()
            async with db.execute("""
                SELECT COUNT(*) FROM memories
                WHERE created_at >= ?
            """, (recent_cutoff,)) as cursor:
                recent_count = (await cursor.fetchone())[0]

        return {
            "total_memories": total_count,
            "by_type": type_stats,
            "by_importance": importance_stats,
            "recent_week": recent_count,
        }

    async def _load_full_memory(self, memory_id: UUID) -> Optional[Memory]:
        """加载完整的记忆对象（包含关联数据）"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row

            # 获取主记录
            async with db.execute(
                "SELECT * FROM memories WHERE id = ?",
                (str(memory_id),)
            ) as cursor:
                row = await cursor.fetchone()
                if not row:
                    return None

            memory_data = dict(row)

            # 获取标签
            async with db.execute(
                "SELECT tag FROM memory_tags WHERE memory_id = ?",
                (str(memory_id),)
            ) as cursor:
                tags = {row[0] for row in await cursor.fetchall()}

            # 获取分类
            async with db.execute(
                "SELECT category FROM memory_categories WHERE memory_id = ?",
                (str(memory_id),)
            ) as cursor:
                categories = {row[0] for row in await cursor.fetchall()}

            # 获取关联
            async with db.execute(
                "SELECT related_memory_id FROM memory_relations WHERE memory_id = ?",
                (str(memory_id),)
            ) as cursor:
                related_memories = {UUID(row[0]) for row in await cursor.fetchall()}

            return self._row_to_memory(memory_data, tags, categories, related_memories)

    def _row_to_memory(
        self,
        row_data: Dict[str, Any],
        tags: Set[str],
        categories: Set[str],
        related_memories: Set[UUID]
    ) -> Memory:
        """将数据库行转换为Memory对象"""
        from .memory import MemoryType, MemoryImportance

        return Memory(
            id=UUID(row_data['id']),
            content=row_data['content'],
            summary=row_data['summary'],
            memory_type=MemoryType(row_data['memory_type']),
            importance=MemoryImportance(row_data['importance']),
            tags=tags,
            categories=categories,
            created_at=datetime.fromisoformat(row_data['created_at']),
            updated_at=datetime.fromisoformat(row_data['updated_at']),
            accessed_at=datetime.fromisoformat(row_data['accessed_at']),
            context=json.loads(row_data['context'] or '{}'),
            session_id=row_data['session_id'],
            conversation_id=row_data['conversation_id'],
            related_memories=related_memories,
            source=row_data['source'],
            metadata=json.loads(row_data['metadata'] or '{}'),
            access_count=row_data['access_count'],
            relevance_score=row_data['relevance_score'],
        )

    async def _update_access_info(self, memory: Memory) -> None:
        """更新访问信息"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                UPDATE memories
                SET accessed_at = ?, access_count = ?
                WHERE id = ?
            """, (
                memory.accessed_at.isoformat(),
                memory.access_count,
                str(memory.id)
            ))
            await db.commit()


class SQLiteMemoryManager(MemoryManager):
    """基于SQLite的记忆管理器"""

    def __init__(self, config: StorageConfig):
        super().__init__()
        self.storage = StorageEngine(config)

    async def initialize(self) -> None:
        """初始化管理器"""
        await self.storage.initialize()

    async def create_memory(self, memory: Memory) -> Memory:
        """创建记忆"""
        return await self.storage.store_memory(memory)

    async def get_memory(self, memory_id: UUID) -> Optional[Memory]:
        """获取记忆"""
        return await self.storage.get_memory(memory_id)

    async def update_memory(self, memory: Memory) -> Memory:
        """更新记忆"""
        return await self.storage.update_memory(memory)

    async def delete_memory(self, memory_id: UUID) -> bool:
        """删除记忆"""
        return await self.storage.delete_memory(memory_id)

    async def search_memories(self, query: MemoryQuery) -> List[Memory]:
        """搜索记忆"""
        return await self.storage.search_memories(query)

    async def get_related_memories(self, memory_id: UUID, limit: int = 5) -> List[Memory]:
        """获取相关记忆"""
        return await self.storage.get_related_memories(memory_id, limit)

    async def get_recent_memories(self, hours: int = 24, limit: int = 10) -> List[Memory]:
        """获取近期记忆"""
        return await self.storage.get_recent_memories(hours, limit)

    async def cleanup_old_memories(self, days: int = 30) -> int:
        """清理旧记忆"""
        return await self.storage.cleanup_old_memories(days)

    async def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return await self.storage.get_memory_stats()
