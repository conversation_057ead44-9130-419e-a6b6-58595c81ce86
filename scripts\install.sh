#!/bin/bash

# AutoMem 安装脚本
# 支持 Linux 和 macOS

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 未安装"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    required_version="3.8"
    
    if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
        log_error "Python 版本需要 >= 3.8，当前版本: $python_version"
        exit 1
    fi
    
    log_success "Python 版本检查通过: $python_version"
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装"
        exit 1
    fi
    
    # 检查git
    if ! command -v git &> /dev/null; then
        log_warning "git 未安装，某些功能可能受限"
    fi
}

# 创建虚拟环境
create_venv() {
    log_info "创建Python虚拟环境..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        log_success "虚拟环境创建完成"
    else
        log_info "虚拟环境已存在"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖包..."
    
    # 安装基础依赖
    pip install -r requirements.txt
    
    log_success "依赖包安装完成"
}

# 初始化配置
init_config() {
    log_info "初始化配置文件..."
    
    if [ ! -f "config.yaml" ]; then
        if [ -f "config.example.yaml" ]; then
            cp config.example.yaml config.yaml
            log_success "配置文件已创建: config.yaml"
        else
            log_warning "示例配置文件不存在，将使用默认配置"
        fi
    else
        log_info "配置文件已存在"
    fi
}

# 创建数据目录
create_data_dir() {
    log_info "创建数据目录..."
    
    data_dir="data"
    if [ ! -d "$data_dir" ]; then
        mkdir -p "$data_dir"
        mkdir -p "$data_dir/logs"
        log_success "数据目录创建完成: $data_dir"
    else
        log_info "数据目录已存在"
    fi
}

# 安装CLI工具
install_cli() {
    log_info "安装CLI工具..."
    
    # 创建可执行脚本
    cat > automem << 'EOF'
#!/bin/bash
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"
source venv/bin/activate
python -m src.automem.cli "$@"
EOF
    
    chmod +x automem
    
    # 添加到PATH（可选）
    if [[ ":$PATH:" != *":$(pwd):"* ]]; then
        log_info "要将AutoMem添加到PATH，请运行："
        echo "export PATH=\"\$PATH:$(pwd)\""
        echo "或将上述命令添加到您的 ~/.bashrc 或 ~/.zshrc 文件中"
    fi
    
    log_success "CLI工具安装完成"
}

# 运行测试
run_tests() {
    log_info "运行安装测试..."
    
    # 测试导入
    python3 -c "
import sys
sys.path.append('src')
try:
    from automem.config import AutoMemConfig
    print('✅ 配置模块导入成功')
except ImportError as e:
    print(f'❌ 配置模块导入失败: {e}')
    sys.exit(1)
"
    
    log_success "安装测试通过"
}

# 显示完成信息
show_completion() {
    log_success "AutoMem 安装完成！"
    echo
    echo "🚀 快速开始："
    echo "  1. 启动服务器: ./automem serve"
    echo "  2. 查看状态: ./automem status"
    echo "  3. 查看帮助: ./automem --help"
    echo
    echo "📚 更多信息："
    echo "  - 配置文件: config.yaml"
    echo "  - 数据目录: data/"
    echo "  - 日志文件: data/logs/"
    echo
    echo "🔧 开发模式："
    echo "  ./automem serve --dev"
    echo
}

# 主函数
main() {
    echo "🤖 AutoMem 安装程序"
    echo "===================="
    echo
    
    check_requirements
    create_venv
    install_dependencies
    init_config
    create_data_dir
    install_cli
    run_tests
    show_completion
}

# 错误处理
trap 'log_error "安装过程中发生错误"; exit 1' ERR

# 运行主函数
main "$@"
