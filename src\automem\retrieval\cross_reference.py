"""
交叉引用引擎

发现和管理记忆之间的关联关系。
"""

import asyncio
from typing import Dict, List, Set, Any, Optional, Tuple
from uuid import UUID
from datetime import datetime, timezone, timedelta
from collections import defaultdict, Counter
import itertools

from ..config.settings import RetrievalConfig
from ..utils.logging import LoggerMixin
from ..core.memory import Memory, MemoryQuery, MemoryType, MemoryImportance
from ..core.storage import StorageEngine


class MemoryRelation:
    """记忆关系"""
    
    def __init__(
        self,
        source_memory_id: UUID,
        target_memory_id: UUID,
        relation_type: str,
        strength: float,
        evidence: List[str],
        created_at: Optional[datetime] = None
    ):
        self.source_memory_id = source_memory_id
        self.target_memory_id = target_memory_id
        self.relation_type = relation_type
        self.strength = strength
        self.evidence = evidence
        self.created_at = created_at or datetime.now(timezone.utc)
    
    def __repr__(self) -> str:
        return f"MemoryRelation({self.relation_type}, {self.strength:.2f})"


class RelationshipGraph:
    """关系图"""
    
    def __init__(self):
        self.relations: Dict[UUID, List[MemoryRelation]] = defaultdict(list)
        self.reverse_relations: Dict[UUID, List[MemoryRelation]] = defaultdict(list)
    
    def add_relation(self, relation: MemoryRelation) -> None:
        """添加关系"""
        self.relations[relation.source_memory_id].append(relation)
        self.reverse_relations[relation.target_memory_id].append(relation)
    
    def get_relations(self, memory_id: UUID) -> List[MemoryRelation]:
        """获取记忆的所有关系"""
        return self.relations.get(memory_id, [])
    
    def get_reverse_relations(self, memory_id: UUID) -> List[MemoryRelation]:
        """获取指向该记忆的关系"""
        return self.reverse_relations.get(memory_id, [])
    
    def get_all_relations(self, memory_id: UUID) -> List[MemoryRelation]:
        """获取记忆的所有关系（双向）"""
        return self.get_relations(memory_id) + self.get_reverse_relations(memory_id)


class CrossReferenceEngine(LoggerMixin):
    """交叉引用引擎"""
    
    def __init__(self, config: RetrievalConfig, storage: StorageEngine):
        self.config = config
        self.storage = storage
        
        # 关系图
        self.relationship_graph = RelationshipGraph()
        
        # 关系类型定义
        self.relation_types = {
            "semantic": "语义相关",
            "temporal": "时间相关", 
            "causal": "因果关系",
            "sequential": "顺序关系",
            "topical": "主题相关",
            "contextual": "上下文相关",
            "reference": "引用关系",
            "update": "更新关系",
            "contradiction": "矛盾关系",
        }
        
        # 关系发现统计
        self.discovery_stats = {
            "total_discoveries": 0,
            "relations_found": 0,
            "relation_types": defaultdict(int),
        }
    
    async def discover_relations(
        self,
        memory: Memory,
        candidate_memories: Optional[List[Memory]] = None,
        max_candidates: int = 100
    ) -> List[MemoryRelation]:
        """发现记忆关系"""
        
        self.logger.debug(f"发现记忆关系: {memory.id}")
        
        # 更新统计
        self.discovery_stats["total_discoveries"] += 1
        
        # 获取候选记忆
        if not candidate_memories:
            candidate_memories = await self._get_candidate_memories(memory, max_candidates)
        
        if not candidate_memories:
            return []
        
        # 发现各种类型的关系
        relations = []
        
        # 语义关系
        semantic_relations = await self._discover_semantic_relations(memory, candidate_memories)
        relations.extend(semantic_relations)
        
        # 时间关系
        temporal_relations = await self._discover_temporal_relations(memory, candidate_memories)
        relations.extend(temporal_relations)
        
        # 主题关系
        topical_relations = await self._discover_topical_relations(memory, candidate_memories)
        relations.extend(topical_relations)
        
        # 上下文关系
        contextual_relations = await self._discover_contextual_relations(memory, candidate_memories)
        relations.extend(contextual_relations)
        
        # 引用关系
        reference_relations = await self._discover_reference_relations(memory, candidate_memories)
        relations.extend(reference_relations)
        
        # 过滤和排序关系
        filtered_relations = await self._filter_and_rank_relations(relations)
        
        # 更新关系图
        for relation in filtered_relations:
            self.relationship_graph.add_relation(relation)
        
        # 更新统计
        self.discovery_stats["relations_found"] += len(filtered_relations)
        for relation in filtered_relations:
            self.discovery_stats["relation_types"][relation.relation_type] += 1
        
        self.logger.debug(f"发现 {len(filtered_relations)} 个关系")
        return filtered_relations
    
    async def _get_candidate_memories(self, memory: Memory, max_candidates: int) -> List[Memory]:
        """获取候选记忆"""
        candidates = []
        
        # 1. 同一会话的记忆
        if memory.session_id:
            session_query = MemoryQuery(
                session_id=memory.session_id,
                limit=20
            )
            session_memories = await self.storage.search_memories(session_query)
            candidates.extend([m for m in session_memories if m.id != memory.id])
        
        # 2. 相似标签的记忆
        if memory.tags:
            tag_query = MemoryQuery(
                tags=list(memory.tags),
                limit=30
            )
            tag_memories = await self.storage.search_memories(tag_query)
            candidates.extend([m for m in tag_memories if m.id != memory.id])
        
        # 3. 相似时间的记忆
        time_window = timedelta(hours=24)
        time_query = MemoryQuery(
            created_after=memory.created_at - time_window,
            created_before=memory.created_at + time_window,
            limit=20
        )
        time_memories = await self.storage.search_memories(time_query)
        candidates.extend([m for m in time_memories if m.id != memory.id])
        
        # 4. 相同类型的记忆
        type_query = MemoryQuery(
            memory_types=[memory.memory_type],
            limit=20
        )
        type_memories = await self.storage.search_memories(type_query)
        candidates.extend([m for m in type_memories if m.id != memory.id])
        
        # 去重并限制数量
        unique_candidates = []
        seen_ids = set()
        for candidate in candidates:
            if candidate.id not in seen_ids:
                unique_candidates.append(candidate)
                seen_ids.add(candidate.id)
                if len(unique_candidates) >= max_candidates:
                    break
        
        return unique_candidates
    
    async def _discover_semantic_relations(
        self, 
        memory: Memory, 
        candidates: List[Memory]
    ) -> List[MemoryRelation]:
        """发现语义关系"""
        relations = []
        
        memory_words = set(memory.content.lower().split())
        
        for candidate in candidates:
            candidate_words = set(candidate.content.lower().split())
            
            # 计算词汇重叠度
            common_words = memory_words & candidate_words
            if len(common_words) >= 3:  # 至少3个共同词汇
                total_words = memory_words | candidate_words
                similarity = len(common_words) / len(total_words)
                
                if similarity >= 0.3:  # 30%相似度阈值
                    evidence = [f"共同词汇: {', '.join(list(common_words)[:3])}"]
                    
                    relation = MemoryRelation(
                        source_memory_id=memory.id,
                        target_memory_id=candidate.id,
                        relation_type="semantic",
                        strength=similarity,
                        evidence=evidence
                    )
                    relations.append(relation)
        
        return relations
    
    async def _discover_temporal_relations(
        self, 
        memory: Memory, 
        candidates: List[Memory]
    ) -> List[MemoryRelation]:
        """发现时间关系"""
        relations = []
        
        for candidate in candidates:
            time_diff = abs((memory.created_at - candidate.created_at).total_seconds())
            
            # 时间接近的记忆
            if time_diff <= 3600:  # 1小时内
                strength = 1.0 - (time_diff / 3600)
                evidence = ["时间接近"]
                
                relation = MemoryRelation(
                    source_memory_id=memory.id,
                    target_memory_id=candidate.id,
                    relation_type="temporal",
                    strength=strength,
                    evidence=evidence
                )
                relations.append(relation)
            
            # 顺序关系（同一会话内）
            elif (memory.session_id and 
                  memory.session_id == candidate.session_id and
                  time_diff <= 86400):  # 24小时内
                
                if memory.created_at > candidate.created_at:
                    relation_type = "sequential"
                    evidence = ["会话顺序"]
                    strength = 0.7
                    
                    relation = MemoryRelation(
                        source_memory_id=candidate.id,  # 较早的记忆指向较晚的
                        target_memory_id=memory.id,
                        relation_type=relation_type,
                        strength=strength,
                        evidence=evidence
                    )
                    relations.append(relation)
        
        return relations
    
    async def _discover_topical_relations(
        self, 
        memory: Memory, 
        candidates: List[Memory]
    ) -> List[MemoryRelation]:
        """发现主题关系"""
        relations = []
        
        if not memory.tags:
            return relations
        
        for candidate in candidates:
            if not candidate.tags:
                continue
            
            # 计算标签重叠
            common_tags = memory.tags & candidate.tags
            if common_tags:
                tag_similarity = len(common_tags) / len(memory.tags | candidate.tags)
                
                if tag_similarity >= 0.3:  # 30%标签重叠
                    evidence = [f"共同标签: {', '.join(list(common_tags)[:3])}"]
                    
                    relation = MemoryRelation(
                        source_memory_id=memory.id,
                        target_memory_id=candidate.id,
                        relation_type="topical",
                        strength=tag_similarity,
                        evidence=evidence
                    )
                    relations.append(relation)
        
        return relations
    
    async def _discover_contextual_relations(
        self, 
        memory: Memory, 
        candidates: List[Memory]
    ) -> List[MemoryRelation]:
        """发现上下文关系"""
        relations = []
        
        for candidate in candidates:
            strength = 0.0
            evidence = []
            
            # 同一会话
            if memory.session_id and memory.session_id == candidate.session_id:
                strength += 0.5
                evidence.append("同一会话")
            
            # 同一对话
            if memory.conversation_id and memory.conversation_id == candidate.conversation_id:
                strength += 0.3
                evidence.append("同一对话")
            
            # 相同分类
            if memory.categories & candidate.categories:
                common_categories = memory.categories & candidate.categories
                strength += 0.2
                evidence.append(f"相同分类: {', '.join(list(common_categories)[:2])}")
            
            if strength >= 0.4:  # 40%上下文相关性阈值
                relation = MemoryRelation(
                    source_memory_id=memory.id,
                    target_memory_id=candidate.id,
                    relation_type="contextual",
                    strength=strength,
                    evidence=evidence
                )
                relations.append(relation)
        
        return relations
    
    async def _discover_reference_relations(
        self, 
        memory: Memory, 
        candidates: List[Memory]
    ) -> List[MemoryRelation]:
        """发现引用关系"""
        relations = []
        
        # 检查是否有明确的引用模式
        memory_content = memory.content.lower()
        
        for candidate in candidates:
            # 检查是否引用了候选记忆的内容
            candidate_words = candidate.content.lower().split()
            
            # 寻找引用模式
            reference_patterns = [
                "参考", "引用", "提到", "如前所述", "之前说过",
                "reference", "refer", "mentioned", "as said", "previously"
            ]
            
            has_reference = any(pattern in memory_content for pattern in reference_patterns)
            
            if has_reference:
                # 检查是否有内容重叠
                memory_words = set(memory_content.split())
                candidate_words_set = set(candidate_words)
                
                overlap = memory_words & candidate_words_set
                if len(overlap) >= 2:
                    strength = min(len(overlap) / 10.0, 1.0)
                    evidence = ["明确引用", f"重叠内容: {len(overlap)}个词"]
                    
                    relation = MemoryRelation(
                        source_memory_id=memory.id,
                        target_memory_id=candidate.id,
                        relation_type="reference",
                        strength=strength,
                        evidence=evidence
                    )
                    relations.append(relation)
        
        return relations
    
    async def _filter_and_rank_relations(
        self, 
        relations: List[MemoryRelation]
    ) -> List[MemoryRelation]:
        """过滤和排序关系"""
        if not relations:
            return []
        
        # 过滤低强度关系
        filtered = [r for r in relations if r.strength >= 0.3]
        
        # 去重（相同的记忆对只保留最强的关系）
        relation_map = {}
        for relation in filtered:
            key = (relation.source_memory_id, relation.target_memory_id)
            if key not in relation_map or relation.strength > relation_map[key].strength:
                relation_map[key] = relation
        
        # 按强度排序
        final_relations = list(relation_map.values())
        final_relations.sort(key=lambda r: r.strength, reverse=True)
        
        # 限制数量
        return final_relations[:20]
    
    async def get_memory_relations(self, memory_id: UUID) -> List[MemoryRelation]:
        """获取记忆的所有关系"""
        return self.relationship_graph.get_all_relations(memory_id)
    
    async def find_relation_paths(
        self, 
        source_id: UUID, 
        target_id: UUID, 
        max_depth: int = 3
    ) -> List[List[MemoryRelation]]:
        """查找记忆间的关系路径"""
        paths = []
        visited = set()
        
        def dfs(current_id: UUID, path: List[MemoryRelation], depth: int):
            if depth > max_depth:
                return
            
            if current_id == target_id and path:
                paths.append(path.copy())
                return
            
            if current_id in visited:
                return
            
            visited.add(current_id)
            
            # 探索所有出边
            for relation in self.relationship_graph.get_relations(current_id):
                if relation.target_memory_id not in visited:
                    path.append(relation)
                    dfs(relation.target_memory_id, path, depth + 1)
                    path.pop()
            
            visited.remove(current_id)
        
        dfs(source_id, [], 0)
        return paths
    
    async def get_discovery_stats(self) -> Dict[str, Any]:
        """获取发现统计信息"""
        return {
            "total_discoveries": self.discovery_stats["total_discoveries"],
            "relations_found": self.discovery_stats["relations_found"],
            "relation_types": dict(self.discovery_stats["relation_types"]),
            "average_relations_per_discovery": (
                self.discovery_stats["relations_found"] / 
                max(self.discovery_stats["total_discoveries"], 1)
            ),
            "total_relations_in_graph": sum(
                len(relations) for relations in self.relationship_graph.relations.values()
            ),
        }
    
    async def cleanup_weak_relations(self, min_strength: float = 0.3) -> int:
        """清理弱关系"""
        removed_count = 0
        
        for memory_id in list(self.relationship_graph.relations.keys()):
            relations = self.relationship_graph.relations[memory_id]
            strong_relations = [r for r in relations if r.strength >= min_strength]
            
            removed_count += len(relations) - len(strong_relations)
            self.relationship_graph.relations[memory_id] = strong_relations
        
        # 同样清理反向关系
        for memory_id in list(self.relationship_graph.reverse_relations.keys()):
            relations = self.relationship_graph.reverse_relations[memory_id]
            strong_relations = [r for r in relations if r.strength >= min_strength]
            self.relationship_graph.reverse_relations[memory_id] = strong_relations
        
        self.logger.info(f"清理了 {removed_count} 个弱关系")
        return removed_count
