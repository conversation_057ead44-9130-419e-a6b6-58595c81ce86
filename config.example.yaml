# AutoMem 配置文件示例
# 复制此文件为 config.yaml 并根据需要修改

# 基本设置
server_name: "AutoMem"
server_version: "0.1.0"
debug: false

# MCP传输设置
mcp_transport: "stdio"  # 可选: "stdio", "sse", "http"
mcp_host: "localhost"
mcp_port: 8000

# 存储配置
storage:
  data_dir: "./data"           # 数据存储目录
  max_memories: 10000          # 最大记忆数量
  cleanup_interval: "24h"      # 清理间隔 (支持: 1h, 24h, 7d等)
  backup_enabled: true         # 是否启用备份
  backup_interval: "7d"        # 备份间隔

# 智能层配置
intelligence:
  embedding_model: "all-MiniLM-L6-v2"  # 嵌入模型
  classification_threshold: 0.7         # 分类阈值 (0-1)
  auto_tag_enabled: true               # 自动标签生成
  max_tags_per_memory: 5               # 每个记忆最大标签数
  importance_threshold: 0.5            # 重要性阈值 (0-1)
  language_detection: true             # 语言检测
  supported_languages:                 # 支持的语言
    - "zh"  # 中文
    - "en"  # 英文

# 检索配置
retrieval:
  max_results: 10                    # 最大检索结果数
  similarity_threshold: 0.6          # 相似度阈值 (0-1)
  time_decay_factor: 0.1            # 时间衰减因子
  context_window_size: 5            # 上下文窗口大小
  enable_cross_reference: true      # 启用交叉引用
  semantic_search_enabled: true     # 启用语义搜索

# 日志配置
logging:
  level: "INFO"                     # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
  file: "./logs/automem.log"        # 日志文件路径
  max_file_size: "10MB"            # 最大日志文件大小
  backup_count: 5                  # 日志备份文件数量
  enable_console: true             # 启用控制台输出
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 实验性功能 (可选)
experimental_features:
  # 高级记忆合并
  advanced_memory_merging: false
  
  # 情感分析
  emotion_analysis: false
  
  # 多模态支持
  multimodal_support: false
  
  # 分布式存储
  distributed_storage: false
