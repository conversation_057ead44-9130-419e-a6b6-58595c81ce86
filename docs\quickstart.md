# 快速开始

本指南将帮助您在 5 分钟内快速上手 AutoMem 智能记忆管理系统。

## 🚀 安装

### 方法一：自动安装脚本

```bash
# Linux/macOS
curl -sSL https://raw.githubusercontent.com/your-org/automem/main/scripts/install.sh | bash

# Windows (PowerShell)
iwr -useb https://raw.githubusercontent.com/your-org/automem/main/scripts/install.ps1 | iex
```

### 方法二：手动安装

```bash
# 克隆仓库
git clone https://github.com/your-org/automem.git
cd automem

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 初始化配置
cp config.example.yaml config.yaml
```

## ⚙️ 基本配置

编辑 `config.yaml` 文件：

```yaml
# 基本配置
server_name: "我的AutoMem"
debug: false

# 存储配置
storage:
  data_dir: "./data"
  max_memories: 10000

# 智能处理
intelligence:
  embedding_model: "all-MiniLM-L6-v2"
  auto_classification: true
  auto_tag_enabled: true

# 日志配置
logging:
  level: "INFO"
  enable_console: true
```

## 🎯 启动服务

### 启动 MCP 服务器

```bash
# 使用 stdio 传输（推荐用于 MCP 客户端）
./automem serve --stdio

# 使用 HTTP 传输（用于测试和调试）
./automem serve --host localhost --port 8000
```

### 验证安装

```bash
# 检查系统状态
./automem status

# 查看版本信息
./automem version

# 运行测试
./automem test
```

## 📝 基本使用

### 1. 存储记忆

通过 MCP 客户端调用工具：

```json
{
  "method": "tools/call",
  "params": {
    "name": "store_memory",
    "arguments": {
      "content": "Python是一种高级编程语言，广泛用于Web开发、数据科学和AI。",
      "memory_type": "fact",
      "importance": "high",
      "tags": ["Python", "编程", "技术"],
      "auto_process": true
    }
  }
}
```

### 2. 搜索记忆

```json
{
  "method": "tools/call",
  "params": {
    "name": "search_memories",
    "arguments": {
      "query": "Python编程语言",
      "limit": 5,
      "similarity_threshold": 0.6
    }
  }
}
```

### 3. 获取上下文

```json
{
  "method": "tools/call",
  "params": {
    "name": "get_context",
    "arguments": {
      "topic": "机器学习",
      "session_id": "my_session",
      "limit": 3
    }
  }
}
```

## 🔧 MCP 客户端集成

### Claude Desktop 集成

在 Claude Desktop 的配置文件中添加：

```json
{
  "mcpServers": {
    "automem": {
      "command": "/path/to/automem/automem",
      "args": ["serve", "--stdio"],
      "env": {
        "AUTOMEM_CONFIG": "/path/to/config.yaml"
      }
    }
  }
}
```

### 其他 MCP 客户端

AutoMem 兼容所有标准的 MCP 客户端。请参考您的客户端文档了解具体的集成方法。

## 📊 查看资源

AutoMem 提供多种资源供 MCP 客户端访问：

- `memory://recent` - 最近的记忆
- `memory://context/{topic}` - 特定主题的上下文
- `memory://timeline/{date}` - 特定日期的记忆
- `memory://tags/{tag}` - 特定标签的记忆
- `memory://summary` - 系统摘要

## 🎨 使用提示符

AutoMem 提供智能提示符帮助您更好地利用记忆：

### 回忆上下文

```
请回忆与"机器学习"相关的上下文信息，深度分析，关注技术细节。
```

### 总结记忆

```
请总结本周的记忆，重点关注工作方面的内容。
```

### 发现连接

```
请分析"深度学习"与已有记忆之间的潜在连接。
```

## 🔍 监控和管理

### 查看统计信息

```bash
# 系统状态
./automem status --detailed

# 内存使用情况
./automem stats
```

### 备份和恢复

```bash
# 创建备份
./scripts/backup.sh

# 恢复备份
./scripts/restore.sh backup_file.tar.gz
```

### 清理维护

```bash
# 预览清理
./automem cleanup --dry-run --days 30

# 执行清理
./automem cleanup --days 30
```

## 🆘 常见问题

### Q: 服务启动失败怎么办？

A: 检查以下几点：
1. Python 版本是否 >= 3.8
2. 依赖是否完整安装
3. 配置文件是否正确
4. 数据目录权限是否正确

```bash
# 诊断命令
./automem test
./automem status --detailed
```

### Q: 搜索结果不准确？

A: 可以调整以下参数：
- 降低 `similarity_threshold`
- 增加 `max_results`
- 检查嵌入模型配置

### Q: 内存使用过高？

A: 尝试以下优化：
- 减少 `max_memories` 限制
- 启用自动清理
- 调整缓存大小

## 📚 下一步

- 阅读 [配置指南](configuration.md) 了解详细配置
- 查看 [最佳实践](best-practices.md) 获取使用建议
- 探索 [API 参考](api-reference.md) 了解高级功能
- 参与 [社区讨论](https://github.com/your-org/automem/discussions)

## 💡 提示

- 使用有意义的标签和分类来组织记忆
- 定期备份重要数据
- 监控系统性能和资源使用
- 参与社区分享经验和最佳实践

祝您使用愉快！🎉
