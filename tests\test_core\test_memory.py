"""
测试核心记忆模块
"""

import pytest
from datetime import datetime, timezone
from uuid import UUID

from src.automem.core.memory import (
    Memory, MemoryType, MemoryImportance, MemoryQuery,
    MemorySearchResult, MemoryMetadata
)


class TestMemory:
    """测试Memory类"""
    
    def test_memory_creation(self):
        """测试记忆创建"""
        content = "这是一个测试记忆"
        memory = Memory(
            content=content,
            memory_type=MemoryType.CONVERSATION,
            importance=MemoryImportance.MEDIUM
        )
        
        assert memory.content == content
        assert memory.memory_type == MemoryType.CONVERSATION
        assert memory.importance == MemoryImportance.MEDIUM
        assert isinstance(memory.id, UUID)
        assert isinstance(memory.created_at, datetime)
        assert memory.created_at.tzinfo == timezone.utc
        assert memory.updated_at == memory.created_at
        assert memory.access_count == 0
        assert memory.tags == set()
        assert memory.categories == set()
    
    def test_memory_with_metadata(self):
        """测试带元数据的记忆创建"""
        memory = Memory(
            content="测试内容",
            memory_type=MemoryType.FACT,
            importance=MemoryImportance.HIGH,
            tags={"标签1", "标签2"},
            categories={"分类1"},
            session_id="session_123",
            conversation_id="conv_456",
            source="test_source"
        )
        
        assert memory.tags == {"标签1", "标签2"}
        assert memory.categories == {"分类1"}
        assert memory.session_id == "session_123"
        assert memory.conversation_id == "conv_456"
        assert memory.source == "test_source"
    
    def test_memory_update_content(self):
        """测试记忆内容更新"""
        memory = Memory(
            content="原始内容",
            memory_type=MemoryType.CONVERSATION,
            importance=MemoryImportance.MEDIUM
        )
        
        original_updated_at = memory.updated_at
        
        # 模拟时间流逝
        import time
        time.sleep(0.01)
        
        memory.update_content("更新后的内容")
        
        assert memory.content == "更新后的内容"
        assert memory.updated_at > original_updated_at
    
    def test_memory_access_tracking(self):
        """测试记忆访问跟踪"""
        memory = Memory(
            content="测试内容",
            memory_type=MemoryType.CONVERSATION,
            importance=MemoryImportance.MEDIUM
        )
        
        assert memory.access_count == 0
        assert memory.last_accessed_at is None
        
        memory.mark_accessed()
        
        assert memory.access_count == 1
        assert memory.last_accessed_at is not None
        
        memory.mark_accessed()
        assert memory.access_count == 2
    
    def test_memory_tag_management(self):
        """测试标签管理"""
        memory = Memory(
            content="测试内容",
            memory_type=MemoryType.CONVERSATION,
            importance=MemoryImportance.MEDIUM
        )
        
        # 添加标签
        memory.add_tag("新标签")
        assert "新标签" in memory.tags
        
        # 添加多个标签
        memory.add_tags(["标签1", "标签2"])
        assert "标签1" in memory.tags
        assert "标签2" in memory.tags
        
        # 移除标签
        memory.remove_tag("标签1")
        assert "标签1" not in memory.tags
        assert "标签2" in memory.tags
        
        # 清空标签
        memory.clear_tags()
        assert len(memory.tags) == 0
    
    def test_memory_category_management(self):
        """测试分类管理"""
        memory = Memory(
            content="测试内容",
            memory_type=MemoryType.CONVERSATION,
            importance=MemoryImportance.MEDIUM
        )
        
        # 添加分类
        memory.add_category("新分类")
        assert "新分类" in memory.categories
        
        # 添加多个分类
        memory.add_categories(["分类1", "分类2"])
        assert "分类1" in memory.categories
        assert "分类2" in memory.categories
        
        # 移除分类
        memory.remove_category("分类1")
        assert "分类1" not in memory.categories
        assert "分类2" in memory.categories
    
    def test_memory_age_calculation(self):
        """测试记忆年龄计算"""
        memory = Memory(
            content="测试内容",
            memory_type=MemoryType.CONVERSATION,
            importance=MemoryImportance.MEDIUM
        )
        
        age_seconds = memory.age_in_seconds()
        assert age_seconds >= 0
        assert age_seconds < 1  # 应该很小
        
        age_days = memory.age_in_days()
        assert age_days >= 0
        assert age_days < 1
    
    def test_memory_summary_generation(self):
        """测试记忆摘要生成"""
        long_content = "这是一个很长的记忆内容。" * 20
        memory = Memory(
            content=long_content,
            memory_type=MemoryType.CONVERSATION,
            importance=MemoryImportance.MEDIUM
        )
        
        summary = memory.generate_summary(max_length=50)
        assert len(summary) <= 50
        assert summary.endswith("...")
    
    def test_memory_serialization(self):
        """测试记忆序列化"""
        memory = Memory(
            content="测试内容",
            memory_type=MemoryType.FACT,
            importance=MemoryImportance.HIGH,
            tags={"标签1", "标签2"},
            categories={"分类1"},
            session_id="session_123"
        )
        
        # 转换为字典
        memory_dict = memory.to_dict()
        assert memory_dict["content"] == "测试内容"
        assert memory_dict["memory_type"] == "fact"
        assert memory_dict["importance"] == "high"
        assert set(memory_dict["tags"]) == {"标签1", "标签2"}
        
        # 从字典创建
        new_memory = Memory.from_dict(memory_dict)
        assert new_memory.content == memory.content
        assert new_memory.memory_type == memory.memory_type
        assert new_memory.importance == memory.importance
        assert new_memory.tags == memory.tags


class TestMemoryQuery:
    """测试MemoryQuery类"""
    
    def test_basic_query(self):
        """测试基本查询"""
        query = MemoryQuery(
            content="测试查询",
            limit=10
        )
        
        assert query.content == "测试查询"
        assert query.limit == 10
        assert query.memory_types is None
        assert query.importance_levels is None
    
    def test_filtered_query(self):
        """测试过滤查询"""
        query = MemoryQuery(
            content="测试查询",
            memory_types=[MemoryType.CONVERSATION, MemoryType.FACT],
            importance_levels=[MemoryImportance.HIGH],
            tags=["重要", "测试"],
            categories=["技术"],
            session_id="session_123",
            limit=20
        )
        
        assert len(query.memory_types) == 2
        assert MemoryType.CONVERSATION in query.memory_types
        assert MemoryType.FACT in query.memory_types
        assert query.importance_levels == [MemoryImportance.HIGH]
        assert query.tags == ["重要", "测试"]
        assert query.categories == ["技术"]
        assert query.session_id == "session_123"
        assert query.limit == 20
    
    def test_time_range_query(self):
        """测试时间范围查询"""
        from datetime import timedelta
        
        now = datetime.now(timezone.utc)
        start_time = now - timedelta(days=7)
        end_time = now
        
        query = MemoryQuery(
            created_after=start_time,
            created_before=end_time,
            updated_after=start_time
        )
        
        assert query.created_after == start_time
        assert query.created_before == end_time
        assert query.updated_after == start_time


class TestMemorySearchResult:
    """测试MemorySearchResult类"""
    
    def test_search_result_creation(self, sample_memory):
        """测试搜索结果创建"""
        result = MemorySearchResult(
            memory=sample_memory,
            similarity_score=0.85,
            relevance_score=0.90,
            match_reasons=["内容匹配", "标签匹配"]
        )
        
        assert result.memory == sample_memory
        assert result.similarity_score == 0.85
        assert result.relevance_score == 0.90
        assert result.match_reasons == ["内容匹配", "标签匹配"]
        assert abs(result.combined_score - 0.875) < 0.001  # (0.85 + 0.90) / 2
    
    def test_search_result_sorting(self, sample_memories):
        """测试搜索结果排序"""
        results = []
        
        for i, memory in enumerate(sample_memories[:3]):
            result = MemorySearchResult(
                memory=memory,
                similarity_score=0.5 + i * 0.1,
                relevance_score=0.6 + i * 0.1
            )
            results.append(result)
        
        # 按综合得分排序
        sorted_results = sorted(results, key=lambda r: r.combined_score, reverse=True)
        
        assert sorted_results[0].combined_score >= sorted_results[1].combined_score
        assert sorted_results[1].combined_score >= sorted_results[2].combined_score


class TestMemoryMetadata:
    """测试MemoryMetadata类"""
    
    def test_metadata_creation(self):
        """测试元数据创建"""
        metadata = MemoryMetadata(
            source="test_source",
            confidence=0.95,
            processing_time=0.123,
            model_version="v1.0"
        )
        
        assert metadata.source == "test_source"
        assert metadata.confidence == 0.95
        assert metadata.processing_time == 0.123
        assert metadata.model_version == "v1.0"
    
    def test_metadata_update(self):
        """测试元数据更新"""
        metadata = MemoryMetadata()
        
        metadata.update({
            "custom_field": "custom_value",
            "score": 0.88
        })
        
        assert metadata.get("custom_field") == "custom_value"
        assert metadata.get("score") == 0.88
