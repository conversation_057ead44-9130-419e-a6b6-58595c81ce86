# AutoMem Windows 安装脚本
# PowerShell 脚本

param(
    [switch]$SkipVenv,
    [switch]$Dev,
    [string]$PythonPath = "python"
)

# 颜色函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Blue "[INFO] $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "[SUCCESS] $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "[WARNING] $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "[ERROR] $message"
}

# 检查系统要求
function Test-Requirements {
    Write-Info "检查系统要求..."
    
    # 检查Python
    try {
        $pythonVersion = & $PythonPath --version 2>&1
        if ($LASTEXITCODE -ne 0) {
            throw "Python 未找到"
        }
        
        $version = $pythonVersion -replace "Python ", ""
        $versionParts = $version.Split(".")
        $major = [int]$versionParts[0]
        $minor = [int]$versionParts[1]
        
        if ($major -lt 3 -or ($major -eq 3 -and $minor -lt 8)) {
            throw "Python 版本需要 >= 3.8，当前版本: $version"
        }
        
        Write-Success "Python 版本检查通过: $version"
    }
    catch {
        Write-Error $_.Exception.Message
        exit 1
    }
    
    # 检查pip
    try {
        & $PythonPath -m pip --version | Out-Null
        if ($LASTEXITCODE -ne 0) {
            throw "pip 未安装"
        }
    }
    catch {
        Write-Error "pip 未安装或无法访问"
        exit 1
    }
}

# 创建虚拟环境
function New-VirtualEnvironment {
    if ($SkipVenv) {
        Write-Info "跳过虚拟环境创建"
        return
    }
    
    Write-Info "创建Python虚拟环境..."
    
    if (!(Test-Path "venv")) {
        & $PythonPath -m venv venv
        if ($LASTEXITCODE -ne 0) {
            Write-Error "虚拟环境创建失败"
            exit 1
        }
        Write-Success "虚拟环境创建完成"
    } else {
        Write-Info "虚拟环境已存在"
    }
    
    # 激活虚拟环境
    & "venv\Scripts\Activate.ps1"
    
    # 升级pip
    python -m pip install --upgrade pip
}

# 安装依赖
function Install-Dependencies {
    Write-Info "安装Python依赖包..."
    
    if (Test-Path "requirements.txt") {
        python -m pip install -r requirements.txt
        if ($LASTEXITCODE -ne 0) {
            Write-Error "依赖包安装失败"
            exit 1
        }
    } else {
        Write-Warning "requirements.txt 文件不存在，跳过依赖安装"
    }
    
    Write-Success "依赖包安装完成"
}

# 初始化配置
function Initialize-Config {
    Write-Info "初始化配置文件..."
    
    if (!(Test-Path "config.yaml")) {
        if (Test-Path "config.example.yaml") {
            Copy-Item "config.example.yaml" "config.yaml"
            Write-Success "配置文件已创建: config.yaml"
        } else {
            Write-Warning "示例配置文件不存在，将使用默认配置"
        }
    } else {
        Write-Info "配置文件已存在"
    }
}

# 创建数据目录
function New-DataDirectory {
    Write-Info "创建数据目录..."
    
    $dataDir = "data"
    if (!(Test-Path $dataDir)) {
        New-Item -ItemType Directory -Path $dataDir -Force | Out-Null
        New-Item -ItemType Directory -Path "$dataDir\logs" -Force | Out-Null
        Write-Success "数据目录创建完成: $dataDir"
    } else {
        Write-Info "数据目录已存在"
    }
}

# 安装CLI工具
function Install-CLI {
    Write-Info "安装CLI工具..."
    
    # 创建批处理文件
    $batchContent = @"
@echo off
cd /d "%~dp0"
if exist venv\Scripts\activate.bat (
    call venv\Scripts\activate.bat
)
python -m src.automem.cli %*
"@
    
    $batchContent | Out-File -FilePath "automem.bat" -Encoding ASCII
    
    # 创建PowerShell脚本
    $psContent = @"
`$scriptDir = Split-Path -Parent `$MyInvocation.MyCommand.Path
Set-Location `$scriptDir
if (Test-Path "venv\Scripts\Activate.ps1") {
    & "venv\Scripts\Activate.ps1"
}
python -m src.automem.cli `$args
"@
    
    $psContent | Out-File -FilePath "automem.ps1" -Encoding UTF8
    
    Write-Success "CLI工具安装完成"
    Write-Info "可以使用 .\automem.bat 或 .\automem.ps1 运行AutoMem"
}

# 运行测试
function Test-Installation {
    Write-Info "运行安装测试..."
    
    # 测试导入
    $testScript = @"
import sys
sys.path.append('src')
try:
    from automem.config import AutoMemConfig
    print('✅ 配置模块导入成功')
except ImportError as e:
    print(f'❌ 配置模块导入失败: {e}')
    sys.exit(1)
"@
    
    $testScript | python
    if ($LASTEXITCODE -ne 0) {
        Write-Error "安装测试失败"
        exit 1
    }
    
    Write-Success "安装测试通过"
}

# 显示完成信息
function Show-Completion {
    Write-Success "AutoMem 安装完成！"
    Write-Host ""
    Write-Host "🚀 快速开始："
    Write-Host "  1. 启动服务器: .\automem.bat serve"
    Write-Host "  2. 查看状态: .\automem.bat status"
    Write-Host "  3. 查看帮助: .\automem.bat --help"
    Write-Host ""
    Write-Host "📚 更多信息："
    Write-Host "  - 配置文件: config.yaml"
    Write-Host "  - 数据目录: data\"
    Write-Host "  - 日志文件: data\logs\"
    Write-Host ""
    Write-Host "🔧 开发模式："
    Write-Host "  .\automem.bat serve --dev"
    Write-Host ""
    
    if ($Dev) {
        Write-Info "开发模式已启用"
    }
}

# 主函数
function Main {
    Write-Host "🤖 AutoMem Windows 安装程序"
    Write-Host "============================="
    Write-Host ""
    
    try {
        Test-Requirements
        New-VirtualEnvironment
        Install-Dependencies
        Initialize-Config
        New-DataDirectory
        Install-CLI
        Test-Installation
        Show-Completion
    }
    catch {
        Write-Error "安装过程中发生错误: $($_.Exception.Message)"
        exit 1
    }
}

# 运行主函数
Main
