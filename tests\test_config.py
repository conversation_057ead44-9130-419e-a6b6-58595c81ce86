"""
配置模块测试
"""

import pytest
from pathlib import Path
from tempfile import TemporaryDirectory

from automem.config import AutoMemConfig, StorageConfig, IntelligenceConfig


class TestStorageConfig:
    """存储配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = StorageConfig()
        
        assert config.data_dir == Path("./data")
        assert config.max_memories == 10000
        assert config.cleanup_interval == "24h"
        assert config.backup_enabled is True
    
    def test_data_dir_creation(self):
        """测试数据目录创建"""
        with TemporaryDirectory() as temp_dir:
            data_dir = Path(temp_dir) / "test_data"
            config = StorageConfig(data_dir=data_dir)
            
            assert config.data_dir.exists()
            assert config.data_dir.is_dir()


class TestIntelligenceConfig:
    """智能配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = IntelligenceConfig()
        
        assert config.embedding_model == "all-MiniLM-L6-v2"
        assert config.classification_threshold == 0.7
        assert config.auto_tag_enabled is True
        assert config.max_tags_per_memory == 5
    
    def test_threshold_validation(self):
        """测试阈值验证"""
        # 有效阈值
        config = IntelligenceConfig(classification_threshold=0.5)
        assert config.classification_threshold == 0.5
        
        # 无效阈值
        with pytest.raises(ValueError):
            IntelligenceConfig(classification_threshold=1.5)
        
        with pytest.raises(ValueError):
            IntelligenceConfig(classification_threshold=-0.1)


class TestAutoMemConfig:
    """主配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = AutoMemConfig()
        
        assert config.server_name == "AutoMem"
        assert config.server_version == "0.1.0"
        assert config.debug is False
        assert config.mcp_transport == "stdio"
    
    def test_nested_configs(self):
        """测试嵌套配置"""
        config = AutoMemConfig()
        
        assert isinstance(config.storage, StorageConfig)
        assert isinstance(config.intelligence, IntelligenceConfig)
        assert config.storage.max_memories == 10000
        assert config.intelligence.auto_tag_enabled is True
    
    def test_config_file_operations(self):
        """测试配置文件操作"""
        with TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "test_config.yaml"
            
            # 创建配置
            original_config = AutoMemConfig(
                server_name="TestServer",
                debug=True,
            )
            
            # 保存配置
            original_config.save_to_file(config_path)
            assert config_path.exists()
            
            # 加载配置
            loaded_config = AutoMemConfig.load_from_file(config_path)
            assert loaded_config.server_name == "TestServer"
            assert loaded_config.debug is True
